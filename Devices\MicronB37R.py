import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
# from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
# from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.Micron import CMicron as CMicron

###########################################################################
### Reference Datasheet: B37R_Fortis_512Gb_1Tb_2Tb_NAND_Datasheet_20191009.pdf
###########################################################################


class CMicronB37R(CMicron):
    def __init__(self):
        CMicron.__init__(self)
        self.DEVICE_MANUFACTURER = "Micron"
        self.DEVICE_NAME = "B37R"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 1  # B37R has one CE per channel # nanocycler.CE_NUM
        self.DEVICE_ID_LEN = 4

        self.PAGE_LENGTH = 18344
        self.CHUNK_NUMBER = 1
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 4  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.PAGE_NUMBER = 1536  # per block

        self.LUN_START_BIT_ADDRESS = 23
        self.BLOCK_START_BIT_ADDRESS = 11
        self.VALID_LUN_MASK = 0x01
        self.VALID_PAGE_MASK = 0x7FF
        self.VALID_BLOCK_MASK = 0xFFF

        self.RR_OPTIONS = 15


    def micron_set_row_address(self, opcbuilder, row_address):
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

    def micron_set_row_address_reg(self, opcbuilder):
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 4, is stored as byte 4 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 5, is stored as byte 5 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 6, is stored as byte 6 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 7) # row address byte 7, is stored as byte 7 of the ram


    def die_configure(self, ch_list: [], ce_list: [], lODT = 4, lDriverStrength = 4):

        self.device_reset(ch_list, ce_list)

        # driver strength
        # 2: 37.5 Ohm (default)
        # 3: 50 Ohm
        # 4: 37.5 Ohm
        p1 = lDriverStrength
        self.set_feature(ch_list, ce_list, 0x10, p1, 0x00, 0x00, 0x00)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        bODT = lODT
        bRE_C = 1
        bDQS_c = 1
        bVREF = 1
        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)

        p1 = (bODT << 4) | (bRE_C << 2) | (bDQS_c << 1) | (bVREF << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        p3 = 0
        p4 = 0

        self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4)



    def offset_to_voltage(self, offset):
        offset_voltage = -1280 + offset * 10

        return offset_voltage

