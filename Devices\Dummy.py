import nanocycler

from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
from nanocycler import datalog as datalog
# from nanocycler import utility as utility
# from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

############################################################
### Dummy Device class
############################################################

class CDummy(COnfiDevice):
    def __init__(self):
        COnfiDevice.__init__(self)
        return

    def turn_on(self, vcc: float, vccq: float):
        COnfiDevice.turn_on(self, vcc, vccq)
        return

    def turn_off(self):
        return

    def device_id_read(self, ch_list: [], ce, address = 0, id_len = 8):
        return

    def page_parameter_read(self, ch_list: [], ce, address = 0):
        return

    def zq_calib_long(self, ch_list: [], ce_list: [], lun: int):
        return

    def zq_calib_short(self, ch_list: [], ce_list: [], lun: int):
        return

    def device_reset(self, ch_list: [], ce_list: [], reset_cmd=ONFI_CMD.RESET_CMD_FFh):
        ws.info("[device_reset] reset_cmd: {0}".format(hex(reset_cmd)))
        self.device_reset(ch_list, ce_list, reset_cmd)
        return

    def set_feature(self, ch_list: [], ce_list: [], address, p1, p2, p3, p4):
        return

    def set_feature_by_lun(self, ch_list: [], ce_list: [], lun, address, p1, p2, p3, p4):
        return

    def set_feature_async(self, ch_list: [], ce_list: [], address, p1, p2, p3, p4):
        return

    def set_feature_by_lun_async(self, ch_list: [], ce_list: [], lun, address, p1, p2, p3, p4):
        return

    def get_feature(self, ch_list: [], ce, address):
        return

    def get_feature_by_lun(self, ch_list: [], ce, lun, address):
        return

    def get_status_enhanced_71h(self, ch_list: [], ce, lun):
        return

    def get_status_enhanced_78h(self, ch_list: [], ce, lun, block):
        return

    def get_status(self, ch_list: [], ce):
        return

    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list: []):
        for page in page_list:
            self.program_page(ch_list, ce_list, lun_list, block, page, 0)

    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):
        return

    def die_configure(self, ch_list: [], ce_list: [], odt = 0, driver_strength = 0):
        return False

    def identification(self):
        return True

    def calib_setup(self, data_rate_mhz):
        return True

    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):
        self.dummy_log("ERS", ch_list, ce_list, lun_list, [block], 0, 0, 0)
        return True

    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):
        return True

    def erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay):
        return True

    def erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay = -1):
        return True

    def multi_plane_erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay):
        return True

    def multi_plane_erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay = -1):
        return True

    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):
        self.dummy_log("PRG", ch_list, ce_list, lun_list, [block], page, 0, 0)
        return True

    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):
        return True

    def program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay):
        return True

    def program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay = -1):
        return True

    def multi_plane_program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay):
        return True

    def multi_plane_program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay = -1):
        return True

    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        buffer = bytearray(page)
        for i in range(page_length):
            buffer[i] = 0
        return True, buffer

    def dummy_log(self, function, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column, page_length):
        datalog.set_function(function)
        for ch in ch_list:
            datalog.set_channel(ch)
            datalog.add_data("RnB", "0.000")
            datalog.add_data("Fails", "0")
            datalog.add_data("PmuAvg", "0.0")
            datalog.add_data("PmuMax", "0.0")

    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        self.dummy_log("READ", ch_list, [ce], [lun], [block], page, column, page_length)
        return

    def page_fast_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page compare
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list : [], page, column, page_length):
        return True

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        return True

    def reset_read_retry_option(self, ch_list: [], ce, lun):
        return True

    def read_retry_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def level_name(self, level):
        return "L{0}".format(level + 1)

    def offset_to_voltage(self, offset):
        return offset

    def offset_to_code(self, offset):
        # default implementation
        # in order to have linear scan from min offset to max offset
        code = (offset + 0x80) & 0xFF
        return code

    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):
        return False, 0.0

    def reset_read_offset_code(self, ch_list: [], ce, lun, level):
        return True

    def read_offset_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def read_offset_page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.page_read(ch_list, ce, lun, block, page, column, page_length)

    def vt_level_name(self, level):
        return "VTL{0}".format(level + 1)

    def vt_init(self, ch_list: [], ce, lun, level):
        return

    def vt_terminate(self, ch_list: [], ce, lun, level):
        return

    def set_vt_code(self, ch_list: [], ce, lun, level, vt):
        return False, 0

    def reset_vt_code(self, ch_list: [], ce, lun):
        return

    def vt_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def vt_page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.page_read(ch_list, ce, lun, block, page, column, page_length)

    def read_internal_temperature(self, ch_list: [], ce):
        return True, 25.0

    def is_bad_block(self, ch_list: [], ce, lun, block):
        isBadBlock = False
        return isBadBlock

    def load_info(self, ch, ce, lun, uid, name, did, manufacturer = "", device_model = ""):
        return

    def custom_test_1_log_measure(self, tag, target, measure, limit):
        error = abs(target - measure)
        ok = (error <= limit)

        msg = "[{0}] Target: {1:.3f} - Measure: {2:.3f} - Error: {3:.3f} - Limit: {4:.3f}".format(tag, target, measure,
                                                                                                  error, limit)
        if ok:
            ws.info(msg)
        else:
            ws.warning(msg)

        datalog.add_data_tag("Target", tag, "{0:.3f}".format(target))
        datalog.add_data_tag("Measure", tag, "{0:.3f}".format(measure))
        datalog.add_data_tag("Limit", tag, "{0:.3f}".format(limit))
        datalog.add_data_tag("Error", tag, "{0:.3f}".format(error))
        datalog.add_data_tag("Pass", tag, "{0}".format("PASS" if ok else "FAIL"))

        return ok

    def custom_test_1_power_measures(self, vcc_target, vccq_target, vpp_target):
        ok = True

        vcc_limit = 0.050 # 50mV limit
        vccq_limit = 0.050 # 50mV limit
        vpp_limit = 0.100 # 100mV limit
        i_limit = 10 # 10mA limit

        ###########################################
        # Measure and log VCC-ICC

        vcc_measure = hw.get_vcc()
        ok &= self.custom_test_1_log_measure("VCC", vcc_target, vcc_measure, vcc_limit)  # 50mV limit

        icc_slow = hw.adc_measure(hw.enumADCChannel.Icc)
        ok &= self.custom_test_1_log_measure("ICC-SLOW", 0, icc_slow, i_limit)  # 10mA limit

        hw.pmu_offset_compensation(hw.enumPmuChannel.Icc)
        hw.pmu_setup(hw.enumPmuChannel.Icc, hw.enumPmuTriggerMode.PmuSoftware, 128, 1024)
        hw.pmu_start(True)
        res, icc_fast = hw.pmu_get_average_of_samples()
        ok &= self.custom_test_1_log_measure("ICC-FAST", 0, icc_fast, i_limit)  # 10mA limit

        ###########################################
        # Measure and log VCCQ-ICCQ

        vccq_measure = hw.get_vccq()
        ok &= self.custom_test_1_log_measure("VCCQ", vccq_target, vccq_measure, vccq_limit)  # 50mV limit

        iccq_slow = hw.adc_measure(hw.enumADCChannel.Iccq)
        ok &= self.custom_test_1_log_measure("ICCQ-SLOW", 0, iccq_slow, i_limit)  # 10mA limit

        hw.pmu_offset_compensation(hw.enumPmuChannel.Iccq)
        hw.pmu_setup(hw.enumPmuChannel.Iccq, hw.enumPmuTriggerMode.PmuSoftware, 128, 1024)
        hw.pmu_start(True)
        res, iccq_fast = hw.pmu_get_average_of_samples()
        ok &= self.custom_test_1_log_measure("ICCQ-FAST", 0, iccq_fast, i_limit)  # 10mA limit

        ###########################################
        # Measure and log VPP-IPP

        vpp_measure = hw.get_vpp()
        ok &= self.custom_test_1_log_measure("VPP", vpp_target, vpp_measure, vpp_limit)  # 100mV limit

        hw.adc_offset_compensation(hw.enumADCChannel.Ipp)
        ipp_slow = hw.adc_measure(hw.enumADCChannel.Ipp)
        ok &= self.custom_test_1_log_measure("IPP-SLOW", 0, ipp_slow, i_limit)  # 10mA limit

        hw.pmu_offset_compensation(hw.enumPmuChannel.Ipp)
        hw.pmu_setup(hw.enumPmuChannel.Ipp, hw.enumPmuTriggerMode.PmuSoftware, 128, 1024)
        hw.pmu_start(True)
        res, ipp_fast = hw.pmu_get_average_of_samples()
        ok &= self.custom_test_1_log_measure("IPP-FAST", 0, ipp_fast, i_limit)  # 10mA limit

        return ok

    def custom_test_1_power_set(self, vcc_list, vccq_list, vpp_list):

        ok = True

        ch_list = [0,1]
        ce_list = [0]
        self.device_reset(ch_list, ce_list)
        for ch in ch_list:
            hw.select_channel(ch)
            res, RnBtime = hw.custom_sequence_get_busy_time()
            if RnBtime > 0:
                ok &= False
                ws.warning("CH: {0} ReadyBusy Time [{1}] is not 0".format(ch, RnBtime))

        range_len = min([len(vcc_list), len(vccq_list), len(vpp_list)])

        for i in range(range_len):

            vcc = vcc_list[i]
            vccq = vccq_list[i]
            vpp = vpp_list[i]

            self.set_power_supply(vcc, vccq, vpp)

            time.msec_sleep(3000)  # without load takes time to go down
            ok &= self.custom_test_1_power_measures(vcc, vccq, vpp)

        if not ok:
            ws.set_alarm("Acceptance Inspection Failed!")

        return ok

    def range_plus(self, low, up, length):
        list = []
        step = (up - low) / float(length)
        for i in range(length):
            list.append(low)
            low = low + step
        return list


    def custom_test_1(self):
        enable_temperature = True
        lSettle = 300
        temp_list = [40, 80, 125, 50]
        max_elapsed_time_list = [180, 180, 180, 300]
        vcc_list = [3.3, 1.0, 3.8]
        vccq_list = [1.2, 0.95, 1.9]
        vpp_list = [12.0, 10.0, 14.0]

        TEMP_PROFILER_INTERVAL = 5
        TEMP_ACCURACY = 1
        TIME_ACCURACY = 10 # sec

        ok = True

        datalog.set_label("ACCEPTANCE-INSPECTION")

        self.turn_on(3.3, 1.2)

        ok &= self.custom_test_1_power_set(vcc_list, vccq_list, vpp_list)

        if enable_temperature:
            temp_plot = nanocycler.plot()
            temp_plot.open("TempProfiler-Custom_test-1", "Time", "Temp", "Sensor")
            hw.temperature_enable(True)
            # hw.board_set_temp_led(eBlinkStatusType.BlinkSlow)

            step = 0

            range_len = min([len(temp_list), len(max_elapsed_time_list)])

            for i in range(range_len):
                temp_set_point = temp_list[i]
                target_temp_elapsed_time = max_elapsed_time_list[i]
                ws.info("Set Temperature: " + str(temp_set_point))
                hw.temperature_set(float(temp_set_point))
                t1 = hw.get_nsec_time() // 1e9

                temp_sensor = 0
                t2 = t1

                while abs(temp_sensor - float(temp_set_point)) > TEMP_ACCURACY:
                    if ws.is_stop_request():
                        break
                    fX = step * TEMP_PROFILER_INTERVAL
                    temp_sensor = hw.sensor_temperature_get()
                    t2 = hw.get_nsec_time() // 1e9
                    temp_plot.add(fX, temp_set_point, "SetPoint")
                    temp_plot.add(fX, temp_sensor, "Sensor")
                    temp_plot.flush()

                    step = step + 1
                    time.sleep(TEMP_PROFILER_INTERVAL)

                elapsed = t2 - t1
                in_time = (elapsed - target_temp_elapsed_time) <= TIME_ACCURACY
                ok &= in_time
                res = "PASS" if in_time  else "FAIL"
                if in_time:
                    ws.info("Temperature {0:.2f} reached in {1:.2f} seconds [{2}]".format(temp_set_point, elapsed, res))
                else:
                    ws.warning("Temperature {0:.2f} reached in {1:.2f} seconds [{2}]".format(temp_set_point, elapsed, res))
                datalog.set_temperature(temp_sensor)
                datalog.add_data("Elapsed", "{0}".format(elapsed))
                datalog.add_data("Pass", "{0}".format(res))

                ws.info("Settle: " + str(lSettle) + " [sec]")

                for i_settle in range(0, lSettle, TEMP_PROFILER_INTERVAL):
                    if ws.is_stop_request():
                        break

                    fX = step * TEMP_PROFILER_INTERVAL
                    temp_sensor = hw.sensor_temperature_get()
                    temp_plot.add(fX, temp_set_point, "SetPoint")
                    temp_plot.add(fX, temp_sensor, "Sensor")
                    temp_plot.flush()

                    step = step + 1
                    time.sleep(TEMP_PROFILER_INTERVAL)

                in_temp_spec = abs(temp_sensor - temp_set_point) <= TEMP_ACCURACY
                ok &= in_temp_spec
                res = "PASS" if in_temp_spec else "FAIL"
                if in_temp_spec:
                    ws.info("Temperature after settle time is: {0:.2f} [{1}]".format(temp_sensor, res))
                else:
                    ws.warning("Temperature after settle time is: {0:.2f} [{1}]".format(temp_sensor, res))
                datalog.set_temperature(temp_sensor)
                datalog.add_data("Temperature", "{0:.1f}".format(temp_sensor))
                datalog.add_data("Pass", "{0}".format(res))

                # apply and measure voltage and current
                ok &= self.custom_test_1_power_set(vcc_list, vccq_list, vpp_list)

            # hw.board_set_temp_led(eBlinkStatusType.BlinkOff)
            hw.temperature_enable(False)

            # close plot
            temp_plot.close()

            datalog.set_label("")

        self.turn_off()

        if not ok:
            ws.error("Acceptance Inspection Failed!")
            ws.set_alarm("Acceptance Inspection Failed!")



    def custom_test_2(self):
        vcc_list = [3.3, 1.0, 3.8]
        vccq_list = [1.2, 0.95, 1.9]
        vpp_list = [12.0, 10.0, 14.0]

        datalog.set_label("READY-BUSY-CHECK")

        self.turn_on(3.3, 1.2)

        ok = True
        ch_list = [0, 1]
        ce_list = [0]

        for r in range(1000):
            self.device_reset(ch_list, ce_list)
            for ch in ch_list:
                hw.select_channel(ch)
                res, RnBtime = hw.custom_sequence_get_busy_time()
                if RnBtime > 0:
                    ok &= False
                    ws.warning("Retry: {0} - CH: {1} ReadyBusy Time [{2}] is not 0".format(r, ch, RnBtime))

        datalog.set_label("")

        self.turn_off()

        if not ok:
            ws.error("READY-BUSY-CHECK Failed!")
            ws.set_alarm("READY-BUSY-CHECK Failed!")




