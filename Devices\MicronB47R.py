import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.Micron import CMicron as CMicron


###########################################################################
### Reference Datasheet: b47r_fortis_max_512gb_nand_datasheet.pdf
###########################################################################


class CMicronB47R(CMicron):
    def __init__(self):
        CMicron.__init__(self)
        self.DEVICE_MANUFACTURER = "Micron"
        self.DEVICE_NAME = "B47R"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2  # B47R has 2 CE per channel
        self.DEVICE_ID_LEN = 4

        self.PAGE_LENGTH = 18352
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 4  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER =  2 # MT29F4T08EULEEM4
        self.BLOCK_NUMBER = 1024  # it is just a number it depends on device size
        self.PAGE_NUMBER = 2112  # per block

        self.LUN_START_BIT_ADDRESS = 24
        self.BLOCK_START_BIT_ADDRESS = 12
        self.VALID_LUN_MASK = 0x03
        self.VALID_PAGE_MASK = 0xFFF
        self.VALID_BLOCK_MASK = 0xFFF

        self.RR_OPTIONS = 8
        self.rr_option = 0

        self.level = 0
        self.offset_code = 0


    def micron_set_row_address(self, opcbuilder, row_address):
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)


    def micron_set_row_address_reg(self, opcbuilder):
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 4, is stored as byte 4 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 5, is stored as byte 5 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 6, is stored as byte 6 of the ram
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 7) # row address byte 7, is stored as byte 7 of the ram


    def die_configure(self, ch_list, ce_list: [], lODT = 4, lDriverStrength = 4):

        # for hard reset
        for ce in ce_list:
            for lun in range(self.LUN_NUMBER):
                self.get_status_enhanced_78h(ch_list, ce, lun, 0)
                self.device_reset(ch_list, [ce], ONFI_CMD.RESET_CMD_FDh)

        self.device_reset(ch_list, ce_list, ONFI_CMD.RESET_CMD_FFh)

        # driver strength
        # 2: 37.5 Ohm (default)
        # 3: 50 Ohm
        # 4: 37.5 Ohm
        p1 = lDriverStrength
        self.set_feature(ch_list, ce_list, 0x10, p1, 0x00, 0x00, 0x00)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        bODT = lODT
        bRE_C = 1
        bDQS_c = 1
        bVREF = 1
        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)

        p1 = (bODT << 4) | (bRE_C << 2) | (bDQS_c << 1) | (bVREF << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        p3 = 0
        p4 = 0

        self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4)



    def offset_to_voltage(self, offset):
        offset_voltage = -1280 + offset * 10

        return offset_voltage


    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):

        if level > 6:
            return False, 0.0

        offset_code =  self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        self.level = level
        self.offset_code = offset_code

        return True, offset_voltage

    @staticmethod
    # just process TLC pages
    def is_lower_page(page):
        if (page >= 4) and (page <= 1047):
            return ((page - 4) % 3) == 0
        if (page >= 1064) and (page <= 2107):
            return ((page - 1064) % 3) == 0
        return False

    @staticmethod
    # just process TLC pages
    def is_upper_page(page):
        if (page >= 4) and (page <= 1047):
            return ((page - 4) % 3) == 1
        if (page >= 1064) and (page <= 2107):
            return ((page - 1064) % 3) == 1
        return False

    @staticmethod
    # just process TLC pages
    def is_extra_page(page):
        if (page >= 4) and (page <= 1047):
            return ((page - 4) % 3) == 2
        if (page >= 1064) and (page <= 2107):
            return ((page - 1064) % 3) == 2
        return False

    def read_offset_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        offset_cycles = 0
        # cycle 0 => L1 lower, L2 upper, L3 extra
        if (self.level == 0 and CMicronB47R.is_lower_page(page)) or \
                (self.level == 1 and CMicronB47R.is_upper_page(page)) or \
                (self.level == 2 and CMicronB47R.is_extra_page(page)):
            offset_cycles |= self.offset_code
        # cycle 0 => L5 lower, L4 upper, L7 extra
        if (self.level == 4 and CMicronB47R.is_lower_page(page)) or \
                (self.level == 3 and CMicronB47R.is_upper_page(page)) or \
                (self.level == 6 and CMicronB47R.is_extra_page(page)):
            offset_cycles |= self.offset_code << 8
        # cycle 3 => L6 upper
        if self.level == 5 and CMicronB47R.is_upper_page(page):
            offset_cycles |= self.offset_code << 16

        self.set_ram_data_3(ch_list, column_address, row_address, offset_cycles)

        # in order to improve the performance, the sequence is created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, 0x2E)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 8) # offset Cycle 1 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 9) # offset Cycle 2 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 10) # offset Cycle 3 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i= False)


    def read_offset_page_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        offset_cycles = 0
        # cycle 0 => L1 lower, L2 upper, L3 extra
        if (self.level == 0 and CMicronB47R.is_lower_page(page)) or \
                (self.level == 1 and CMicronB47R.is_upper_page(page)) or \
                (self.level == 2 and CMicronB47R.is_extra_page(page)):
            offset_cycles |= self.offset_code
        # cycle 0 => L5 lower, L4 upper, L7 extra
        if (self.level == 4 and CMicronB47R.is_lower_page(page)) or \
                (self.level == 3 and CMicronB47R.is_upper_page(page)) or \
                (self.level == 6 and CMicronB47R.is_extra_page(page)):
            offset_cycles |= self.offset_code << 8
        # cycle 3 => L6 upper
        if self.level == 5 and CMicronB47R.is_upper_page(page):
            offset_cycles |= self.offset_code << 16

        self.set_ram_data_3(ch_list, column_address, row_address, offset_cycles)

        # in order to improve the performance, the sequence is created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, 0x2E)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 8)  # offset Cycle 1 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 9)  # offset Cycle 2 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 10)  # offset Cycle 3 is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG,
                                      0)  # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG,
                                      1)  # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

    #######################################################################################
    # Read Retry

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:
            if retry_option >= len(self.option_buffer) == 0:
                return False, ""
            self.rr_option = int(self.option_buffer[retry_option][0])
        else:
            self.rr_option = retry_option

        if self.rr_option >= self.RR_OPTIONS:
            return False, ""

        # enable ACRR and calibration read
        acrr = 1
        calibration_read = 0
        p1 = (calibration_read) | (acrr << 2)
        self.set_feature_by_lun(ch_list, [ce], lun, 0x96, p1, 0, 0, 0)

        return True, "{0}".format(hex(self.rr_option))


    def reset_read_retry_option(self, ch_list: [], ce, lun):
        # set default rr_option = 0
        self.rr_option = 0

        # re-run the old read sequence to a dummy address row=0, col=0, to apply default rr_option = 0
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        ram_dword0 = (0 & 0xFFFF ) | (self.rr_option << 16)
        ram_dword1 = 0
        self.set_ram_data_2(ch_list, ram_dword0, ram_dword1)

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        # now disable ACRR
        self.set_feature_by_lun(ch_list, [ce], lun, 0x96, 0, 0, 0, 0)

        return True

    def read_retry_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # READ PAGE (00h-30h) Operation

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)
        # seed_high, seed_low = BaseDevice.recall_pattern(self, 0, lun, block, page)

        ram_dword0 = (column_address & 0xFFFF ) | (self.rr_option << 16)
        ram_dword1 = row_address
        self.set_ram_data_2(ch_list, ram_dword0, ram_dword1)

        # in order to improve the performance, the sequence is created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0)  # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1)  # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 2)  # rr_option is stored as byte21 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DELAY, 100)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_DELAY, self.tPRE)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)
        
        logger.log_read("READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=True, en_i=False)