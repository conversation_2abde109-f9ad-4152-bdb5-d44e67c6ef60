## @package nanocycler.time
#  <summary> Contains the time functions. </summary>
#
# @ingroup wsApiGroupPyLanguage

from nanocycler import hardware as hw
from nanocycler import ws as ws

class NanoTimer:

    @staticmethod
    def sleep(sec: int):
        start = hw.get_nsec_time()
        elapsed = 0

        while elapsed < sec:
            elapsed = (hw.get_nsec_time() - start) / 1E9

            bStop = ws.is_stop_request()
            if bStop:
                ws.warning("Stop Requested")
                break

        return

    @staticmethod
    def msec_sleep(msec: int):
        start = hw.get_nsec_time()
        elapsed = 0

        while elapsed < msec:
            elapsed = (hw.get_nsec_time() - start) / 1E6

        return

    @staticmethod
    def usec_sleep(usec: int):
        start = hw.get_nsec_time()
        elapsed = 0

        while elapsed < usec:
            elapsed = (hw.get_nsec_time() - start) / 1E3

        return

    @staticmethod
    def nsec_sleep(nsec: int):
        hw.get_nsec_time()
        hw.nsec_delay(nsec)

# ! @endcond
