## @package nanocycler.sequencetable
# <summary>	Contains the sequence table vectors to be executed by Fpga </summary>
#
# @ingroup onfiApiGroupPyLanguage

from .libmanager import *
from .utility import *

_lib_handle = CLibManagerSingleton.load_libwslibpy()

_sequencetableapicreate = CLibManagerSingleton.load_func(_lib_handle, "p", "SequenceTableApi_Create", "LBBBBBBBB")
_sequencetableapidelete = CLibManagerSingleton.load_func(_lib_handle, "v", "SequenceTableApi_Delete", "p")
_sequencetableapisetedge = CLibManagerSingleton.load_func(_lib_handle, "b", "SequenceTableApi_SetEdge", "pifB")
_sequencetableapicompile = CLibManagerSingleton.load_func(_lib_handle, "i", "SequenceTableApi_Compile", "p")
_sequencetableapigetdatavector = CLibManagerSingleton.load_func(_lib_handle, "b", "SequenceTableApi_GetDataVector", "pLp")


##<summary> Defines the sequence signals of sequence table.</summary>
#
# @ingroup wsApiGroup
class enumSequenceSignal:
    # ! @cond Doxygen_Suppress
    CE = 0
    WP = 1
    CLE = 2
    ALE = 3
    nWE = 4
    nRE = 5
    DQS = 6
    DQ = 8
	
	# ! @endcond

## <summary>	The sequence table object contains the sequence table vectors to be executed by Fpga.</summary>
#
#  @ingroup wsApiGroup
class sequencetable:
    # ! @cond Doxygen_Suppress
    def __init__(self, lFreqMHz, bCE, bWP, bCLE, bALE, bnWE, bnRE, bDQS, bDQ):
        self.seqtableptr = _sequencetableapicreate(lFreqMHz, bCE, bWP, bCLE, bALE, bnWE, bnRE, bDQS, bDQ)

    # ! @endcond

    ## <summary>	Cleanup function to unload the library from memory.</summary>
    #
    def cleanup(self):
        if self.seqtableptr is not None:
            _sequencetableapidelete(self.seqtableptr)

    # self.npy.close()

    ## <summary>	Get pointer of sequence vector table object.</summary>
    #
    # <returns>	return pointer of sequence vector table object for execution.</returns>
    def get_ptr(self):
        return self.seqtableptr

    ## <summary>	Set Edge.</summary>
    #
    # <param name="eSignal"> enumSequenceSignal enumerative value.	</param>
    # <param name="fNsecTime"> Time in nano seconds. </param>
    # <param name="nextValue"> Next byte value</param>
    #
    # <returns>	true if it succeeds, false if it fails.</returns>
    def set_edge(self, eSignal, fNsecTime, nextValue):
        return bool(_sequencetableapisetedge(self.seqtableptr, eSignal, fNsecTime, nextValue))

    ## <summary>	Compile.</summary>
    #
    def compile(self):
        return int(_sequencetableapicompile(self.seqtableptr))

    ## <summary>	Get data vector.</summary>
    #
    # <param name="vectorIndex"> Vector index to retrieve.	</param>
    # <param name="vector">	[out] Bytearray buffer of 16 byte </param>
    #
    # <returns>	true if it succeeds, false if it fails.</returns>
    def get_data_vector(self, vectorIndex, vector):
        adraBuffer = address_of(vector)
        return bool(_sequencetableapigetdatavector(self.seqtableptr, vectorIndex, adraBuffer))
