## @package nanocycler.hardware
# 	<summary>	The Hardware control api.
# 					Used to control the hardware mounted on board</summary>
#
# @ingroup wsApiGroupPyLanguage

#from .libmanager import *
from .utility import *
from .opcodebuilder import *

# ! @cond Doxygen_Suppress
_wslib_lib_handle = CLibManagerSingleton.load_libwslib()

_init_wslib = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "Init", "")
_terminate_wslib = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "Terminate", "")
_turnon = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TurnOn", "ffL")
_turnoff = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TurnOff", "")
_temperatureget = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "TemperatureGet", "")
_temperatureset = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TemperatureSet", "ff")
_temperatureenable = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TemperatureEnable", "b")
_temperatureenableplot = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TemperatureEnablePlot", "bi")
_settemperaturereference = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetTemperatureReference", "f")
_resettemperaturereference = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "ResetTemperatureReference", "")
_sensortemperatureget = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "SensorTemperatureGet", "")
_getvcc = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "GetVcc", "")
_getvccq = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "GetVccq", "")
_getvpp = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "GetVpp", "")
_setvccon = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVccOn", "")
_setvccoff = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVccOff", "")
_setvccqon = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVccqOn", "")
_setvccqoff = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVccqOff", "")
_setvppon = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVppOn", "")
_setvppoff = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetVppOff", "")
_setdatarate = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "SetDataRate", "L")
_getnsectime = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "GetNsecTime", "")
_gettime = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "GetTime", "pis")
_nsecdelay = CLibManagerSingleton.load_func(_wslib_lib_handle, "v", "NsecDelay", "L")
_getboardversion = CLibManagerSingleton.load_func(_wslib_lib_handle, "L", "GetBoardVersion", "")
_getfpgaversion = CLibManagerSingleton.load_func(_wslib_lib_handle, "L", "GetFpgaVersion", "")
_getboardstatus = CLibManagerSingleton.load_func(_wslib_lib_handle, "L", "GetBoardStatus", "")
_boardsetstatusled = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "BoardSetStatusLed", "i")
_boardsettempled = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "BoardSetTempLed", "i")
_debugsetled = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "DebugSetLed", "ii")
_vccset = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "VccSet", "f")
_vccqset = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "VccqSet", "f")
_vppset = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "VppSet", "f")
_adcmeasure = CLibManagerSingleton.load_func(_wslib_lib_handle, "d", "ADCMeasure", "i")
_adcoffsetcompensation = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "ADCOffsetCompensation", "i")
_pmuoffsetcompensation = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuOffsetCompensation", "i")
_pmusetup = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuSetup", "iiLL")
_pmuaveragesetup = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuAverageSetup", "ii")
_pmumaxsetup = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuMaxSetup", "iiL")
_pmustart = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuStart", "b")
_pmustop = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuStop", "b")
_pmugetsamples = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuGetSamples", "pip")
_pmugetaverageofsamples = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuGetAverageOfSamples", "p")
_pmugetmaxofsamples = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "PmuGetMaxOfSamples", "p")
_pmugetstatusmask = CLibManagerSingleton.load_func(_wslib_lib_handle, "i", "PmuGetStatusMask", "")
_temperaturesetlimit = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "TemperatureSetLimit", "f")
_istemperaturealert = CLibManagerSingleton.load_func(_wslib_lib_handle, "b", "IsTemperatureAlert", "")
_enablesequencerecording = CLibManagerSingleton.load_func(_wslib_lib_handle, "v", "EnableSequenceRecording", "bs")
# ! @endcond


# ! @cond Doxygen_Suppress

CHANNEL_NUM = 2
CE_NUM = 4
OP_CODE_MAX_COUNT = 1024

MIN_DATA_RATE_MTS = 400
MAX_DATA_RATE_MTS = 1600
DEFAULT_DATA_RATE_MTS = 400

# ! @endcond


##<summary>	Defines the Power Supply enumerative.</summary>
#
# @ingroup wsApiGroup
class enumPsStatusType:
    ## <summary>	An enum constant representing the PowerSupply Off ( Relay open ).</summary>
    PsOff = 0
    ## <summary>	An enum constant representing the PowerSupply On ( Relay closed ).</summary>
    PsOn = 1


##<summary>	Defines the blink status enumerative of leds in the board.</summary>
#
# @ingroup wsApiGroup
class enumBlinkStatusType:
    ## <summary>	An enum constant representing the led blink off option.</summary>
    BlinkOff = 0
    ## <summary>	An enum constant representing the led blink on option.</summary>
    BlinkOn = 1
    ## <summary>	An enum constant representing the led blink slow option.</summary>
    BlinkSlow = 2
    ## <summary>	An enum constant representing the led blink fast option.</summary>
    BlinkFast = 3


##<summary>	ADC channel enumerative for slow ADC control (ADC128D818 in Power Supply Unit).</summary>
#
# @ingroup wsApiGroup
class enumADCChannel:
    ## <summary>	An enum constant representing the Vcc option.</summary>
    Vcc = 0
    ## <summary>	An enum constant representing the Vccq option.</summary>
    Vccq = 1
    ## <summary>	An enum constant representing the Vpp option.</summary>
    Vpp = 2
    ## <summary>	An enum constant representing the Heater voltage option.</summary>
    VHeater = 3
    ## <summary>	An enum constant representing the Fan Voltage option.</summary>
    VFan = 4
    ## <summary>	An enum constant representing the Icc option.</summary>
    Icc = 5
    ## <summary>	An enum constant representing the Iccq option.</summary>
    Iccq = 6
    ## <summary>	An enum constant representing the Ipp option.</summary>
    Ipp = 7


##<summary>	Fast ADC trigger mode.</summary>
#
# @ingroup wsApiGroup
class enumPmuTriggerMode:
    ## <summary>	An enum constant representing the hardware trigger option.</summary>
    PmuHardware = 0
    ## <summary>	An enum constant representing the software trigger option.</summary>
    PmuSoftware = 1


##<summary>	Fast ADC channel.</summary>
#
# @ingroup wsApiGroup
class enumPmuChannel:
    ## <summary>	An enum constant representing the Icc option.</summary>
    Icc = 0
    ## <summary>	An enum constant representing the Iccq option.</summary>
    Iccq = 1
    ## <summary>	An enum constant representing the Ipp option.</summary>
    Ipp = 2


##<summary>	Fast ADC status.</summary>
#
# @ingroup wsApiGroup
class enumPmuStatusMask:
    ## <summary>	An enum constant representing the Alarm option, pmu in alarm.</summary>
    PmuAlarmMask = 0x80
    ## <summary>	An enum constant representing the running option, a measurement operation is still running.</summary>
    PmuRunningMask = 0x20
    ## <summary>	An enum constant representing the ready option, measurement completed.</summary>
    PmuReadyMask = 0x01

## <summary>	Executes the initialization of hardware and onfi lib to give the hardware accessibility
# 					Called during Test Program initialization to make sure that the hardware is initialized for first
# 					</summary>
#
# <returns>	true, no fails.</returns>
def init():
    res = True
    res |= _init_wslib()
    res |= _init_onfilib()
    return res


## <summary>	Executes the api termination.</summary>
#
# <returns>	true, no fails.</returns>
def terminate():
    res = True
    res |= _terminate_onfilib()
    res |= _terminate_wslib()
    return res

## <summary>	Gets the board version.
# 		Bit  0..7: board revision - socket
#	 	Bit 15..8: nacycler version (0 standard , 1 high speed)  </summary>
#
# <returns>	The version.</returns>
def get_board_version():
    return int(_getboardversion())

## <summary>	Gets the FPGA version.</summary>
#
# <returns>	The version.</returns>
def get_fpga_version():
    return int(_getfpgaversion())


## <summary>	Gets Board Status register of the the FPGA.</summary>
#
# <returns>	The status register.</returns>
def get_board_status():
    return int(_getboardstatus())


## <summary>	Executes the turn on operation.
# 				The function turns-on the power supply (it enables the switches) and program the DAC for Vcc and VccQ level. </summary>
#
# <param name="vcc"> 	The vcc.</param>
# <param name="vccq">	The vccq.</param>
# <param name="vccqDelayMsec">	Msec delay between Vcc and VccQ.</param>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def turn_on(vcc: float, vccq: float, vccqDelayMsec: int = 100):
    return bool(_turnon(vcc, vccq, vccqDelayMsec))


## <summary>	Executes the turn off operation.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def turn_off():
    return bool(_turnoff())


## <summary>	Gets the current Temperature of socket.</summary>
#
# <returns>	the temperature.</returns>
# @snippet std_examples.py Hardware temperature
def temperature_get():
    return float(_temperatureget())


## <summary>	Sets the desidered socket temperature.</summary>
#
# <param name="target">	the Target temperature.</param>
# <param name="accuracy">	the accuracy of measure.</param>
#
# <returns>	true if it succeeds, false if it fails.</returns>
# @snippet std_examples.py Hardware temperature
def temperature_set(target: float, accuracy: float = 1.0):
    return bool(_temperatureset(target, accuracy))


## <summary>	Enables or disables the temperature controller.</summary>
#
# <param name="enable">	true to enable, false to disable.</param>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware temperature
def temperature_enable(enable: bool):
    return bool(_temperatureenable(int(enable)))


## <summary>	Enables or disables the temperature plot.</summary>
#
# <param name="enable">        True to enable, False to disable.</param>
# <param name="secRefreshTime">	The refresh time in seconds.</param>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware temperature
def temperature_enable_plot(enable: bool, secRefreshTime):
    return bool(_temperatureenableplot(int(enable), secRefreshTime))


## <summary>	This routine is used to specify the internal device temperature in order to have more accurate temperature regulation.
#				The reference temperature can be the temperature measured using a specific die command or the average between some dies.</summary>
#
# <param name="dieTemperature">	The reference temperature measured in a die.</param>
#
# <returns>		true if it succeeds
#				false if it fails
#				false if read temperature is negative
#				false if the factor Die over Read temperature is too high
#	</returns>
# @snippet std_examples.py Hardware temperature
def set_temperature_reference(dieTemperature: float):
    return bool(_settemperaturereference(dieTemperature))


## <summary>	It resets the temperature regulation to use only the sensor on the bridge.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware temperature
def reset_temperature_reference():
    return bool(_resettemperaturereference())


## <summary>	It returns the sensor temperature without any regulation on the reference temperature.</summary>
#
# <returns>	the sensor temperature</returns>
# @snippet std_examples.py Hardware temperature
def sensor_temperature_get():
    return float(_sensortemperatureget())


## <summary>	Gets the Vcc.</summary>
#
# <returns>	The Vcc value.</returns>
# @snippet std_examples.py Hardware voltage
def get_vcc():
    return float(_getvcc())


## <summary>	Gets the VccQ.</summary>
#
# <returns>	the VccQ.</returns>
# @snippet std_examples.py Hardware voltage
def get_vccq():
    return float(_getvccq())


## <summary>	Gets the VPP.</summary>
#
# <returns>	the VPP.</returns>
# @snippet std_examples.py Hardware voltage
def get_vpp():
    return float(_getvpp())


## <summary>	Turn on the Vcc, It closes the switch to supply Vcc.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vcc_on():
    return bool(_setvccon())


## <summary>	Turn off the Vcc.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vcc_off():
    return bool(_setvccoff())


## <summary>	Turn on the VccQ based on the jumper configuration.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vccq_on():
    return bool(_setvccqon())


## <summary> Turn off the VccQ.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vccq_off():
    return bool(_setvccqoff())


## <summary>	Turn on the VPP, It closes the switch to supply VPP.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vpp_on():
    return bool(_setvppon())


## <summary>	Turn off the VPP.</summary>
#
# <returns>	true, no fails.</returns>
# @snippet std_examples.py Hardware voltage
def set_vpp_off():
    return bool(_setvppoff())


## <summary>	Sets the FPGA frequency.</summary>
#
# <param name="data_rate_mts">	The frequency in MTs.</param>
#
# <returns>	true if PLL is locked, false if no.</returns>
def set_datarate(data_rate_mts: int):
    return bool(_setdatarate(data_rate_mts))

## <summary>	Get elapsed time in nsec. It counts nsec after the system boot.</summary>
#
# <returns>	The elapsed time in nsec. 20 nsec is the resolution, the max value is (2^64-1)*20 nsec.</returns>
def get_nsec_time():
    return float(_getnsectime())

## <summary>	Get current time with selected format.</summary>
#
# <param name="format">	see the format string of strftime C function.</param>
#
# <returns>	true if format is correct and the timestamp formatted string.</returns>
def get_time(format = "%Y%m%d-%H%M%S"):
    lTimeByteNumber = 80
    aTime = bytearray(lTimeByteNumber)
    adraTime = address_of(aTime)
    bRes = bool(_gettime(adraTime, lTimeByteNumber, format))
    sTime = aTime.decode('utf-8')
    return bRes, sTime


## <summary>	Nano second delay implemented in hardware. Be careful the call to c++ function will take hundred of usec.</summary>
#
# <param name="nsec">	Nano second to wait.</param>
def nsec_delay(nsec: int):
    _nsecdelay(nsec)


## <summary>	Sets the board status led.</summary>
#
# <param name="etype">	The Blink Status enumerative.</param>
#
# <returns>	true if it succeeds
# 			false if eType is not valid
# </returns>
def board_set_status_led(etype: enumBlinkStatusType):
    return bool(_boardsetstatusled(etype))


## <summary>	Sets the board temperature led.</summary>
#
# <param name="etype">	The Blink Status enumerative.</param>
#
# <returns>	true if it succeeds
# 			false if eType is not valid
# </returns>
def board_set_temp_led(etype: enumBlinkStatusType):
    return bool(_boardsettempled(etype))


## <summary>	Sets the board temperature led.</summary>
#
# <param name="ledIndex">	The Led Index ( 0..3) </param>
# <param name="eType">	The BlinkStatus Type </param>
#
# <returns>	true if it succeeds
#			false if it fails
#           false if Led is out of Indexs 0..3
# </returns>
def debug_set_led(ledIndex: int, eType: enumBlinkStatusType):
    return bool(_debugsetled(ledIndex, eType))


## <summary>	Program the Vcc level.</summary>
#
# <param name="value"> 	The target Vcc level.</param>
#
# <returns>	true if it succeeds, false if value is out of range 0.8...3.3V.</returns>
def vcc_set(value: float):
    return bool(_vccset(value))


## <summary>	Program the VccQ level.</summary>
#
# <param name="value"> 	The target VccQ level.</param>
#
# <returns>	rue if it succeeds, false if value is out of range 1.0...2.0V.</returns>
def vccq_set(value: float):
    return bool(_vccqset(value))


## <summary>	Program the VPP level.</summary>
#
# <param name="value"> 	The target VPP level.</param>
#
# <returns>	true if it succeeds, false if value is out of range 10.0...15.0V.</returns>
def vpp_set(value: float):
    return bool(_vppset(value))


## <summary>	Auto compensation of ADC Measurement Offset. It must be called only when device is in standby</summary>
#
# <param name="eADCChannel"> 	The channel to compensate.</param>
#
# <returns>	true if it succeeds
# </returns>
def adc_offset_compensation(eADCChannel: enumADCChannel):
    return bool(_adcoffsetcompensation(eADCChannel))


## <summary>	Executes the ADC measure on selected channel.</summary>
#
# <param name="eADCChannel"> 	The channel to measure.</param>
#
# <returns>	the measured values.</returns>
def adc_measure(eADCChannel: enumADCChannel):
    return float(_adcmeasure(eADCChannel))


## <summary>	Auto compensation of PMU Measurement Offset. It must be called only when device is in standby</summary>
#
# <param name="eChannel"> 	The channel to compensate.</param>
#
# <returns>	true if it succeeds
#				false if the ADC is busy
#				false if the number of measurement per sample is 0
#				false if there are no samples
# </returns>
def pmu_offset_compensation(eChannel: enumPmuChannel):
    return bool(_pmuoffsetcompensation(eChannel))


## <summary>	Configure the ADC for the next acquisition.
#				Because the max number of samples is 2^52 but the buffer Length is limited to 2048 samples,
#				the measure can continue even if the buffer is full and the max and average can be still aquired
# </summary>
#
# <param name="ePumChannel"> 	The channel to measure.</param>
# <param name="ePmuTriggerMode"> 	The trigger mode.</param>
# <param name="samples"> 	Number of samples to acquire.</param>
# <param name="averages"> 	Number of averages for each sample.</param>
#
# <returns>	true if it succeeds
#             false ePmuChannel is not valid index
#             false ePmuTriggerMode is not valid index
#             false lSamples exceeds 2^52
#             false lAverages exceeds 2^20
# </returns>
# @snippet hs_examples.py Pmu Example
def pmu_setup(ePumChannel: enumPmuChannel, ePmuTriggerMode: enumPmuTriggerMode, samples: int, averages: int):
    return bool(_pmusetup(ePumChannel, ePmuTriggerMode, samples, averages))


## <summary>	Configure the ADC for the acquisition of the Average value.</summary>
# 				Also the Max value can be acquired (in this case
#				the averages parameter is 16384 measurements equals to 819.2uS ).</summary>
#
# <param name="ePumChannel"> 	The channel to measure.</param>
# <param name="ePmuTriggerMode"> 	The trigger mode.</param>
#
# <returns>	true if it succeeds, false if it fails.</returns>
# @snippet hs_examples.py Pmu Example
def pmu_average_setup(ePumChannel: enumPmuChannel, ePmuTriggerMode: enumPmuTriggerMode):
    return bool(_pmuaveragesetup(ePumChannel, ePmuTriggerMode))


## <summary>	Configure the ADC for the acquisition of the Max value.
# 			Also the average value can be acquired. See PmuAverageSetup</summary>
#
# <param name="ePmuChannel"> 		The channel to measure.</param>
# <param name="ePmuTriggerMode"> 	The trigger mode.</param>
# <param name="averages"> 			Number of averages for each sample (50nS).</param>
#
# <returns>	true if it succeeds
#             false ePmuChannel is not valid index
#             false ePmuTriggerMode is not valid index
#             false averages exceeds 2^20
# </returns>
# @snippet hs_examples.py Pmu Example
def pmu_max_setup(ePmuChannel: enumPmuChannel, ePmuTriggerMode: enumPmuTriggerMode, averages: int):
    return bool(_pmumaxsetup(ePmuChannel, ePmuTriggerMode, averages))


## <summary>	Start an acquisition by software command.</summary>
#
# <param name="wait"> 	If true, it waits for acquisition ends.</param>
#
# <returns>	true, no fails.</returns>
# @snippet hs_examples.py Pmu Example
def pmu_start(wait: bool):
    return bool(_pmustart(int(wait)))


## <summary>	Stop the acquisition by software command.</summary>
#
# <param name="wait"> 	If true, it waits for acquisition ends.</param>
#
# <returns>	true, no fails.</returns>
# @snippet hs_examples.py Pmu Example
def pmu_stop(wait: bool):
    return bool(_pmustop(int(wait)))


## <summary>	Get the last measured samples.</summary>
#
# <param name="aValues"> 	The array to store the current measures in A.</param>
# <param name="samples"> 	Number of samples to acquire.</param>
#
# <returns>		true if it succeeds
#            	false if afValues is null
#				false if the number of measurement per sample is 0
#				false if there are no samples
# </returns>
# @snippet hs_examples.py Pmu Example
def pmu_get_samples(aValues, samples: int):
    bafValues = af2b(aValues)
    adrlSamples = get_adr_buf_for_int()
    bres = _pmugetsamples(bafValues[0], samples, adrlSamples[0])
    b2af(bafValues[1], aValues)
    lSamples = get_int_from_bytes(adrlSamples[1])
    return bool(bres), lSamples


## <summary>	Get the measure average between start and stop command.</summary>
#
# <returns>
#			[bres]  true, no fails.
#			[averageMeasure] the average value in mA
# </returns>
# @snippet hs_examples.py Pmu Example
def pmu_get_average_of_samples():
    averageMeasurebuf = get_adr_buf_for_float()
    bres = _pmugetaverageofsamples(averageMeasurebuf[0])
    averageMeasure = get_float_from_bytes(averageMeasurebuf[1])
    return bool(bres), float(averageMeasure)


## <summary>	Get the measure maximum between start and stop command.</summary>
#
# <returns>
#			[bres]  true if it succeeds, false if the number of measurement per sample is 0, false if the ADC read less measurement than Averages setup by PmuSetup.
#			[maxMeasure] the maximum measure
# </returns>
# @snippet hs_examples.py Pmu Example
def pmu_get_max_of_samples():
    maxMeasurebuf = get_adr_buf_for_float()
    bres = _pmugetmaxofsamples(maxMeasurebuf[0])
    maxMeasure = get_float_from_bytes(maxMeasurebuf[1])
    return bool(bres), maxMeasure


## <summary>	Query the PMU status.</summary>
#
# <returns>	the Combination of PmuStatusMask.</returns>
# @snippet hs_examples.py Pmu Example
def pmu_get_status_mask():
    return int(_pmugetstatusmask())


## <summary>	Sets a max limit for the socket temperature, if this limit is reached the header will be turned off.</summary>
#
# <param name="maxLimit">	the temperatrure max limit.</param>
#
# <returns>	true if it succeeds, false if it fails.</returns>
def temperature_set_limit(maxLimit: float):
    return bool(_temperaturesetlimit(maxLimit))


## <summary>	Query if the socket temperature is over the max limit.</summary>
#
# <returns>	true if the temperature is over the limit, false if there isn't alert.</returns>
def is_temperature_alert():
    return bool(_istemperaturealert())


##<summary>	Retrieves the min data rate ( MTs) allowed.</summary>
#
# @ingroup wsApiGroup
def min_datarate_MTs():
    return MIN_DATA_RATE_MTS


##<summary>	Retrieves the max data rate ( MTs) allowed.</summary>
#
# @ingroup wsApiGroup
def max_datarate_MTs():
    return MAX_DATA_RATE_MTS


##<summary>	Retrieves the default data rate ( MTs) allowed.</summary>
#
# @ingroup wsApiGroup
def default_datarate_MTs():
    return DEFAULT_DATA_RATE_MTS

##<summary>	Queries if NanoCycler version is HS.</summary>
#
# @ingroup wsApiGroup
def is_nanocycler_hs():
    return True



##<summary> Defines the pattern types implemented by FPGA.</summary>
#
# @ingroup wsApiGroup
class enumPatternType:
    ## <summary>	An enum constant representing Pattern All0 </summary>
    All0 = 0
    ## <summary>	An enum constant representing Pattern All1 </summary>
    All1 = 1
    ## <summary>	An enum constant representing User Buffer. The buffer size is limited to 64 Kbytes.
    User = 2
    ##<summary>	An enum constant representing Pattern Counter
    #					Byte0 -> 0x00, Byte 1 -> 0x01, … Byte 255 -> 0xFF</summary>
    Counter = 3
    ## <summary>	An enum constant representing Pattern Random
    # 					Random generator based on FLSR and different seed per page and cycle;
    # 					same page at same cycle in different block will use the same pattern</summary>
    Random = 4
    ## <summary>	An enum constant representing Pattern Alternate (0x55 0xAA) </summary>
    Alt_55_AA = 5
    ## <summary>	An enum constant representing Pattern Alternate (0xFF 0x00) </summary>
    Alt_FF_00 = 6

    # ! @cond Doxygen_Suppress
    NoPattern = 255
	# ! @endcond


# ! @cond Doxygen_Suppress
_onfilib_handle = CLibManagerSingleton.load_libonfilib()

_init_onfilib = CLibManagerSingleton.load_func(_onfilib_handle, "b", "Init", "")
_terminate_onfilib = CLibManagerSingleton.load_func(_onfilib_handle, "b", "Terminate", "")
_selectchannel = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SelectChannel", "L")
_selectce = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SelectCe", "L")
_getselectedchannel = CLibManagerSingleton.load_func(_onfilib_handle, "b", "GetSelectedChannel", "p")
_isrnbtimeout = CLibManagerSingleton.load_func(_onfilib_handle, "b", "IsRnBTimeout", "")
_setpattern = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetPattern", "iLL")
_setpatternuserbuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetPatternUserBuffer", "pL")
_setpatternuserbufferword = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetPatternUserBufferWord", "LL")
_getpatternseed = CLibManagerSingleton.load_func(_onfilib_handle, "q", "GetPatternSeed", "LL")
_setcomparemask = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetCompareMask", "B")
_setchunklength = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetChunkLength", "L")
_setreadlatencyclcyles = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetReadLatencyCycles", "L")
_setwritelatencyclcyles = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetWriteLatencyCycles", "L")
_setruntablecemask = CLibManagerSingleton.load_func(_onfilib_handle, "b", "SetRunTableCeMask", "B")
_vectorramreset = CLibManagerSingleton.load_func(_onfilib_handle, "b", "VectorRamReset", "")
_vectorramaddtable = CLibManagerSingleton.load_func(_onfilib_handle, "b", "VectorRamAddTable", "pp")
_calibrate = CLibManagerSingleton.load_func(_onfilib_handle, "b", "Calibrate", "LLb")
_calibrategetreadwindow = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CalibrateGetReadWindow", "p")
_calibrategetwritewindow = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CalibrateGetWriteWindow", "p")
_customsequencesetopcoderamdata = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceSetOpCodeRamData", "LL")
_customsequencerun = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceRun", "pbL")
_customsequencerunparallel = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceRunParallel", "pLbL")
_customsequencerunext = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceRunExt", "pLLbL")
_customsequenceisrunning = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceIsRunning", "")
_customsequencegetbusytime = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetBusyTime", "p")
_customsequencegetwritetobusytime = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetWriteToBusyTime", "p")
_customsequencegetfailcount = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetFailCount", "p")
_customsequencegetfailcount4chunk = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetFailCount4Chunk", "Lp")
_customsequencegetbusytime4chunk = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetBusyTime4Chunk", "Lp")
_customsequencegetfail0count = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetFail0Count", "p")
_customsequencegetfail1count = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetFail1Count", "p")
_customsequencegetread0count = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetRead0Count", "p")
_customsequencegetread1count = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetRead1Count", "p")
_customsequencegetreadbytecount = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetReadByteCount", "p")
_customsequencegetoutbufferword = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetOutBufferWord", "Lp")
_customsequencegetoutbuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceGetOutBuffer", "pL")
_customsequencecopyoutbuffer2dmabuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceCopyOutBuffer2DmaBuffer", "L")
_customsequencesetinbufferword = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceSetInBufferWord", "LL")
_customsequencesetinbuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceSetInBuffer", "pL")
_customsequencecopydmabuffer2inbuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "CustomSequenceCopyDmaBuffer2InBuffer", "L")
_pagebufferwritebuilder = CLibManagerSingleton.load_func(_onfilib_handle, "b", "PageBufferWriteBuilder", "p")
_pagebufferreadbuilder = CLibManagerSingleton.load_func(_onfilib_handle, "b", "PageBufferReadBuilder", "p")
_randomfillexpectedbuffer = CLibManagerSingleton.load_func(_onfilib_handle, "b", "RandomFillExpectedBuffer", "LLpL")
# ! @endcond


## <summary>	Select the channel ( internal chip ).
# 					Currently only two are supported.</summary>
#
# <param name="channel">	The channel index.</param>
#
# <returns>	true if it succeeds,
# 			false if no channel is selected.
#			false if lChannel is higher than 2.
# </returns>
# @snippet std_examples.py Onfi select_channel
def select_channel(channel: int):
    bRes = _selectchannel(channel)
    return bool(bRes)


## <summary>	Returns the selected channel ( internal chip ).</summary>
#
# <returns>	(bRes, channel) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           channel : the selected channel. \n
# </returns>
def get_selected_channel():
    adrBufChannel = get_adr_buf_for_int()
    bRes = _getselectedchannel(adrBufChannel[0])
    channel = get_int_from_bytes(adrBufChannel[1])
    return bool(bRes), int(channel)

## <summary>	Select next target ce in order to apply the right calibration paramters.
#
# <param name="channel">	The ce index.</param>
#
# <returns>	true if it succeeds,
#			false if channel is higher than 4.
# </returns>
# @snippet std_examples.py Onfi select_channel
def select_ce(ce: int):
    bRes = _selectce(ce)
    return bool(bRes)


## <summary> Check if the timeout expires for the last ready busy operation. </summary>
#
# <returns>	true if timeout expired, false if timeout is not expired, false if no channel is selected.</returns>
def is_rnb_timeout():
    bRes = _isrnbtimeout()
    return bool(bRes)



## <summary>   Sets up the pattern to be used in the next tests.</summary>
#
# <param name="pattern_type">   The pattern type.</param>
# <param name="seed_index">   the seed index used for random generator.</param>
# <param name="seed_cycle">   the seed cycle used for random generator.</param>
#
# <returns>   true if it succeeds,
#             false if no channel is selected.
# @snippet std_examples.py Onfi set_pattern
def set_pattern(pattern_type: enumPatternType, seed_index: int, seed_cycle: int):
    bRes = _setpattern(pattern_type, seed_index, seed_cycle)
    return bool(bRes)


## <summary>   Sets up the user pattern buffer to be used according with SetPattern.</summary>
#
# <param name="byte_array">   array of data to be written.</param>
# <param name="byte_length">   byte length of the array.</param>
#
# <returns>   true if it succeeds,
#             false if no channel is selected.
# </returns>
# @snippet std_examples.py Onfi set_pattern
def set_pattern_user_buffer(byte_array: bytearray, byte_length: int):
    abUserBuffer = address_of(byte_array)
    lUserBufferLength = byte_length
    bRes = _setpatternuserbuffer(abUserBuffer, lUserBufferLength)
    return bool(bRes)


## <summary>   Sets up the word index of user pattern buffer to be used according with SetPattern.</summary>
#
# <param name="index">   index of word to set up.</param>
# <param name="data">   data to set.</param>
#
# <returns>   true if it succeeds,
#             false if no channel is selected.
# </returns>
# @snippet std_examples.py Onfi set_pattern
def set_pattern_user_buffer_word(index: int, data: int):
    bRes = _setpatternuserbufferword(index, data)
    return bool(bRes)


## <summary>   Returns the seed values used for random pattern generation.</summary>
#
# <param name="seed_index">   the seed index used for random generator.</param>
# <param name="seed_cycle">   the seed cycle used for random generator.</param>
#
def get_pattern_seed(seed_index: int, seed_cycle: int):
    llseed = _getpatternseed(int(seed_index), int(seed_cycle))
    lseedhigh = (llseed >> 32) & 0xFFFFFFFF
    lseedlow = llseed & 0xFFFFFFFF
    return lseedhigh, lseedlow


## <summary>	Sets up the mask for pattern matching.</summary>
#
# <param name="mask">	the binary mask to enable pattern matching. A 1 in bit of position (n) enable comparing of DQ(n). </param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def set_compare_mask(mask: int):
    bRes = _setcomparemask(mask)
    return bool(bRes)


## <summary>	Sets the chunk length to extract fails for each chunk.</summary>
#
# <param name="length">	length in byte of the chunk. </param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def set_chunk_length(length: int):
    bRes = _setchunklength(length)
    return bool(bRes)

## <summary>	Sets the number of read latency cycle (RE pulse) to apply during DDR DATA OUT.</summary>
#
# <param name="cycles">	the number of cycles. </param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def set_read_latency_cycles(cycles: int):
    bRes = _setreadlatencyclcyles(cycles)
    return bool(bRes)


## <summary>	Sets the number of write latency cycle (DQS pulse) to apply during DDR DATA IN.</summary>
#
# <param name="cycles">	the number of cycles. </param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def set_write_latency_cycles(cycles: int):
    bRes = _setwritelatencyclcyles(cycles)
    return bool(bRes)


## <summary>	Sets up the CE mask for run table back door.</summary>
#
# <param name="ceMask">	the binary mask to enable CE index. </param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if bCeMask is higher than 0xF.</returns>
def set_run_table_ce_mask(ceMask):
    bRes = _setruntablecemask(ceMask)
    return bool(bRes)


## <summary> For timing analysis, it clears the vector ram.</summary>
#
# <returns>	true if it succeeds, false if no channel is selected, false if loading fails.</returns>
def vector_ram_reset():
    bRes = _vectorramreset()
    return bool(bRes)


## <summary>	For timing analysis, it loads a sequence table into the vector ram.
#				it returns the reference to use in the OpCodeBuilder (BD_RUN_TABLE).</summary>
#
# <param name="opSequenceTable">	[in] The vector sequence table to execute.</param>
#
# <returns>	(bRes, lTableReference) \n
#			bRes : true if it succeeds, false if it fails.\n
#			lTableReference : return table reference.\n
# </returns>
def vector_ram_add_table(opSequenceTable):
    addrTableReference = get_adr_buf_for_int()
    bRes = _vectorramaddtable(opSequenceTable.get_ptr(), addrTableReference[0])
    lTableReference = get_int_from_bytes(addrTableReference[1])
    return bool(bRes), lTableReference


## <summary>	Executes the calibrate operation.</summary>
#
# <param name="ceIndex">	Zero-based index of the CE.</param>
# <param name="byteNumber">	Number of bytes to read.</param>
# <param name="bVerbose">	(Optional) true to verbose - [true for default].</param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if ceIndex is higher than 4, false if byteNumber exceeds 65536,	false if Calibration fails. See CalibrateGetReadWindow / CalibrateGetWriteWindow.</returns>
# @snippet std_examples.py Onfi calibrate
def calibrate(ceIndex: int, byteNumber: int, bVerbose: bool = True):
    bRes = _calibrate(ceIndex, byteNumber, int(bVerbose))
    return bool(bRes)


## <summary>	Get calibrate read window.</summary>
#
# <returns>	(bRes, calibWindow) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#			calibWindow : return calibration window.\n
# </returns>
def calibrate_get_read_window():
    aBuffer = bytearray(4 * 8)
    addrlCalibWindow = address_of(aBuffer)
    bRes = _calibrategetreadwindow(addrlCalibWindow)
    aResBuffer = [0] * 8
    for i in range(0, 8):
        base_index = i * 4
        aResBuffer[i] = get_int_from_bytes(aBuffer[base_index: base_index + 4])
    return bool(bRes), aResBuffer


## <summary>	Get calibrate write window.</summary>
#
# <returns>	(bRes, calibWindow) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#			calibWindow : return calibration window.\n
# </returns>
def calibrate_get_write_window():
    aBuffer = bytearray(4 * 8)
    addrlCalibWindow = address_of(aBuffer)
    bRes = _calibrategetwritewindow(addrlCalibWindow)
    aResBuffer = [0] * 8
    for i in range(0, 8):
        base_index = i * 4
        aResBuffer[i] = get_int_from_bytes(aBuffer[base_index: base_index + 4])
    return bool(bRes), aResBuffer

## <summary>	write opcode parameter value into register to be accessed during OpCodeBuilder execution by FPGA.</summary>
#
# <param name="index">	[in] index of reg (0..3) </param>
# <param name="data">	[in] Data value - 32 bits </param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def custom_sequence_set_opcode_ram_data(index: int, data: int):
    bRes = _customsequencesetopcoderamdata(index, data)
    return bool(bRes)


## <summary>	Executes the run operation.</summary>
#
# <param name="opCodeBuilder">	[in] The op-code builder containing all back-door codes to execute.</param>
# <param name="wait">		 	[in] (Optional) Specify if wait for execution end or just start and return.</param>
# <param name="mSecTimeout">	[in] (Optional) In case of wait, It specify the millisecond timeout.</param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if opCodeBuilder exceeds OP_CODE_MAX_COUNT (1024) codes,	false if opCodeBuilder contains BD_DDR_DATA_OUT or BD_DDR_DATA_IN with length higher than 65536.</returns>
# @snippet std_examples.py Onfi custom_sequence_run
def custom_sequence_run(opCodeBuilder, wait: bool = True, mSecTimeout: int = 5000):
    bRes = _customsequencerun(opCodeBuilder.get_ptr(), int(wait), mSecTimeout)
    return bool(bRes)


## <summary>	Executes the run operation in a one or more ce.</summary>
#
# <param name="opCodeBuilder">	[in] The op-code builder containing all back-door codes to execute.</param>
# <param name="ceMask">			[in] CE mask, F means all ce are selected; 0x0 no ce are selected.</param>
# <param name="wait">		 	[in] (Optional) Specify if wait for execution end or just start and return.</param>
# <param name="mSecTimeout">	[in] (Optional) In case of wait, It specify the millisecond timeout.</param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if ceMask is higher than 0xF, false if opCodeBuilder exceeds OP_CODE_MAX_COUNT (128) codes, false if opCodeBuilder contains BD_DDR_DATA_OUT or BD_DDR_DATA_IN with length higher than 65536.</returns>
def custom_sequence_run_parallel(opCodeBuilder: opcodebuilder, ceMask: int, wait: bool = True,
                                 mSecTimeout: int = 5000):
    bRes = _customsequencerunparallel(opCodeBuilder.get_ptr(), ceMask, int(wait), mSecTimeout)
    return bool(bRes)


## <summary>	Executes the run operation in a multiple channel and ce.</summary>
#
# <param name="opCodeBuilder">	[in] The op-code builder containing all back-door codes to execute.</param>
# <param name="chMask">			[in] CE mask, 3 means both ch are selected; 0x0 no ce are selected.</param>
# <param name="ceMask">			[in] CE mask, F means all ce are selected; 0x0 no ce are selected.</param>
# <param name="wait">		 	[in] (Optional) Specify if wait for execution end or just start and return.</param>
# <param name="mSecTimeout">	[in] (Optional) In case of wait, It specify the millisecond timeout.</param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if ceMask is higher than 0xF, false if opCodeBuilder exceeds OP_CODE_MAX_COUNT (1024) codes, false if opCodeBuilder contains BD_DDR_DATA_OUT or BD_DDR_DATA_IN with length higher than 65536.</returns>
def custom_sequence_run_ext(opCodeBuilder: opcodebuilder, chMask: int, ceMask: int, wait: bool = True,
                            mSecTimeout: int = 5000):
    bRes = _customsequencerunext(opCodeBuilder.get_ptr(), chMask, ceMask, int(wait), mSecTimeout)
    return bool(bRes)


## <summary>	Query if there is an operation running on selected channel.</summary>
#
# <returns>	true if an operation is running, false if the execution is completed, false if no channel is selected.</returns>
def custom_sequence_is_running():
    bRes = _customsequenceisrunning()
    return bool(bRes)


## <summary>	Executes the get busy time operation.</summary>
#
# <returns>	(bRes, busyTimeNsec) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           busyTimeNsec : the measured busy time nsec.\n
# </returns>
def custom_sequence_get_busy_time():
    adrBuflBusyTime = get_adr_buf_for_int()
    bRes = _customsequencegetbusytime(adrBuflBusyTime[0])
    lBusyTime = get_int_from_bytes(adrBuflBusyTime[1])
    return bool(bRes), lBusyTime


## <summary>	Executes the get WB (Write to Busy) time operation.</summary>
#
# <returns>	(bRes, writeToBusyTimeNsec) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           writeToBusyTimeNsec : the measured WB time nsec.\n
# </returns>
def custom_sequence_get_write_to_busy_time():
    adrBuflWriteToBusyTime = get_adr_buf_for_int()
    bRes = _customsequencegetwritetobusytime(adrBuflWriteToBusyTime[0])
    lWriteToBusyTime = get_int_from_bytes(adrBuflWriteToBusyTime[1])
    return bool(bRes), lWriteToBusyTime


## <summary>	Returns the fail count.</summary>
#
# <returns>	(bRes, failCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           failCount : number of fails count.\n
# </returns>
def custom_sequence_get_fail_count():
    adrBuflFailCount = get_adr_buf_for_int()
    bRes = _customsequencegetfailcount(adrBuflFailCount[0])
    fail_count = get_int_from_bytes(adrBuflFailCount[1])
    return bool(bRes), fail_count


## <summary>	Returns the fail count for a specific chunk index.</summary>
#
# <param name="chunk">	[in] The chunk index.</param>
# <returns>	(bRes, failCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           failCount : number of fails count.\n
# </returns>
def custom_sequence_get_fail_count_4_chunk(chunk):
    adrBuflFailCount = get_adr_buf_for_int()
    bRes = _customsequencegetfailcount4chunk(chunk, adrBuflFailCount[0])
    fail_count = get_int_from_bytes(adrBuflFailCount[1])
    return bool(bRes), fail_count


## <summary>	Returns the busy time in nanosecond for a specific chunk index.</summary>
#
# <param name="chunk">	[in] The chunk index.</param>
# <returns>	(bRes, busy_time) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           busy_time : the busy time in nano second.\n
# </returns>
def custom_sequence_get_busy_time_4_chunk(chunk):
    adrBusyTime = get_adr_buf_for_int()
    bRes = _customsequencegetbusytime4chunk(chunk, adrBusyTime[0])
    busy_time = get_int_from_bytes(adrBusyTime[1])
    return bool(bRes), busy_time


## <summary>	Returns the number of fails versus 0 expected in the last compare. Detects all bits changed from 0 to 1 </summary>
#
# <returns>	(bRes, failCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           failCount : number of fails count.\n
# </returns>
def custom_sequence_get_fail0_count():
    adrBuflFailCount = get_adr_buf_for_int()
    bRes = _customsequencegetfail0count(adrBuflFailCount[0])
    fail_count = get_int_from_bytes(adrBuflFailCount[1])
    return bool(bRes), fail_count


## <summary>	Returns the number of fails versus 1 expected in the last compare. Detects all bits changed from 1 to 0 </summary>
#
# <returns>	(bRes, failCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           failCount : number of fails count.\n
# </returns>
def custom_sequence_get_fail1_count():
    adrBuflFailCount = get_adr_buf_for_int()
    bRes = _customsequencegetfail1count(adrBuflFailCount[0])
    fail_count = get_int_from_bytes(adrBuflFailCount[1])
    return bool(bRes), fail_count


## <summary>	Returns the number of bit read at 0 in the last compare </summary>
#
# <returns>	(bRes, bitCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           bitCount : number of 0.\n
# </returns>
def custom_sequence_get_read0_count():
    adrBufBitCount = get_adr_buf_for_int()
    bRes = _customsequencegetread0count(adrBufBitCount[0])
    lBitCount = get_int_from_bytes(adrBufBitCount[1])
    return bool(bRes), lBitCount


## <summary>	Returns the number of bit read at 1 in the last compare </summary>
#
# <returns>	(bRes, bitCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           bitCount : number of 1.\n
# </returns>
def custom_sequence_get_read1_count():
    adrBufBitCount = get_adr_buf_for_int()
    bRes = _customsequencegetread1count(adrBufBitCount[0])
    lBitCount = get_int_from_bytes(adrBufBitCount[1])
    return bool(bRes), lBitCount


## <summary>	Returns the number of read/compared byte.</summary>
#
# <returns>	(bRes, byteCount) \n
#			bRes : true if it succeeds, false if no channel is selected.\n
#           byteCount : the number of byte count.\n
# </returns>
def custom_sequence_get_readbyte_count():
    adrBuflReadByteCount = get_adr_buf_for_int()
    bRes = _customsequencegetreadbytecount(adrBuflReadByteCount[0])
    lReadByteCount = get_int_from_bytes(adrBuflReadByteCount[1])
    return bool(bRes), lReadByteCount


## <summary>	Gets the data from Internal BRAM output.
#				The internal RAM size is 65536 byte or 16384 words.
#				If the running OpCodeBuilder contains BD_DDR_DATA_OUT / BD_SDR_DATA_OUT
# 		        the data can be read
#  </summary>
#
# <param name="index">	The index of 32bit data.</param>
#
# <returns>	(bRes, lData) \n
#			bRes : true if it succeeds, false if no channel is selected, false if lIndex exceeds 65536/4.\n
#           lData : the 32-bit data.\n
# </returns>
def custom_sequence_get_out_buffer_word(index: int):
    adrBuflOutBufferWord = get_adr_buf_for_int()
    bRes = _customsequencegetoutbufferword(index, adrBuflOutBufferWord[0])
    lOutBufferWord = get_int_from_bytes(adrBuflOutBufferWord[1])
    return bool(bRes), lOutBufferWord


## <summary>	Gets the internal BRAM out into the input buffer.
#				It performs a dma transfer from BRAM out to the dma buffer than a memcpy from DMA buffer to pbData.
#				The BRAM and DMA size is 65536 byte.
#  </summary>
#
# <param name="length">	The number of bytes to read.</param>
#
# <returns>	(bRes, byBuffer) \n
#			bRes : true if it succeeds, false if no channel is selected, false if length exceeds 65536.\n
#           byBuffer : bytearray buffer of length elements.\n
# </returns>
def custom_sequence_get_out_buffer(length: int):
    aBuff = bytearray(length)
    aBuffAddr = address_of(aBuff)
    bRes = _customsequencegetoutbuffer(aBuffAddr, length)
    return bool(bRes), aBuff


## <summary>	It performs dma transfer from BRAM output to the DMA buffer.
#				The BRAM and DMA size is 65536 byte.
#  </summary>
#
# <param name="length">	The number of bytes to copy.</param>
#
# <returns>	bRes \n
#			bRes : true if it succeeds, false if no channel is selected, false if length exceeds 65536, false if DMA transfer fails.\n
# </returns>
def custom_sequence_copy_out_buffer_2_dma_buffer(length: int):
    bRes = _customsequencecopyoutbuffer2dmabuffer(length)
    return bool(bRes)


## <summary>	Sets-in data to Device.</summary>
#
# <param name="index">	The index of 32bit data.</param>
# <param name="data"> 	The 32 bits data to write.</param>
#
# <returns>	true if it succeeds, false if no channel is selected, false if index exceeds 65536/4.</returns>
# @snippet std_examples.py Onfi custom_sequence_set_in_buffer_word
def custom_sequence_set_in_buffer_word(index: int, data: int):
    bRes = _customsequencesetinbufferword(index, data)
    return bool(bRes)


## <summary>	Sets-in buffer - data into Intenal BRAM Input.</summary>
#				It performs a memcpy from pbData to DMA buffer than a dma transfer to the BRAM In
#				The BRAM and DMA size is 65536 byte.
# <param name="byte_array"> 	bytearray buffer of length data to write</param>
# <param name="length">	The number of byte to write.</param>
#
# <returns>	true if it succeeds, false if no channel is selected.</returns>
def custom_sequence_set_in_buffer(byte_array: bytearray, length: int):
    lDataAddr = address_of(byte_array)
    bRes = _customsequencesetinbuffer(lDataAddr, length)
    return bool(bRes)


## <summary>	It performs dma transfer from DMA buffer to the BRAM Input
#				The BRAM and DMA size is 65536 byte.
#  </summary>
#
# <param name="length">	The number of bytes to copy.</param>
#
# <returns>	bRes \n
#			bRes : true if it succeeds, false if no channel is selected, false if DMA transfer fails.\n
# </returns>
def custom_sequence_copy_dma_buffer_2_in_buffer(length: int):
    bRes = _customsequencecopydmabuffer2inbuffer(length)
    return bool(bRes)


## <summary>	Specify the page buffer write sequence to use during calibration.</summary>
#
# <param name="opCodeBuilder">	[in] The op-code builder containing all back-door codes for page buffer write sequence.</param>
#
# <returns>	always true.</returns>
# @snippet std_examples.py Onfi pagebuffer_builder
def page_buffer_write_builder(opCodeBuilder: opcodebuilder):
    return bool(_pagebufferwritebuilder(opCodeBuilder.get_ptr()))


## <summary>	Specify the page buffer read sequence to use during calibration.</summary>
#
# <param name="opCodeBuilder">	[in] The op-code builder containing all back-door codes for page buffer read sequence.</param>
#
# <returns>	always true.</returns>
# @snippet std_examples.py Onfi pagebuffer_builder
def page_buffer_read_builder(opCodeBuilder: opcodebuilder):
    return bool(_pagebufferreadbuilder(opCodeBuilder.get_ptr()))


## <summary>	Fill a buffer with expected random pattern.</summary>
#
# <param name="lSeedIndex">	[in] the seed index used for random generator.</param>
# <param name="lSeedCycle">	[in] the seed cycle used for random generator.</param>
# <param name="length">	[in] byte length of the buffer to fill in.</param>
#
# <returns>	always true.</returns>
def random_fill_expected_buffer(lSeedIndex, lSeedCycle, length):
    aBuff = bytearray(length)
    aBuffAddr = address_of(aBuff)
    bRes = _randomfillexpectedbuffer(lSeedIndex, lSeedCycle, aBuffAddr, length)
    return bool(bRes), aBuff


## <summary>	Return a byte read in sdr mode from the devide.</summary>
#
# <param name="byte_index">	[in] the index of the byte to return.</param>
#
# <returns>	data byte.</returns>
def custom_sequence_sdr_get_data_byte(byte_index):
    idx = byte_index * 2 # 2 bytes, one for each edge
    word_index = idx // 4
    byte_in_word_index = idx % 4
    shift = 8 * byte_in_word_index
    res, data = custom_sequence_get_out_buffer_word(word_index)
    return (data >> shift) & 0xFF


## <summary>	Return a byte read in ddr mode from the devide.</summary>
#
# <param name="byte_index">	[in] the index of the byte to return.</param>
#
# <returns>	data byte.</returns>
def custom_sequence_ddr_get_data_byte(byte_index):
    word_index = byte_index // 4
    byte_in_word_index = byte_index % 4
    shift = 8 * byte_in_word_index
    res, data = custom_sequence_get_out_buffer_word(word_index)
    return (data >> shift) & 0xFF

## <summary>	Enables or disables the sequence recording and set the sequence name.</summary>
#
# <param name="enable">	true to enable, false to disable.</param>
# <param name="sequence_name">	sequence name.</param>
#
def sequence_recording_enable(enable: bool, sequence_name: str):
    _enablesequencerecording(int(enable), sequence_name)
