import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM






############################################################
### the Device generic class
############################################################

class CTLCDevice(COnfiDevice):
    """This is the base class for a generic Nand device. Each device class should inherit from this class.
     It contains the default implementation of several Nand function. Overriding a method it is possible to
     customize the implementation of a function for a specific device.
      """
    def __init__(self):
        COnfiDevice.__init__(self)
        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_MANUFACTURER = "NandManufacturer"
        self.DEVICE_NAME = "TLCDevice"
        self.LEVEL_NUMBER = 3  # TLC 3 levels

        # ##########################################################
        # # change this configuration according device parameters
        # Toshiba Bics3 like
        self.DEVICE_CE_NUMBER = 2
        self.toshiba_like = True
        self.PAGE_LENGTH = 18336
        self.PLANE_NUMBER = 2
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.WL_NUMBER = 256
        self.PAGE_NUMBER = self.LEVEL_NUMBER * self.WL_NUMBER
        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 8
        self.VALID_LUN_MASK = 0x01
        self.VALID_PAGE_MASK = 0x0FF
        self.VALID_BLOCK_MASK = 0x1FFF
        self.PAGE_PARAM_ADDR = 0x40
        # ##########################################################

        # ##########################################################
        # # change this configuration according device parameters
        # Toshiba Bics4 like
        # self.DEVICE_CE_NUMBER = 2
        # self.toshiba_like = True
        # self.PAGE_LENGTH = 18336
        # self.PLANE_NUMBER = 2
        # self.LUN_NUMBER = 2
        # self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        # self.WL_NUMBER = 384
        # self.PAGE_NUMBER = self.LEVEL_NUMBER * self.WL_NUMBER
        # self.LUN_START_BIT_ADDRESS = 20
        # self.BLOCK_START_BIT_ADDRESS = 9
        # self.VALID_LUN_MASK = 0x03
        # self.VALID_PAGE_MASK = 0x1FF
        # self.VALID_BLOCK_MASK = 0x1FFF
        # self.PAGE_PARAM_ADDR = 0x40
        # ##########################################################

        ##########################################################
        # change this configuration according device parameters
        # Micron B27B like
        # self.DEVICE_CE_NUMBER = 1
        # self.toshiba_like = False
        # self.PAGE_LENGTH = 18592
        # self.PLANE_NUMBER = 4
        # self.LUN_NUMBER = 1
        # self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        # self.WL_NUMBER = 1152
        # self.PAGE_NUMBER = 3456
        # self.LUN_START_BIT_ADDRESS = 23
        # self.BLOCK_START_BIT_ADDRESS = 12
        # self.VALID_LUN_MASK = 0x00
        # self.VALID_PAGE_MASK = 0xFFF
        # self.VALID_BLOCK_MASK = 0x7FF
        # self.PAGE_PARAM_ADDR = 0x00
        ##########################################################

        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.MAX_ERASE_TIME = 15000 # 15 msec
        self.MAX_PROG_TIME = 10000 # 10 msec
        self.MAX_READ_TIME = 1000 # 1 msec

        self.VCC = 2.5
        self.VCCQ = 1.2


    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list: []):

        for page in page_list:
            self.program_page(ch_list, ce_list, lun_list, block, page, 0)


    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):

        for page in page_list:
            self.multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0)



    # #################################################
    # ERASE

    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block], en_i=bI3)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                opcbuilder.set_row_address(row_address)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    # #################################################
    # PROGRAM

    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre

        # pattern is the same between ce, and lun: use the first
        self.recall_pattern(ch_list, ce_list[0], lun_list[0], block, page)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # record I4w only on the last lun
            bI4w = self.pmu_algo_is_I4w()
            if lun_idx != (len(lun_list) - 1):
                bI4w = False

            res, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if self.toshiba_like and (page % self.LEVEL_NUMBER + 1) < self.LEVEL_NUMBER:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        if bI2 or bI4w:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # record I2 and I4w only for upper page (2) of the last block
                bI2 = self.pmu_algo_is_I2()
                bI4w = self.pmu_algo_is_I4w()
                if (page % self.LEVEL_NUMBER) != (self.LEVEL_NUMBER - 1) or block_idx != (len(block_list) - 1):
                    bI2 = False
                    bI4w = False

                res, row_address = self.build_row_address(lun, block, page)

                # pattern is the same between ce  and channel, use the first
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2) # 2 to use register seed

                if self.toshiba_like:
                    opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.set_row_address(row_address)
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
                # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if self.toshiba_like and (page % self.LEVEL_NUMBER) < (self.LEVEL_NUMBER - 1):
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)

                if bI2:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                    # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                if bI2:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)

            if (page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1):
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()
                logger.log_program("MP-PRG", ch_list, ce_list, [lun], block_list, page, rbTimeName="tPROG", en_i=bI2 or bI4w)


        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # log SR only if log is enabled, page is upper page and we want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1)):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    # #################################################
    # PAGE READ

    def get_read_buffer(self, ch, buffer_length):
        hw.select_channel(ch)
        bRes, buffer = hw.custom_sequence_get_out_buffer(buffer_length)
        return buffer

    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        if self.toshiba_like:
            opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # the sequence will be executed by run_parallel in order to run time change the ce
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start
        if self.toshiba_like:
            opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        if bI4r:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        if bI4r:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)

        opcbuilder.cleanup()


    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list : [], page, column, page_length):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName="tR", en_fails=False, en_rnb=True,
                        en_i=bI1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False, en_i=bI4r)

        opcbuilder.cleanup()
