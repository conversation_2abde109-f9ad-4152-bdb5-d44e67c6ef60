import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
# from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
# from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.Toshiba import CToshiba as CToshiba


###########################################################################
### Reference Datasheet: TH58VHT4T42VA
###########################################################################

class CToshibaVHT(CToshiba):
    def __init__(self):
        CToshiba.__init__(self)
        self.DEVICE_MANUFACTURER = "Toshiba"
        self.DEVICE_NAME = "VHT"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 2  # plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.WL_NUMBER = 192
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # per block

        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 8
        self.VALID_LUN_MASK = 0x01
        self.VALID_WL_MASK = 0xFF
        self.VALID_BLOCK_MASK = 0x1FFF

        self.VCC = 3.3
        self.VCCQ = 1.2



    # Figure 4‐2. SDR to DDR, DDR to SDR Mode Transition Diagram
    def die_configure(self, ch_list: [], ce_list: [], odt, driver_strength):

        # Driver strength 10h
        # 00h~01h Reserved
        # 02h Ron = Driver Multiplier : Underdrive
        # 03h Reserved
        # 04h Ron = 35 Driver Multiplier : 1 (default)
        # 05h Reserved
        # 06h Ron = Driver Multiplier : Overdrive 1
        # 07h~FFh Reserved
        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        lAddress = 0x10
        COnfiDevice.set_feature(self, ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        bODT = odt
        bRE_C = 1
        bDQS_c = 1
        bVREF = 1
        p1 = (bODT << 4) | (bRE_C << 2) | (bDQS_c << 1) | (bVREF << 0)
        lAddress = 0x02
        COnfiDevice.set_feature(self, ch_list, ce_list, lAddress, p1, p2, p3, p4)


    def identification(self):

        # turn on the device with device specific level
        COnfiDevice.turn_on(self, self.VCC, self.VCCQ)

        # during initilize configure the default (min) frequency
        hw.set_datarate(hw.default_datarate_MTs())

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        # lun_list = range(0, self.LUN_NUMBER)

        # assign default chunk length for each channel
        for ch in ch_list:
            hw.select_channel(ch)
            hw.set_chunk_length(self.CHUNK_LENGTH)

        # reset all
        COnfiDevice.device_reset(self, ch_list, ce_list)

        # on channel 0 program configuration interface address B0h
        p1 = (0x0 << 4) | (0x00 << 2) | (0x01 << 0) # Configuration - B
        COnfiDevice.set_feature(self, [0], ce_list, 0xB0, p1, 0, 0, 0)


        # Don't know why this doesn't work
        # self.die_configure(ch_list, ce_list, 4, 4)

        for ch in ch_list:
            self.die_configure([ch], ce_list, 4, 4)

            for ce in ce_list:
                # Read Device Id
                COnfiDevice.device_id_read(self, [ch], ce, 0, self.DEVICE_ID_LEN)
                # Read page parameters
                COnfiDevice.page_parameter_read(self, [ch], ce, 0)
                # # Read Unique id
                # self.read_unique_id([ch], ce)

        # # after read unique id it is necesarry to reset and reconfigure all die
        # for ch in ch_list:
        #     self.die_configure([ch], ce_list, 4, 4)
        #
        # # before exit perform perform ZQ Calib Long
        # for lun in lun_list:
        #     CBaseDevice.zq_calib_long(self, ch_list, ce_list, lun)

