import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.YMTC import CYMTC as CYMTC



###########################################################################
### Reference Datasheet:
###########################################################################


class CYMTCX19050(CYMTC):
    def __init__(self):
        CYMTC.__init__(self)
        self.DEVICE_MANUFACTURER = "YMTC"
        self.DEVICE_NAME = "X19050"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18432
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 2  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.WL_NUMBER = 384
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 2012 # it is just a number it depends on device size
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # per block

        self.LUN_START_BIT_ADDRESS = 23
        self.BLOCK_START_BIT_ADDRESS = 11
        self.VALID_LUN_MASK = 0x07
        self.VALID_BLOCK_MASK = 0x7FF
        self.VALID_PAGE_MASK = 0x7FF

        self.MAX_ERASE_TIME = 20000  # 20 msec
        self.MAX_PROG_TIME = 4000  # 4 msec
        self.MAX_READ_TIME = 200  # 200 usec

        self.VCC = 2.5
        self.VCCQ = 1.2


    CALIB_LEN = 4096

    def calib_setup(self, data_rate_mhz, calib_length, ce = 0, lun = 0):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(ce)
        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        # self.apply_col_address(opcbuilder, column_address)
        # self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_length)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.page_buffer_write_builder(opcbuilder)

        # X1 9050 requires 0x05 ALE x2 0x0E (NO ROW ADDRESS)
        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        self.apply_col_address(opcbuilder, column_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.page_buffer_read_builder(opcbuilder)

        return True
