import nanocycler

from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
from nanocycler import enumPatternType as ePatternType

from lib.DataMgr import the_data_mgr as data_mgr
from lib.DataMgr import PATTERN_SEED as PATTERN_SEED
from lib.ResultLogger import the_result_logger as logger
from nanocycler import NanoTimer as time

from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

class PATTERN_ALGO :
    """ Codes to describe the pattern algorithm for next operation"""

    RANDOM = 0
    """ A random seed will be generated using the nano second timer in FPGA. It ensure new seed is generated each time """
    USER = 1
    """ A user pattern coming from user buffer can be applied. During the buffer can be loaded forom a file or it can be filled by python code. 
    It is not recommended to use in terms of performance """
    ALL0 = 2
    """ All0 pattern will be used for next operations """
    ALL1 = 3
    """ All1=Erased pattern will be used for next operations """
    ALT_AA_55 = 4
    """ Alternate 55 AA pattern will be used for next operations """
    ALT_FF_00 = 5
    """ Alternate 55 AA pattern will be used for next operations """
    INCREMENTAL = 6
    """ Incremental pattern (0,1,2 ... 254, 255, 0, 1, ..)  will be used for next operations """

    RANDOM_0 = 128
    """ A fixed random seed will be used. It is mandatory for VT dump operation where the seed must be known in order to process the output result"""


class PMU_ALGO:
    """ Codes to describe the ICC-ICCQ-IPP measurement algorithm """

    NoPmu = 0
    """ No current measure trigger """

    Icc1 = 1
    """ it controls pmu trigger position for ICC1 measure """
    Icc2 = 2
    """ it controls pmu trigger position for ICC2 measure """
    Icc3 = 3
    """ it controls pmu trigger position for ICC3 measure """
    Icc4r = 4
    """ it controls pmu trigger position for ICC4r measure """
    Icc4w = 5
    """ it controls pmu trigger position for ICC4w measure """
    Icc5 = 6
    """ it controls pmu trigger position for ICC5 measure """

    IccQ1 = 7
    """ it controls pmu trigger position for ICCQ1 measure """
    IccQ2 = 8
    """ it controls pmu trigger position for ICCQ2 measure """
    IccQ3 = 9
    """ it controls pmu trigger position for ICCQ3 measure """
    IccQ4r = 10
    """ it controls pmu trigger position for ICCQ4 measure """
    IccQ4w = 11
    """ it controls pmu trigger position for ICCQ4 measure """
    IccQ5 = 12
    """ it controls pmu trigger position for ICC5 measure """

    Ipp1 = 13
    """ it controls pmu trigger position for IPP1 measure """
    Ipp2 = 14
    """ it controls pmu trigger position for IPP2 measure """
    Ipp3 = 15
    """ it controls pmu trigger position for IPP3 measure """
    Ipp4r = 16
    """ it controls pmu trigger position for IPP4 measure """
    Ipp4w = 17
    """ it controls pmu trigger position for IPP4 measure """
    Ipp5 = 18
    """ it controls pmu trigger position for IPP5 measure """

#######################
### ONFI COMMANDS ###
#######################

class ONFI_CMD:
    READ_ID_CMD_90h = 0x90
    READ_SR_CMD_70h = 0x70
    RESET_CMD_FFh = 0xFF
    RESET_CMD_FDh = 0xFD

    READ_CMD_00h = 0X00
    READ_CMD_30h = 0X30
    READ_CMD_32h = 0X32
    READ_CMD_05h = 0X05
    READ_CMD_06h = 0X06
    READ_CMD_E0h = 0XE0

    ZQ_CALIB_LONG_CMD_F9h = 0xF9
    ZQ_CALIB_SHORT_CMD_D9h = 0xD9

    GET_FEATURE_CMD_EEh = 0xEE
    SET_FEATURE_CMD_EFh = 0xEF

    GET_FEATURE_BY_LUN_CMD_D4h = 0xD4
    SET_FEATURE_BY_LUN_CMD_D5h = 0xD5

    PROGRAM_CMD_80h = 0x80
    PROGRAM_CMD_81h = 0x81
    PROGRAM_CMD_11h = 0x11
    PROGRAM_CMD_1Ah = 0x1A
    PROGRAM_CMD_10h = 0x10
    PROGRAM_SUSPEND_84h = 0x84
    PROGRAM_RESUME_13h = 0x13

    BLOCK_ERASE_CMD_60h = 0x60
    BLOCK_ERASE_CMD_D0h = 0xD0
    BLOCK_ERASE_CMD_D1h = 0xD1

    READ_STATUS_CMD_70h = 0x70
    READ_STATUS_ENHANCED_CMD_71h = 0x71
    READ_STATUS_ENHANCED_CMD_72h = 0x72
    READ_STATUS_ENHANCED_CMD_78h = 0x78

    READ_PARAMETERS_ECh = 0xEC

    SLC_CMD_A2h = 0xA2


############################################################
### the Device generic class
############################################################

class COnfiDevice:
    """This is the base class for a generic Nand device. Each device class should inherit from this class.
     It contains the default implementation of several Nand function. Overriding a method it is possible to
     customize the implementation of a function for a specific device.
      """
    def __init__(self):
        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_MANUFACTURER = "NandManufacturer"
        self.DEVICE_NAME = "OnfiDevice"
        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 8
        self.LEVEL_NUMBER = 3  # TLC 3 levels

        # ##########################################################
        # # change this configuration according device parameters
        # Toshiba Bics3 like
        self.toshiba_like = True
        self.PAGE_LENGTH = 18336
        self.PLANE_NUMBER = 2
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.WL_NUMBER = 256
        self.PAGE_NUMBER = self.LEVEL_NUMBER * self.WL_NUMBER
        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 8
        self.VALID_LUN_MASK = 0x01
        self.VALID_PAGE_MASK = 0x2FF
        self.VALID_BLOCK_MASK = 0x1FFF
        self.PAGE_PARAM_ADDR = 0x40
        self.ROW_BYTE_ADDRESS = 3
        # ##########################################################

        ##########################################################
        # change this configuration according device parameters
        # Micron B27B like
        # self.toshiba_like = False
        # self.PAGE_LENGTH = 18592
        # self.PLANE_NUMBER = 4
        # self.LUN_NUMBER = 1
        # self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        # self.WL_NUMBER = 1152
        # self.PAGE_NUMBER = 3456
        # self.LUN_START_BIT_ADDRESS = 23
        # self.BLOCK_START_BIT_ADDRESS = 12
        # self.VALID_LUN_MASK = 0x00
        # self.VALID_PAGE_MASK = 0xFFF
        # self.VALID_BLOCK_MASK = 0x7FF
        # self.PAGE_PARAM_ADDR = 0x00
        ##########################################################

        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.VT_LEVEL = 7 # TLC device only
        self.VT_LEVEL_STEPS = 256
        self.RR_OPTIONS = 15

        self.MAX_ERASE_TIME = 15000 # 15 msec
        self.MAX_PROG_TIME = 10000 # 10 msec
        self.MAX_READ_TIME = 1000 # 1 msec

        self.VCC = 2.5
        self.VCCQ = 1.2

        self.device_id = ""
        self.signature = ""
        self.manufacturer_code = ""
        self.model = ""

        self.pmu_algo = None

        self.force_all1 = False
        self.force_all0 = False

        self.opcbuilder_cache = None

        self.pattern_buffer = None
        self.option_buffer = None

        self.uid_list = []

        self.current_pattern = [0,0]
        self.current_seed = [0,0]

        self.odt = 4
        self.driver_strength = 4
        self.slc_mode = False
        self.aipr_mode = False

        self.read_latency_cycles = 0
        self.write_latency_cycles = 0

        self.fpga_read_latency_cycles = 0
        self.fpga_write_latency_cycles = 0

        self.tCS = 1000  # nsec
        self.tRR = 300 # nsec
        self.tPRE = 100 # nsec
        self.tRRC = 4000
        self.tRR_PB = 200
        self.tRR_SLC = 200
        self.tRR_FAST = 200

        self.temperature_4_channel = [25, 25]

    def get_last_device_temperature(self, ch):
        return self.temperature_4_channel[ch]

    def pages_to_wl_range(self, page_list):
        wl_min = 0xFFFFFFFF
        wl_max = 0
        for page in page_list:
            wl = page // self.LEVEL_NUMBER
            if wl < wl_min:
                wl_min = wl
            if wl > wl_max:
                wl_max = wl

        return wl_min, wl_max

    # TODO maybe it can be optimized sorting page list first
    def pages_to_wl_list(self, page_list):
        wl_list = []
        for page in page_list:
            wl = page // self.LEVEL_NUMBER
            if wl not in wl_list:
                wl_list.append(wl)

        return wl_list


    # as daulst slc management is disabled so each selected page is tested
    # if the device implements SLC management, override this function in the device class
    def pages_to_tested_pages_list(self, page_list):
        return page_list

    def pmu_algo_is_I1(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc1 == self.pmu_algo or PMU_ALGO.IccQ1 == self.pmu_algo or PMU_ALGO.Ipp1 == self.pmu_algo


    def pmu_algo_is_I2(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc2 == self.pmu_algo or PMU_ALGO.IccQ2 == self.pmu_algo or PMU_ALGO.Ipp2 == self.pmu_algo


    def pmu_algo_is_I3(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc3 == self.pmu_algo or PMU_ALGO.IccQ3 == self.pmu_algo or PMU_ALGO.Ipp3 == self.pmu_algo


    def pmu_algo_is_I4r(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc4r == self.pmu_algo or PMU_ALGO.IccQ4r == self.pmu_algo or PMU_ALGO.Ipp4r == self.pmu_algo


    def pmu_algo_is_I4w(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc4w == self.pmu_algo or PMU_ALGO.IccQ4w == self.pmu_algo or PMU_ALGO.Ipp4w == self.pmu_algo


    def pmu_algo_is_I5(self):
        if self.pmu_algo is None:
            return False
        return PMU_ALGO.Icc5 == self.pmu_algo or PMU_ALGO.IccQ5 == self.pmu_algo or PMU_ALGO.Ipp5 == self.pmu_algo

    def init_test(self, log_enable = True, log_set = None, cycle = 0, condition = "", time_plot = None, current_plot = None):

        logger.configure(self.CHUNK_NUMBER, log_enable, log_set, cycle, condition, time_plot, current_plot)

        if self.opcbuilder_cache is not None:
            self.opcbuilder_cache.cleanup()
            self.opcbuilder_cache = None

    def set_condition(self, condition):
        logger.set_condition(condition)

    def reset_fails_count(self):
        return logger.reset_fails_count()

    def get_fails_count(self, ch):
        return logger.get_fails_count(ch)

    def get_fails_count_4_chunk(self, ch, chunk):
        return logger.get_fails_count_4_chunk(ch, chunk)

    def select_channel(self, ch):
        hw.select_channel(ch)

    def set_ram_data_4(self, ch_list: [], data0, data1, data2, data3):
        """ program 4 registers (16 byte) of the custom sequence Data RAM, the byte programmed can be used in BD_ALE_REG and BD_ALE_REG """
        for ch in ch_list:
            hw.select_channel(ch)
            hw.custom_sequence_set_opcode_ram_data(0, data0)
            hw.custom_sequence_set_opcode_ram_data(1, data1)
            hw.custom_sequence_set_opcode_ram_data(2, data2)
            hw.custom_sequence_set_opcode_ram_data(3, data3)

    def set_ram_data_3(self, ch_list: [], data0, data1, data2):
        """ program 3 registers (12 byte) of the custom sequence Data RAM, the byte programmed can be used in BD_ALE_REG and BD_ALE_REG """
        for ch in ch_list:
            hw.select_channel(ch)
            hw.custom_sequence_set_opcode_ram_data(0, data0)
            hw.custom_sequence_set_opcode_ram_data(1, data1)
            hw.custom_sequence_set_opcode_ram_data(2, data2)

    def set_ram_data_2(self, ch_list: [], data0, data1):
        """ program 2 registers (8 byte) of the custom sequence Data RAM, the byte programmed can be used in BD_ALE_REG and BD_ALE_REG """
        for ch in ch_list:
            hw.select_channel(ch)
            hw.custom_sequence_set_opcode_ram_data(0, data0)
            hw.custom_sequence_set_opcode_ram_data(1, data1)

    def set_ram_data_1(self, ch_list: [], data0):
        """ program 1 registers (4 byte) of the custom sequence Data RAM, the byte programmed can be used in BD_ALE_REG and BD_ALE_REG """
        for ch in ch_list:
            hw.select_channel(ch)
            hw.custom_sequence_set_opcode_ram_data(0, data0)

    def build_row_address(self, lun, block, page):
        if self.toshiba_like:
            wordline = page // self.LEVEL_NUMBER
            row_address = (((lun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | (
                        (block & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (wordline % self.WL_NUMBER))
        else:
            row_address = (((lun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | ((block & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (page % self.PAGE_NUMBER))


        return True, row_address


    def build_roic_address(self, level, offset):
        offset_code0 = 0
        offset_code1 = 0
        offset_code2 = 0
        if level == 0 or level == 1 or level == 2:
            offset_code0 = offset
        elif level == 3 or level == 4 or level == 6:
            offset_code1 = offset
        elif level == 5:
            offset_code2 = offset
        else:
            raise Exception("Invalid Read Level")
        roic_address = (offset_code2 << 16) | (offset_code1 << 8) | offset_code0
        return roic_address

    def format_array(self, array: [], log_as_char = False):
        str = ""
        for i in range(len(array)):
            if log_as_char:
                # TODO replace not supported % char
                if array[i] == 0x25: # 0x25 = %
                    str += "/"
                else:
                    str += "{0}".format(chr(array[i]))
            else:
                str += "{0};".format(hex(array[i]))
        return str


    def turn_on(self, vcc: float, vccq: float):

        # turn on power supplies
        hw.turn_on(vcc, vccq)

        # wait a while after turn on
        time.msec_sleep(3000)

        # check the voltage
        ws.info("Vcc: {0:.3f} - Vccq: {1:.3f}".format(hw.get_vcc(), hw.get_vccq()))

        board_status = hw.get_board_status()
        if (board_status >> 16) & 0x3 != 0x3:
            ws.error("Ready Busy on Channel 0 should be High!")
        if (board_status >> 18) & 0x3 != 0x3:
            ws.error("Ready Busy on Channel 1 should be High!")

        logger.log_power("TURN_ON", vcc= vcc, vccq=vccq)



    def turn_off(self):
        hw.set_vpp_off()
        hw.turn_off()
        logger.log_power("TURN_OFF")


    def device_idle(self, ch_list: [], ce_list: [], idle_time_msec):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        en_i = self.pmu_algo_is_I5()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        if en_i:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC5 start
        for i in range(idle_time_msec):
            opcbuilder.add(eNandBackdoor.BD_DELAY, 1000000) # 1msec
        if en_i:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC5 start
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_idle(ch_list, ce_list, en_i)


    def device_reset(self, ch_list: [], ce_list: [], reset_cmd = ONFI_CMD.RESET_CMD_FFh):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, reset_cmd)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_busy_time("RESET_{0}h".format(hex(reset_cmd)), ch_list, ce_list, rbTimeName="tRST")


    def device_id_read(self, ch_list: [], ce, address = 0, id_len = 8):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        # id len must be multiple of 8 byte
        read_id_len = ((id_len + 7) // 8) * 8

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_ID_CMD_90h)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, read_id_len) # SDR to have lower speed
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            array = bytearray(id_len)
            for byte_id_index in range(id_len):
                array[byte_id_index] = hw.custom_sequence_sdr_get_data_byte(byte_index=byte_id_index)

            # common info do not matter where we read
            self.device_id = self.format_array(array)
            logger.log_device_id([ch], [ce], "DEVICE_ID", self.device_id)


    def page_parameter_read(self, ch_list: [], ce, address = 0):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_PARAMETERS_ECh)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
        # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 32) # SDR to have lower speed (valid only for HS because data out is performed with custom_sequence_ddr_get_data_byte)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 64)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            array = bytearray(64)
            for byte_id_index in range(64):
                array[byte_id_index] = hw.custom_sequence_ddr_get_data_byte(byte_index=byte_id_index)

            # common info do not matter where we read
            self.signature = self.format_array(array[0:4], True)
            logger.log_device_id([ch], [ce], "SIGNATURE", self.signature)
            self.manufacturer_code = self.format_array(array[32:43], True)
            logger.log_device_id([ch], [ce], "MANUFACTURER", self.manufacturer_code)
            self.model = self.format_array(array[44:63], True)
            logger.log_device_id([ch], [ce], "DEVICE_MODEL", self.model)


    def read_unique_id(self, ch_list, ce, lun = 0):
        return


    def zq_calib_long(self, ch_list: [], ce_list: [], lun: int):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.ZQ_CALIB_LONG_CMD_F9h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_busy_time("ZQ_CALIB_LONG", ch_list, ce_list, lun, "tZQCL")


    def zq_calib_short(self, ch_list: [], ce_list: [], lun: int):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.ZQ_CALIB_SHORT_CMD_D9h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_busy_time("ZQ_CALIB_SHORT", ch_list, ce_list, lun, "tZQCS")


    def set_feature(self, ch_list: [], ce_list: [], address, p1, p2, p3, p4, set_latency = False):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SET_FEATURE_CMD_EFh)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)
        for i in range(self.fpga_write_latency_cycles): # to mange DQS write latency
            opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p1)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p1)   # it uses DQS pulse
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p2)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p3)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p4)
        # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        # opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1) # NC
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # NC
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0) # NC
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_feature("SET_FEATURE", ch_list, ce_list, 0, address, [p1, p2, p3, p4])


    def set_feature_by_lun(self, ch_list: [], ce_list: [], lun, address, p1, p2, p3, p4):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SET_FEATURE_BY_LUN_CMD_D5h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)
        for i in range(self.fpga_write_latency_cycles): # to mange DQS write latency
            opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p1)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p1)  # it uses DQS pulse
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p2)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p3)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p4)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_feature("SET_FEATURE_BY_LUN", ch_list, ce_list, lun, address, [p1, p2, p3, p4])

    def set_feature_async(self, ch_list: [], ce_list: [], address, p1, p2, p3, p4):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SET_FEATURE_CMD_EFh)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)
        # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_IN, 4)  # it uses WR pulses
        for i in range(self.fpga_write_latency_cycles): # to mange DQS write latency
            opcbuilder.add(eNandBackdoor.BD_DATA_WR, p1)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p1)  # it uses DQS pulse
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p2)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p3)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p4)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_feature("SET_FEATURE_ASYNC", ch_list, ce_list, 0, address, [p1, p2, p3, p4])

    def set_feature_by_lun_async(self, ch_list: [], ce_list: [], lun, address, p1, p2, p3, p4):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SET_FEATURE_CMD_EFh)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)
        # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_IN, 4)  # it uses WR pulses
        for i in range(self.fpga_write_latency_cycles):  # to mange DQS write latency
            opcbuilder.add(eNandBackdoor.BD_DATA_WR, p1)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p1)  # it uses WR pulse
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p2)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p3)
        opcbuilder.add(eNandBackdoor.BD_DATA_WR, p4)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_feature("SET_FEATURE_BY_LUN_ASYNC", ch_list, ce_list, lun, address, [p1, p2, p3, p4])



    def get_feature(self, ch_list, ce, address):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.GET_FEATURE_CMD_EEh)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            params = []
            for feature_index in range(4):
                data = hw.custom_sequence_sdr_get_data_byte(byte_index=feature_index)
                params.append(data)

            logger.log_feature("GET_FEATURE", [ch], [ce], 0, address, params)


    def get_feature_by_lun(self, ch_list, ce, lun, address):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.GET_FEATURE_BY_LUN_CMD_D4h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_ALE, address)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            params = []
            for feature_index in range(4):
                data = hw.custom_sequence_sdr_get_data_byte(byte_index=feature_index)
                params.append(data)

            logger.log_feature("GET_FEATURE_BY_LUN", [ch], [ce], lun, address, params)

   
    def get_status_70h(self, ch_list, ce, lun):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_CMD_70h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status_slc("SRE70h", ch_list, ce, lun, status_register_check = True)

    
    def get_status_enhanced_71h(self, ch_list, ce, lun):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_71h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SRE71h", ch_list, ce, lun, status_register_check = False)

    
    def get_status_enhanced_72h(self, ch_list, ce, lun):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_72h)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SRE72h", ch_list, ce, lun, status_register_check = False)



    def get_status_enhanced_78h(self, ch_list, ce, lun, block):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        res, row_address = COnfiDevice.build_row_address(self, lun, block, 0)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
        opcbuilder.set_row_address(row_address)
        # self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SRE78h", ch_list, ce, lun, block)




    def get_status(self, ch_list, ce):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_CMD_70h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SR70h", ch_list, ce)


    def fill_pattern_buffer(self, file_buffer):
        if file_buffer:
            # full_path = utility.get_full_path(file_buffer)
            full_path = utility.get_full_path("{0}".format(file_buffer))
            ws.info("Full Path: {0}".format(full_path))

            try:
                fh = open(full_path, 'rb')
                self.pattern_buffer = bytearray(fh.read(self.PAGE_NUMBER * self.PAGE_LENGTH))
                if len(self.pattern_buffer) < self.PAGE_NUMBER * self.PAGE_LENGTH:
                    ws.error("Invalid file length, file size {0} must greater or equal to the block size {1}".format(
                        len(self.pattern_buffer), self.PAGE_NUMBER * self.PAGE_LENGTH))
                fh.close()
            except:
                raise Exception("File {0} is not accessible".format(full_path))  # exception


    def fill_option_buffer(self, file_options):
        ws.info("Try Read File Option: {0}".format(file_options))
        if file_options:
            full_path = utility.get_full_path("{0}/{1}".format("RR/",file_options))
            ws.info("File Option: {0} - Full Path: {1}".format(file_options, full_path))

            try:
                ws.info("Try Read File Option: {0}".format(file_options))

                with open(full_path, 'r') as f:
                    self.option_buffer = []
                    for line in f:
                        line = line.rstrip()
                        words = line.split(',')
                        self.option_buffer.append(words)
            except:
                raise Exception("File {0} is not accessible".format(full_path))  # exception
        else:
            self.option_buffer = None


    def select_pattern(self, ch_list, ce_list, lun_list, block_list, pattern_algo):
        """ Select the pattern for next operation
        This version use same pattern between channel, ce and lun, in this way is possible to support parallel operation (multi channel, multi ce)
        """

        for block in block_list:

            seed = PATTERN_SEED.ALL1
            if pattern_algo == PATTERN_ALGO.RANDOM:
                seed = (int(hw.get_nsec_time()) // 100) & 0xFFFF # nsec time resolution ==> 0.1 usec time seed
            elif pattern_algo == PATTERN_ALGO.RANDOM_0:
                seed = 0 # default seed is 0
            elif pattern_algo == PATTERN_ALGO.USER:
                seed = PATTERN_SEED.USER
            elif pattern_algo == PATTERN_ALGO.ALL0:
                seed = PATTERN_SEED.ALL0
            elif pattern_algo == PATTERN_ALGO.ALL1:
                seed = PATTERN_SEED.ALL1
            elif pattern_algo == PATTERN_ALGO.ALT_AA_55:
                seed = PATTERN_SEED.AA_55
            elif pattern_algo == PATTERN_ALGO.ALT_FF_00:
                seed = PATTERN_SEED.FF_00
            elif pattern_algo == PATTERN_ALGO.INCREMENTAL:
                seed = PATTERN_SEED.INCREMENTAL

            for ch in ch_list:
                for ce in ce_list:
                    for lun in lun_list:
                        # ws.info("SELECT PATTERN: Ch: {0} - Ce: {1} - Lun: {2} - B: {3} - Patter Algo: {4} - DB Seed: {5}".format(ch, ce, lun, block, pattern_algo, seed))
                        data_mgr.set_seed(ch, ce, lun, block, seed)
    
    
    def recall_pattern(self, ch_list, ce, lun, block, page):
        """ Recall expected pattern from database or from previous operation.
        """

        seed_high = [0,0]
        seed_low = [0,0]

        ch_idx = 0
        for ch in ch_list:
            hw.select_channel(ch)

            pattern, seed = self.expected_pattern_fpga_codes(ch, ce, lun, block, page)

            if pattern == ePatternType.User:
                start_pos = page * self.PAGE_LENGTH
                page_buffer = self.pattern_buffer[start_pos:start_pos + self.PAGE_LENGTH]
                hw.set_pattern_user_buffer(page_buffer, self.PAGE_LENGTH)

            hw.set_pattern(pattern, seed, 0)
            # if page == 0 and ch==ch_list[0]:
            #     ws.info("RECALL PATTERN: Ch: {0} - Ce: {1} - Lun: {2} - B: {3} - P: {4} - FPGA Pattern: {5} - FPGA Seed: {6}".format(ch, ce, lun, block, page,  pattern, seed))
            seed_high[ch_idx], seed_low[ch_idx] = hw.get_pattern_seed(seed, 0)
            ch_idx += 1
            # ws.info("RECALL PATTERN: Ch: {0} - Ce: {1} - Lun: {2} - B: {3} - P: {4} - Seed_High: {5} - Seed_Low: {6}".format(ch, ce, lun, block, page,  seed_high, seed_low))

        return seed_high, seed_low

    def expected_pattern_fpga_codes(self, ch, ce, lun, block, page):
        """ Convert the expected pattern int fpga codes to program the builtin pattern generator.
        """
        if self.force_all1:
            seed = 0
            pattern = ePatternType.All1
        elif self.force_all0:
            seed = 0
            pattern = ePatternType.All0
        else:
            block_seed = data_mgr.get_seed(ch, ce, lun, block)
            seed = 0
            pattern = ePatternType.All1
            if block_seed == PATTERN_SEED.USER: # user pattern code
                pattern = ePatternType.User
            elif block_seed == PATTERN_SEED.ALL0:
                pattern = ePatternType.All0
            elif block_seed == PATTERN_SEED.ALL1:
                pattern = ePatternType.All1
            elif block_seed == PATTERN_SEED.AA_55:
                pattern = ePatternType.Alt_55_AA
            elif block_seed == PATTERN_SEED.FF_00:
                pattern = ePatternType.Alt_FF_00
            elif block_seed == PATTERN_SEED.INCREMENTAL:
                pattern = ePatternType.Counter
            elif block_seed >= 0: # random patter code
                seed = block_seed + page
                pattern = ePatternType.Random

        return pattern, seed


    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list: []):

        for page in page_list:
            self.program_page(ch_list, ce_list, lun_list, block, page, 0)


    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):

        for page in page_list:
            self.multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0)


    def address_info(self, lun, block, page):
        wl = 0
        level = ""
        return True, wl, level


    def index_list_to_mask(self, the_list: []):
        mask = 0
        for element in the_list:
            mask |= (1 << element)
        return mask


    def die_configure(self, ch_list: [], ce_list: [], odt = 4, driver_strength = 4) :
        # software reset
        self.device_reset(ch_list, ce_list)

        # Driver strength 10h
        # 00h~01h Reserved
        # 02h Ron = Driver Multiplier : Underdrive
        # 03h Reserved
        # 04h Ron = 35 Driver Multiplier : 1 (default)
        # 05h Reserved
        # 06h Ron = Driver Multiplier : Overdrive 1
        # 07h~FFh Reserved
        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        lAddress = 0x10
        self.set_feature(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        odt_bit = odt & 0xF
        re_c_bit = 1
        dqs_c_bit = 1
        vref_bit = 1
        p1 = (odt_bit << 4) | (re_c_bit << 2) | (dqs_c_bit << 1) | (vref_bit << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        lAddress = 0x02
        self.set_feature(ch_list, ce_list, lAddress, p1, p2, p3, p4)

    def identification(self):

        # clear uid list if already present, but before update central db
        for uid in self.uid_list:
            data_mgr.store_to_central(uid)
        self.uid_list.clear()

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        lun_list = range(0, self.LUN_NUMBER)

        # assign default chunk length for each channel
        for ch in ch_list:
            hw.select_channel(ch)
            hw.set_chunk_length(self.CHUNK_LENGTH)

        for ch in ch_list:
            self.die_configure([ch], ce_list, self.odt, self.driver_strength)

            for ce in ce_list:
                hw.select_ce(ce)
                # Read Device Id
                self.device_id_read([ch], ce, 0, self.DEVICE_ID_LEN)
                # Read page parameters
                self.page_parameter_read([ch], ce, self.PAGE_PARAM_ADDR)

        # before exit perform perform ZQ Calib Long
        for lun in lun_list:
            self.zq_calib_long(ch_list, ce_list, lun)


    def calib_setup(self, data_rate_mhz, calib_length, ce = 0, lun = 0):
        # 63h command is not always supported (for example Bics3), use page buffer test for calibration routine

        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(ce)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        # opcbuilder.set_column_address(0)
        # opcbuilder.set_row_address(row_address)
        self.apply_col_address(opcbuilder,0)
        self.apply_row_address(opcbuilder,row_address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 1000)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_length)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99) # dummy command to skip real programming
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.page_buffer_write_builder(opcbuilder)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        # opcbuilder.set_column_address(0)
        # opcbuilder.set_row_address(row_address)
        self.apply_col_address(opcbuilder,0)
        self.apply_row_address(opcbuilder,row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.page_buffer_read_builder(opcbuilder)

        opcbuilder.cleanup()

        return True
    

    def enable_vpp(self, enable, vpp = 12):
        hw.vpp_set(vpp)
        time.msec_sleep(100)


    def set_power_supply(self, vcc, vccq, vpp):
        hw.vcc_set(vcc)
        hw.vccq_set(vccq)

        vpp_enable = vpp >= 9.99
        if vpp_enable:
            hw.set_vpp_on()
        else:
            hw.set_vpp_off()
        hw.vpp_set(vpp)

        self.enable_vpp(vpp_enable, vpp)
        logger.log_power("SET_POWER", vcc = vcc, vccq = vccq, vpp = vpp)


    def	apply_row_address(self, opcbuilder, address):
        #if self.ROW_BYTE_ADDRESS == 5:
        if self.ROW_BYTE_ADDRESS == 4:
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 24) & 0xFF)
        else:
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 16) & 0xFF)


    def	apply_row_address_reg(self, opcbuilder):
        if self.ROW_BYTE_ADDRESS == 4:
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 4)
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 5)
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 6)
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 7)
        else:
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 4)
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 5)
            opcbuilder.add(eNandBackdoor.BD_ALE_REG, 6)


    def	apply_col_address(self, opcbuilder, address):
        opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (address >> 8) & 0xFF)

    def	apply_col_address_reg(self, opcbuilder):
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 1)


    # #################################################
    # ERASE

    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block], en_i=bI3)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                opcbuilder.set_row_address(row_address)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)

    # #################################################
    # ERASE SUSPEND

    def erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay):
        ws.warning(__name__ + "." + COnfiDevice.erase_block_suspend.__name__ + ": not implemented")
        return True

    def erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay = -1):
        ws.warning(__name__ + "." + COnfiDevice.erase_block_resume.__name__ + ": not implemented")
        return True

    def multi_plane_erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_erase_block_suspend.__name__ + ": not implemented")
        return True

    def multi_plane_erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay = -1):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_erase_block_resume.__name__ + ": not implemented")
        return True

    # #################################################
    # PROGRAM

    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre

        # pattern is the same between ce, and lun: use the first
        self.recall_pattern(ch_list, ce_list[0], lun_list[0], block, page)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # record I4w only on the last lun
            bI4w = self.pmu_algo_is_I4w()
            if lun_idx != (len(lun_list) - 1):
                bI4w = False

            res, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % 3) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if self.toshiba_like and (page % self.LEVEL_NUMBER + 1) < self.LEVEL_NUMBER:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # record I2 and I4w only for upper page (2) of the last block
                bI2 = self.pmu_algo_is_I2()
                bI4w = self.pmu_algo_is_I4w()
                if (page % 3) != 2 or block_idx != (len(block_list) - 1):
                    bI2 = False
                    bI4w = False

                res, row_address = self.build_row_address(lun, block, page)

                # pattern is the same between ce  and channel, use the first
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2) # 2 to use register seed

                if self.toshiba_like:
                    opcbuilder.add(eNandBackdoor.BD_CLE, (page % 3) + 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.set_row_address(row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
                # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if self.toshiba_like and (page % self.LEVEL_NUMBER) + 1 < self.LEVEL_NUMBER:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)

                if bI2:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                    # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                if bI2:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)

            if (page % self.LEVEL_NUMBER) + 1 == self.LEVEL_NUMBER:
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()
                logger.log_program("MP-PRG", ch_list, ce_list, [lun], block_list, page, rbTimeName="tPROG", en_i=bI2 or bI4w)


        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # log SR only if log is enabled, page is upper page and we want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page%3) == 2):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    # #################################################
    # PROGRAM SUSPEND

    def program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_program_page_suspend.__name__ + ": not implemented")
        return True

    def program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay = -1):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_program_page_resume.__name__ + ": not implemented")
        return True

    def multi_plane_program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_program_page_suspend.__name__ + ": not implemented")
        return True

    def multi_plane_program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay = -1):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_program_page_resume.__name__ + ": not implemented")
        return True

    # #################################################
    # PAGE READ

    def get_read_buffer(self, ch, buffer_length):
        hw.select_channel(ch)
        bRes, buffer = hw.custom_sequence_get_out_buffer(buffer_length)
        return buffer

    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        ws.warning(__name__ + "." + COnfiDevice.page_read.__name__ + ": not implemented")

    def multi_plane_single_page_read_disturb(self, ch_list: [], ce, lun, block_list : [], page, column, page_length, cycle, log_cycle): # Eric
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_single_page_read_disturb.__name__ + ": not implemented")
    
    def multi_plane_block_read_disturb(self, ch_list: [], ce, lun, block_list : [], page, column, page_length): # Eric
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_block_read_disturb.__name__ + ": not implemented")

    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # the sequence will be executed by run_parallel in order to run time change the ce
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        if bI1:
            pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
        if self.toshiba_like:
            opcbuilder.add(eNandBackdoor.BD_CLE, (page % 3) + 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI1:
            pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        if bI4r:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        if bI4r:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)

        opcbuilder.cleanup()

    def page_fast_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page compare
        self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list : [], page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % 3) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName="tR", en_fails=False, en_rnb=True,
                        en_i=bI1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False, en_i=bI4r)

        opcbuilder.cleanup()


    def multi_plane_single_page_read_disturb(self, ch_list: [], ce, lun, list_block_list: [[]], page, column_address, page_length, cycles, loop):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        for block_list in list_block_list:
            
            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()

            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # bRes, row_address = COnfiDevice.build_row_address(self, lun, block, page)
                bRes, row_address = self.build_row_address(lun, block, page)

                # opcbuilder.clear()
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.set_row_address(row_address)
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()

        startT = hw.get_nsec_time()
        for cycle in range(0, cycles):
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            if (cycle+1) == cycles:
                endT = hw.get_nsec_time()
                elapsed = (endT - startT) / 1000000000
                ws.info("Done: SPRD with {0} times - Block {1} - Page {2} - Elapsed Time {3:.3f} sec".format(cycles*(loop+1), list_block_list, page, elapsed))
                startT = hw.get_nsec_time()

        opcbuilder.cleanup()


    def multi_plane_block_read_disturb(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % 3) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

    # #################################################
    # READ RETRY

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        ws.warning(__name__ + "." + COnfiDevice.set_read_retry_option.__name__ + ": not implemented")
        return False

    def reset_read_retry_option(self, ch_list: [], ce, lun):
        ws.warning(__name__ + "." + COnfiDevice.reset_read_retry_option.__name__ + ": not implemented")

    def read_retry_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page compare
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def multi_plane_read_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):
        ws.warning(__name__ + "." + COnfiDevice.multi_plane_read_retry_page_compare.__name__ + ": not implemented")

    # #################################################
    # READ OFFSET

    def level_name(self, level):
        # default implementation
        return "L{0}".format(level + 1)

    def get_valid_level_for_page(self, page):
        # as default return all levels
        return range(self.VT_LEVEL)

    def is_level_valid_for_page(self, level, page):
        # default implementation accept all levels
        return True

    def offset_to_voltage(self, offset):
        # default implementation
        return offset

    def offset_to_code(self, offset):
        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        #code = (offset + 0x80) & 0xFF
        code = (offset + 0x100) & 0xFF
        return code

    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):
        ws.warning(__name__ + "." + COnfiDevice.set_read_offset_code.__name__ + ": not implemented")
        return False, 0

    def set_read_offset_page(self, ch_list: [], ce, lun, page, dacs):
        return False, 0


    def set_read_offset_code_multi_level(self, ch, ce, lun, page, offset4level):
        ws.warning(__name__ + "." + COnfiDevice.set_read_offset_code_multi_level.__name__ + ": not implemented")
        return False, 0

    def reset_read_offset_code(self, ch_list: [], ce, lun, level):
        ws.warning(__name__ + "." + COnfiDevice.reset_read_offset_code.__name__ + ": not implemented")
        return True

    def read_offset_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page compare
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def read_offset_roic_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length, level, offset):
        # default implementation as page compare
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def read_offset_page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page read
        return self.page_read(ch_list, ce, lun, block, page, column, page_length)

    # #################################################


    # #################################################
    # VT
    def vt_level_name(self, level):
        return "VTL{0}".format(level + 1)

    def vt_init(self, ch_list: [], ce, lun, level):
        # optional routine, default implementation is empty
        return True

    def vt_terminate(self, ch_list: [], ce, lun, level):
        # optional routine, default implementation is empty
        return True

    def set_vt_code(self, ch_list: [], ce, lun, level, vt):
        ws.warning(__name__ + "." + COnfiDevice.set_vt_code.__name__ + ": not implemented")
        return True, 0

    def reset_vt_code(self, ch_list: [], ce, lun):
        ws.warning(__name__ + "." + COnfiDevice.reset_vt_code.__name__ + ": not implemented")
        return True

    def vt_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page compare
        return self.page_compare(ch_list, ce, lun, block, page, column, page_length)

    def vt_page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        # default implementation as page read
        return self.page_read(ch_list, ce, lun, block, page, column, page_length)

    # #################################################

    def read_internal_temperature(self, ch_list: [], ce):
        ws.warning(__name__ + "." + COnfiDevice.read_internal_temperature.__name__ + ": not implemented")


    def is_bad_block(self, ch_list: [], ce, lun, block):
        ws.warning(__name__ + "." + COnfiDevice.is_bad_block.__name__ + ": not implemented")

    # #################################################

    def load_info(self, ch, ce, lun, uid, name, did, manufacturer = "", device_model = ""):

        # be careful unique id is stored only for the first lun!!!!!
        if not uid or lun != 0:
            return

        ws.info("Load Info for CH: {0} - Ce: {1} Name: {2} - UID: {3} - DID: {4}".format(ch, ce, name, uid, did))

        data_mgr.insert_or_update_device(uid, name, did, device_model, manufacturer)
        data_mgr.load_from_central(uid)
        data_mgr.load_seeds(ch, ce, uid)
        data_mgr.load_bad_blocks(ch, ce, uid)
        self.uid_list.append(uid)


    # #################################################
    # Just for debug ...
    #

    def page_buffer_write(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(self, ch_list)
        ce_mask = self.index_list_to_mask(self, ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x80)
        opcbuilder.set_column_address(0)
        opcbuilder.set_row_address(0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PBW", ch_list, ce_list, lun_list, [block], page)


    def page_buffer_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(self, ch_list)
        ce_mask = self.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x05)
        opcbuilder.set_column_address(0)
        opcbuilder.set_row_address(0)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xE0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # buffer = self.get_read_buffer(ch_list[0], 64) #self.PAGE_LENGTH)
        # ws.info("{0}".format(self.format_array(buffer)))

        logger.log_read("PBR", ch_list, ce, lun, [block], page)


    def page_buffer_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(self, ch_list)
        ce_mask = self.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x05)
        opcbuilder.set_column_address(0)
        opcbuilder.set_row_address(0)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xE0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

    # #################################################
    # HARDWARE

    def fpga_setup(self, fpga_rl, fpga_wl, chunk_number):
        self.fpga_read_latency_cycles = fpga_rl
        self.fpga_write_latency_cycles = fpga_wl
        self.CHUNK_NUMBER = chunk_number
        self.CHUNK_LENGTH = self.PAGE_LENGTH // chunk_number
        # the chunk must be multiple of 8
        self.CHUNK_LENGTH &= 0xFFF8
        # BE CAREFUL: if the page length is not a multiple of chunk length the total fails can be different from the sum(fails4chunk)
        self.PAGE_LENGTH = self.CHUNK_LENGTH * self.CHUNK_NUMBER
        # ws.warning("fpga_rl: {0}, fpga_wl: {1}, chunk_number: {2}, chunk_length: {3} ".format(fpga_rl, fpga_wl, self.CHUNK_NUMBER, self.CHUNK_LENGTH))
        for ch in range(self.CHANNEL_NUM):
            hw.select_channel(ch)
            hw.set_chunk_length(self.CHUNK_LENGTH)
            hw.set_read_latency_cycles(self.fpga_read_latency_cycles)
            hw.set_write_latency_cycles(self.fpga_write_latency_cycles)

    def latency_cycle_decode(self, cycles):
        if cycles == 0:
            return 0
        elif cycles == 1:
            return 1
        elif cycles == 2:
            return 2
        elif cycles == 4:
            return 3
        else:
            return 0

    # #################################################
    # CUSTOM

    def custom_test_1(self):

        ch_list_calib = [0, 1]
        ch_list_read = [0, 1]
        ce = 0
        repeat_page_test = 10000

        ok = True

        summary_fw = nanocycler.filewriter()
        summary_fw.open("FailsForFrequency.csv")
        summary_fw.write_line("MTs;CH;minRW;minWW;RW;WW")

        #data_rates = [720,840,960,1080,1200]
        #for data_rate_mhz in data_rates:
        #for data_rate_mhz in range(600, 1350, 50):
        data_rates = range(1600, 1616, 16)
        for data_rate_mhz in data_rates:

            ws.info("DQ Calib at {0} MTs ....".format(data_rate_mhz))

            min_read_windows = [0, 0]
            min_write_windows = [0, 0]

            lun = 0
            self.calib_setup(data_rate_mhz, 256, ce, lun)
            hw.set_datarate(data_rate_mhz)

            for ch in ch_list_calib:
                self.select_channel(ch)
                hw.calibrate(ce, self.PAGE_LENGTH, False)
                res, read_window = hw.calibrate_get_read_window()
                res, write_window = hw.calibrate_get_write_window()

                min_read_windows[ch] = min(read_window)
                ws.info("CH: {0} - Min: {1} - Max: {2} - Read window: {3}".format(ch, min(read_window), max(read_window), read_window))
                min_write_windows[ch] = min(write_window)
                ws.info("CH: {0} - Min: {1} - Max: {2} - Write window: {3}".format(ch, min(write_window), max(write_window), write_window))
                summary_fw.write_line("{0};{1};{2};{3};{4};{5}".format(data_rate_mhz, ch, min_read_windows[ch], min_write_windows[ch], read_window, write_window))

            fail_count = [0, 0]
            read_byte_count_check = [True, True]
            for page in range(repeat_page_test):
                self.page_buffer_compare(ch_list_read, ce, 0, 0, page, 0, self.PAGE_LENGTH)

                for ch in ch_list_read:
                    hw.select_channel(ch)
                    res, fails = hw.custom_sequence_get_fail_count()
                    # if fails > 0:
                    #     ws.warning("[CH{0} - P{1}] - Fails: {2}".format(ch, page, fails));
                    fail_count[ch] += fails
                    res, byte_count = hw.custom_sequence_get_readbyte_count()
                    # print warning only one time
                    if byte_count != self.PAGE_LENGTH and read_byte_count_check[ch] == True:
                        ws.warning("[CH{0} - P{1}] - Byte Read: {2} - Expected: {3}".format(ch, page, byte_count, self.PAGE_LENGTH))
                        read_byte_count_check[ch] = False

            for ch in ch_list_read:
                if fail_count[ch] > 0 or read_byte_count_check[ch] == False:
                    ok = False
                    ws.warning("DQ Calib at {0} MTs - CH: {1} - Fails: {2} - ReadByteCountCheck: {3}".format(data_rate_mhz, ch, fail_count[ch], read_byte_count_check[ch]))
                else:
                    ws.info("DQ Calib at {0} MTs - CH: {1} - Fails: {2}".format(data_rate_mhz, ch, fail_count[ch]))
                #summary_fw.write_line("{0};{1};{2};{3};{4}".format(data_rate_mhz, ch, min_read_windows[ch], min_write_windows[ch], fail_count[ch]))
            summary_fw.flush()

        summary_fw.close()
        summary_fw.cleanup()

        if not ok:
            ws.error("Inspection Failed!")
            ws.set_alarm("Inspection Failed!")

    def custom_test_2(self):

        ch_list_calib = [0, 1]
        ch_list_read = [0, 1]
        ce_list = [0, 1]
        repeat_page_test = self.PAGE_NUMBER

        read_ber_plot = nanocycler.plot()
        read_ber_plot.openext("BER vs DATARATE", "MTs", nanocycler.enumScaleType.Numerical, "0.", False, "%", nanocycler.enumScaleType.Numerical, "0.000", False, "")

        test_idx = 0

        #data_rates = [720,840,960,1080,1200]
        #for data_rate_mhz in data_rates:
        #for data_rate_mhz in range(600, 1350, 50):

        data_rates = range(1200, 2001, 25)
        for data_rate_mhz in data_rates:

        #for t in range(10):
            #data_rate_mhz = 1950

            ws.info("DQ Calib at {0} MTs ....".format(data_rate_mhz))

            min_read_windows = [0, 0]
            min_write_windows = [0, 0]

            lun = 0
            self.calib_setup(data_rate_mhz, 256, ce, lun)
            hw.set_datarate(data_rate_mhz)

            ok = True

            if not ok:
                continue

            for ce in ce_list:
                for ch in ch_list_calib:
                    self.select_channel(ch)
                    hw.calibrate(ce, repeat_page_test, False)
                    res, read_window = hw.calibrate_get_read_window()
                    res, write_window = hw.calibrate_get_write_window()

                    min_read_windows[ch] = min(read_window)
                    ok &= min_read_windows[ch] > 0
                    ws.info("CH: {0} CE: {1} - Min: {2} - Max: {3} - Read window: {4}".format(ch, ce, min(read_window), max(read_window), read_window))
                    min_write_windows[ch] = min(write_window)
                    ok &= min_write_windows[ch] > 0
                    ws.info("CH: {0} CE: {1} - Min: {2} - Max: {3} - Write window: {4}".format(ch, ce, min(write_window), max(write_window), write_window))

            if not ok:
                continue

            for _t in range(1):
                for ce in ce_list:
                    hw.select_ce(ce)

                    for __t in range(1):
                        fail_count = [0, 0]
                        read_byte_count_check = [True, True]
                        for page in range(self.PAGE_NUMBER):
                            #self.page_buffer_compare(ch_list_read, ce, 0, 0, page % self.PAGE_NUMBER, 0, self.PAGE_LENGTH)
                            #res, busy_pre = hw.custom_sequence_get_busy_time()
                            self.page_compare(ch_list_read, ce, 0, 0, page, 0, self.PAGE_LENGTH)

                            for ch in ch_list_read:
                                hw.select_channel(ch)
                                res, fails = hw.custom_sequence_get_fail_count()
                                if fails > 500:
                                    res, busy_post = hw.custom_sequence_get_busy_time()
                                    ws.error("[CH{0} - CE{1} - P{2}] - Fails: {3} - tR: {4}".format(ch, ce, page, fails, busy_post))
                                # if page == 0:
                                #     res, buf = hw.custom_sequence_get_out_buffer(64)
                                #     if fails > 500:
                                #         ws.error("[CH{0} - CE{1} - P{2}] {3}".format(ch, ce, page, self.format_array(buf)))
                                #     else:
                                #         ws.info("[CH{0} - CE{1} - P{2}] {3}".format(ch, ce, page, self.format_array(buf)))

                                fail_count[ch] += fails
                                res, byte_count = hw.custom_sequence_get_readbyte_count()
                                # print warning only one time
                                if byte_count != self.PAGE_LENGTH and read_byte_count_check[ch] == True:
                                    ws.warning("[CH{0} - P{1}] - Byte Read: {2} - Expected: {3}".format(ch, page, byte_count, self.PAGE_LENGTH))
                                    read_byte_count_check[ch] = False

                        for ch in ch_list_read:
                            ber = 100 * fail_count[ch] / (repeat_page_test * 8 * self.PAGE_LENGTH)
                            if fail_count[ch] > 0 or read_byte_count_check[ch] == False:
                                ws.warning("DQ Calib at {0} MTs - CH: {1} CE: {2} - BER: {3:.5f} - Fails: {4} - ReadByteCountCheck: {5}".format(data_rate_mhz, ch, ce, ber, fail_count[ch], read_byte_count_check[ch]))
                            else:
                                ws.info("DQ Calib at {0} MTs - CH: {1} CE: {2} - BER: {3:.5f} - Fails: {4} - ReadByteCountCheck: {5}".format(data_rate_mhz, ch, ce, ber, fail_count[ch], read_byte_count_check[ch]))
                            read_ber_plot.add(data_rate_mhz, ber, "CH{0}-CE{1}".format(ch,ce))
                            #read_ber_plot.add(test_idx, ber, "CH{0}".format(ch))
                        read_ber_plot.flush()
                        test_idx += 1

        read_ber_plot.close()
        read_ber_plot.cleanup()
