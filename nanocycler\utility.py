## @package nanocycler.utility
#  <summary> Contains the utility functions. </summary>
#
# @ingroup wsApiGroupPyLanguage


# ! @cond Doxygen_Suppress
try:

    from uctypes import bytes_at as bytes_at
    from uctypes import addressof as address_of

except ImportError:

    def bytes_at(addr, array_len):
        return bytearray(array_len)

    def address_of(buffer: bytearray):
        return 0

try:
    import ustruct
except ImportError:
    import struct as ustruct
# ! @endcond


## <summary>	Return full phisical path giving a file name.</summary>
#
# <param name="baseFileName">	the file name.</param>
# @snippet std_examples.py Utility GetFullPath
def get_full_path(baseFileName):
    return "/run/media/mmcblk0p1/nano/workspace/{0}".format(baseFileName)


# ! @cond Doxygen_Suppress
def get_int_from_bytes(buffer):
    value = int.from_bytes(buffer, "little")
    return value

def to_int32(val):
    val &= ((1 << 32) - 1)
    if val & (1 << 31):
        val -= (1 << 32)
    return val

def get_signed_int_from_bytes(buffer):
    value = int.from_bytes(buffer, "little")
    res = to_int32(value)
    return res

def get_float_from_bytes(buffer):
    value = ustruct.unpack("f", buffer)[0]
    return float(value)


def get_address_at(addr):
    addr_bytes = bytes_at(addr, 8)
    return get_int_from_bytes(addr_bytes)


def get_string_at(addr):
    addrv = get_address_at(addr)
    value = ""
    abytes = bytes_at(addrv, 1)
    while abytes[0] != 0:
        value = value + str(abytes, 'utf-8')
        addrv = addrv + 1
        abytes = bytes_at(addrv, 1)

    return value


def get_adr_buf_for_int():
    buffer = bytearray(4)
    adrChannel = address_of(buffer)
    return adrChannel, buffer


def get_adr_buf_for_float():
    buffer = bytearray(4)
    adrChannel = address_of(buffer)
    return adrChannel, buffer


def get_adr_buf_for_ubyte():
    buffer = bytearray(1)
    adress = address_of(buffer)
    return adress, buffer


def cstr(array):
    return str(array, 'utf-8').rstrip("\0")


def str2bool(v:str):
    return v.lower() in ("yes", "true", "t", "1")


def ai2b(int_array):
    buffer = bytearray(len(int_array) * 4)
    j = 0
    for i in range(0, len(int_array)):
        val = int_array[i]
        buffer[j] = val & 0xFF
        j += 1
        buffer[j] = (val >> 8) & 0xFF
        j += 1
        buffer[j] = (val >> 16) & 0xFF
        j += 1
        buffer[j] = (val >> 24) & 0xFF
        j += 1

    address = address_of(buffer)
    return address, buffer


def b2ai(buffer, int_array):
    j = 0
    for i in range(0, len(int_array)):
        val = buffer[j]
        j += 1
        val |= buffer[j] << 8
        j += 1
        val |= buffer[j] << 16
        j += 1
        val |= buffer[j] << 24
        j += 1
        int_array[i] = val
    return


def af2b(float_array):
    lenght = len(float_array)
    buffer = bytearray(lenght * 8)
    j = 0
    for i in range(0, lenght):
        data = ustruct.pack("d", float_array[i])
        buffer[j] = data[0]
        j += 1
        buffer[j] = data[1]
        j += 1
        buffer[j] = data[2]
        j += 1
        buffer[j] = data[3]
        j += 1
        buffer[j] = data[4]
        j += 1
        buffer[j] = data[5]
        j += 1
        buffer[j] = data[6]
        j += 1
        buffer[j] = data[7]
        j += 1
    address = address_of(buffer)

    return address, buffer


def b2af(buffer, float_array):
    j = 0
    for i in range(0, len(float_array)):
        data = bytearray(8)
        data[0] = buffer[j]
        j += 1
        data[1] = buffer[j]
        j += 1
        data[2] = buffer[j]
        j += 1
        data[3] = buffer[j]
        j += 1
        data[4] = buffer[j]
        j += 1
        data[5] = buffer[j]
        j += 1
        data[6] = buffer[j]
        j += 1
        data[7] = buffer[j]
        j += 1
        val = ustruct.unpack("d", data)[0]
        float_array[i] = val
    return

# ! @endcond

