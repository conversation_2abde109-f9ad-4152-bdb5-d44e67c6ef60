
from nanocycler import hardware as hw
from nanocycler import ws as ws
from nanocycler import pmu as pmu
from nanocycler import datalog as datalog
from lib.DataMgr import the_data_mgr as data_mgr
from lib.DataMgr import eBad<PERSON>lockCode as eBadBlockCode

# TODO how to costant
MAX_CHUNK_NUMBER = 32
MAX_CH_NUMBER = 2

class LOG_SET_ITEM:
    tRnB = 0
    tRnB4CHUNK = 1
    FAILS = 2
    FAILS4CHUNK = 3
    SR = 4
    DATA = 5
    PmuAvg = 6
    PmuMax = 7
    PmuSamples = 8



class CFailsCounter:
    def __init__(self):
        self.fails_4_channel = [0.0 for i in range(MAX_CH_NUMBER)]
        self.fails_4_channel_4_chunk = [0.0 for i in range(MAX_CH_NUMBER * MAX_CHUNK_NUMBER)]

    def reset(self):
        for ch in range(MAX_CH_NUMBER):
            self.fails_4_channel[ch] = 0
            for chunk in range(MAX_CHUNK_NUMBER):
                self.fails_4_channel_4_chunk[ch * MAX_CHUNK_NUMBER + chunk] = 0

    def add_fails(self, ch, fails):
        # ws.info("Add Fails for Channel: {0} - Fails: {1}".format(ch, fails))
        self.fails_4_channel[ch] += fails

    def add_fails_4_chunk(self, ch, chunk, fails):
        # ws.info("Add Fails for Channel: {0} - Chunk: {1} - Fails: {2}".format(ch, chunk, fails))
        self.fails_4_channel_4_chunk[ch*MAX_CHUNK_NUMBER+chunk] += fails

    def get_fails(self, ch):
        return self.fails_4_channel[ch]

    def get_fails_4_chunk(self, ch, chunk):
        return self.fails_4_channel_4_chunk[ch*MAX_CHUNK_NUMBER+chunk]



class CResultLogger:
    """The class for data processing and data logging the results"""

    def __init__(self):

        self.log_enable = False
        self.log_set = set()

        self.time_plot = None
        self.current_plot = None
        self.cycle = 0
        self.condition = ""

        self.chunks_number = 0
        self.stop_at_fail = False
        self.fbc_limit = -1

        self.fails_counter = CFailsCounter()

        self.fails_cache = [0 for j in range(MAX_CH_NUMBER)]
        self.fails_4_chunk_cache = [[0 for i in range(MAX_CHUNK_NUMBER)] for j in range(MAX_CH_NUMBER)]
        self.rnb_4_chunk_cache = [[0 for i in range(MAX_CHUNK_NUMBER)] for j in range(MAX_CH_NUMBER)]


    def configure(self, chunks_number, log_enable = False, log_set = None, cycle = 0, condition = "", time_plot = None, current_plot = None,):

        """Configure next processing"""
        self.chunks_number = chunks_number
        self.log_enable = log_enable
        self.log_set = log_set
        self.cycle = cycle
        self.condition = condition

        self.time_plot = time_plot
        self.current_plot = current_plot

        self.fails_counter.reset()

        datalog.set_counter(self.cycle)
        datalog.set_condition(self.condition)


    def is_enabled(self):
        return self.log_enable


    def is_in_log_set(self, item):
        return self.log_set is not None and item in self.log_set


    def set_condition(self, condition):
        self.condition = condition
        datalog.set_condition(self.condition)

    def set_stop_at_fail(self, stop_st_fail):
        self.stop_at_fail = stop_st_fail

    def set_fbc_limit(self, fbc_hard):
        self.fbc_limit = fbc_hard

    def set_chunks_number(self, chunks_number):
        self.chunks_number = chunks_number


    def reset_fails_count(self):
        self.fails_counter.reset()

    def get_fails_count(self, ch):
        return self.fails_counter.get_fails(ch)

    def get_fails_count_4_chunk(self, ch, chunk):
        return self.fails_counter.get_fails_4_chunk(ch, chunk)


    # collect from channel and append to fails_counter object
    def collect_fails(self, ch_list):
        for ch in ch_list:
            hw.select_channel(ch)
            res, fails = hw.custom_sequence_get_fail_count()
            self.fails_cache[ch] = fails
            self.fails_counter.add_fails(ch, fails)


    # collect from channel and append to fails_counter object
    def collect_fails_4_chunk(self, ch_list, chunk_number):
        for ch in ch_list:
            hw.select_channel(ch)
            for chunk in range(chunk_number):
                res, fails = hw.custom_sequence_get_fail_count_4_chunk(chunk)
                self.fails_4_chunk_cache[ch][chunk] = fails
                self.fails_counter.add_fails_4_chunk(ch, chunk, fails)
                if self.fbc_limit >= 0 and fails > self.fbc_limit:
                    Exception("FBC exceeds the hard limit!! Test stopped due to stop at fail is enabled.")


    def log_fpga_result(self, ch, function = "", strRnB = None, strFails = None,
            chunk_number = 0, strRnB4Chunk = None, strFails4Chunk = None,
            strPmuAvg = None, strPmuMax = None, strPmuSamples = None,
            strData = None, dataCount = 0, text = None):

        if not self.log_enable:
            return

        hw.select_channel(ch)

        datalog.set_channel(ch)
        datalog.set_function(function)

        if strRnB is not None:
            res, tRnB = hw.custom_sequence_get_busy_time()
            # ws.info("CH: {0} - {1}: {2}".format(ch, strRnB, tRnB))
            datalog.add_data(strRnB, "{0:.3f}".format(tRnB / 1000)) # convert busy time in usec log with 3 decimals

        if strRnB4Chunk is not None :
            for chunk in range(chunk_number):
                res, nsec_busy_time = hw.custom_sequence_get_busy_time_4_chunk(chunk)
                self.rnb_4_chunk_cache[ch][chunk] = nsec_busy_time / 1000 # convert busy time in usec
            datalog.add_data_vector(strRnB4Chunk, self.rnb_4_chunk_cache[ch], chunk_number, 3) # log with 3 decimals

        if strFails is not None:
            # fails are already cached
            # res, fails = hw.custom_sequence_get_fail_count()
            # ws.info("CH: {0} - {1}: {2}".format(ch, strFail, fails))
            datalog.add_data(strFails, "{0}".format(self.fails_cache[ch]))

        if strFails4Chunk is not None :
            # fails are already cached
            # for chunk in range(chunk_number):
            #     res, fails = hw.custom_sequence_get_fail_count_4_chunk(chunk)
            #     self.fails_4_chunk_cache[ch][chunk] = fails
            datalog.add_data_vector(strFails4Chunk, self.fails_4_chunk_cache[ch], chunk_number, 0)
            # if self.fails_cache[ch] != sum(self.fails_4_chunk_cache[ch]):
            #     ws.warning("Fails: {0} - Chunks: {1}".format(self.fails_cache[ch], sum(self.fails_4_chunk_cache[ch])))

        if hw.is_nanocycler_hs():
            if strPmuAvg is not None:
                res, pmuAvg = pmu.PMU_GET_AVG_CURRENT()
                datalog.add_data(strPmuAvg, "{0:.2f}".format(pmuAvg))

            if strPmuMax is not None:
                res, pmuMax = pmu.PMU_GET_MAX_CURRENT()
                datalog.add_data(strPmuMax, "{0:.2f}".format(pmuMax))

            if strPmuSamples is not None:
                pmuSamples = [0.0 for i in range(pmu.MAX_SAMPLES_COUNT)]
                bRes, samplesCount = pmu.PMU_GET_SAMPLES(pmuSamples, pmu.MAX_SAMPLES_COUNT)
                datalog.add_data_vector(strPmuSamples, pmuSamples, samplesCount, 1)
                if self.current_plot is not None:
                    series = "CH{0}_{1}".format(ch, text)
                    for i in range(0, samplesCount):
                        self.current_plot.add(i * pmu.PMU_SAMPLE_USEC_PERIOD(), pmuSamples[i], series)
                    self.current_plot.flush()

        if strData is not None and dataCount > 0:
            datas = [0.0 for i in range(dataCount)]
            for i in range(dataCount):
                datas[i] = hw.custom_sequence_sdr_get_data_byte(i)
            datalog.add_data_vector(strData, datas, dataCount, 0)


    def log_power(self, function, vcc = 0.0, vccq = 0.0, vpp = 0.0):
        if not self.log_enable:
            return

        datalog.set_ce(0)
        datalog.set_ce(0)
        datalog.set_address(0, 0, 0)
        datalog.set_function(function)
        datalog.set_vcc(vcc)
        datalog.set_vccq(vccq)
        datalog.set_vpp(vpp)
        datalog.add_data("", "") # to add a line


    def log_busy_time(self, function, ch_list, ce_list, lun = 0, rbTimeName ="tRmB"):

        if not self.log_enable:
            return

        strRnB = rbTimeName if self.is_in_log_set(LOG_SET_ITEM.tRnB) else None

        for ch in ch_list:
            for ce in ce_list:
                datalog.set_ce(ce)
                datalog.set_address(lun, 0, 0)
                self.log_fpga_result(ch=ch, function=function, strRnB=strRnB)


    def log_idle(self, ch_list, ce_list, en_i):
        if not self.log_enable:
            return

        strPmuAvg = "IAvg" if en_i and self.is_in_log_set(LOG_SET_ITEM.PmuAvg) else None
        strPmuMax = "IMax" if en_i and self.is_in_log_set(LOG_SET_ITEM.PmuMax) else None
        strPmuSamples = "ISamples" if en_i and self.is_in_log_set(LOG_SET_ITEM.PmuSamples) else None

        for ch in ch_list:
            for ce in ce_list:
                datalog.set_ce(ce)
                self.log_fpga_result(ch=ch, function="IDLE", strPmuAvg = strPmuAvg, strPmuMax = strPmuMax, strPmuSamples = strPmuSamples)

    def log_device_id(self, ch_list, ce_list, id_name, id_value):

        if not self.log_enable:
            return

        for ch in ch_list:
            datalog.set_channel(ch)
            for ce in ce_list:
                ws.info("Ch: {0} - Ce: {1} - {2} : {3}".format(ch, ce, id_name, id_value))
                datalog.set_ce(ce)
                datalog.set_address(0, 0, 0)
                datalog.add_data(id_name, id_value)


    def log_device_unique_id(self, ch_list, ce_list, lun, id_name, id_value):

        if not self.log_enable:
            return

        for ch in ch_list:
            datalog.set_channel(ch)
            for ce in ce_list:
                ws.info("Ch: {0} - Ce: {1} - Lun: {2} - {3} : {4}".format(ch, ce, lun, id_name, id_value))
                datalog.set_ce(ce)
                datalog.set_address(lun, 0, 0)
                datalog.add_data(id_name, id_value)

    def log_die_temperature(self, function, ch_list, ce_list, die_temp):
        for ch in ch_list:
            datalog.set_channel(ch)
            for ce in ce_list:
                datalog.set_ce(ce)
                datalog.set_address(0, 0, 0)
                datalog.set_function(function)
                datalog.add_data("DIE_TEMP", die_temp)


    def log_feature(self, function, ch_list, ce_list, lun, address, params: []):
        # ws.warning("Feature: {0} - Data: {1}".format(hex(address), params))
        if not self.log_enable:
            return

        strRnB = "tFEAT" if self.is_in_log_set(LOG_SET_ITEM.tRnB) else None

        for ch in ch_list:
            datalog.set_channel(ch)
            for ce in ce_list:
                datalog.set_ce(ce)
                datalog.set_address(lun, 0, 0)
                self.log_fpga_result(ch=ch, function=function, strRnB=strRnB)
                datalog.add_data_vector_tag("DATA", "A[{0}]".format(hex(address)), params, len(params), 0)


    def log_calib_param(self, ch, ce, datarate_mhz):

        if not self.log_enable:
            return

        hw.select_channel(ch)

        datalog.set_channel(ch)
        datalog.set_ce(ce)
        datalog.set_address(0, 0, 0)
        datalog.set_datarate(datarate_mhz)
        datalog.set_function("CALIB")

        res, read_window = hw.calibrate_get_read_window()
        datalog.add_data_vector("READ_WINDOW", read_window, 8, 0)
        ws.info("CH: {0} - Read window: {1}".format(ch, read_window))

        res, write_window = hw.calibrate_get_write_window()
        datalog.add_data_vector("WRITE_WINDOW", write_window, 8, 0)
        ws.info("CH: {0} - Write window: {1}".format(ch, write_window))


    def log_bad_block(self, ch, ce, lun, block, is_bad_block, bad_category: int = eBadBlockCode.FactoryBadBlock,
                      function = "BAD_BLOCK"):

        # always update data manager, log only if enabled
        category = data_mgr.get_bad_block_category(ch, ce, lun, block)
        # if category == eBadBlockCode.GoodBlock and is_bad_block:
        if is_bad_block:
            category = bad_category
            data_mgr.add_bad_block(ch, ce, lun, block, category)

        # if not self.log_enable:
        #     return

        datalog.set_channel(ch)
        datalog.set_ce(ce)
        datalog.set_address(lun, block, 0)
        datalog.set_function(function)
        datalog.add_data("Category", "{0}".format(category))

        if is_bad_block:
            ws.info("CH: {0} - Ce {1} - Lun: {2} - Block: {3} - Bad Block Category: {4}".format(ch, ce, lun, block, category))




    def log_status(self, function, ch_list, ce = 0, lun = 0, block = 0, status_register_check = True):

        if not self.log_enable:
            return

        sr_enabled = self.is_in_log_set(LOG_SET_ITEM.SR)
        strData = "SR" if sr_enabled else None

        for ch in ch_list:
            datalog.set_ce(ce)
            datalog.set_address(lun, block, 0)
            self.log_fpga_result(ch=ch, function=function, strData=strData, dataCount=1)

            if sr_enabled and status_register_check:
                sr = hw.custom_sequence_sdr_get_data_byte(0)
                if sr & 0x01 == 0x01:
                    # if data_mgr.is_bad_block(ch, ce, lun, block):  # Eric
                    #     return         # Eric
                    ws.error("Bad Block detected: CH: {0} - Ce: {1} - Lun: {2} - Block: {3} - Code: {4}".format(ch, ce, lun, block, sr & 0x01))
                    data_mgr.add_bad_block(ch, ce, lun, block, eBadBlockCode.FunctionalBadBlock)
                    if self.stop_at_fail:
                        Exception("Test stopped due to stop at fail is enabled.")


    def log(self, function, ch_list, ce_list, lun_list, block_list, page, rbTimeName,
            en_fails = True, en_rnb = True, en_icc = True):

        if not self.log_enable:
            return

        strFails = "FAILS" if en_fails and self.is_in_log_set(LOG_SET_ITEM.FAILS) else None
        strFails4Chunk = "FAILS4CHUNK" if en_fails and self.is_in_log_set(LOG_SET_ITEM.FAILS4CHUNK) else None
        strRnB = rbTimeName if en_rnb and self.is_in_log_set(LOG_SET_ITEM.tRnB) else None
        strRnB4Chunk = "{0}4CHUNK".format(rbTimeName) if en_rnb and self.is_in_log_set(LOG_SET_ITEM.tRnB4CHUNK) else None
        strPmuAvg = "IAvg" if en_icc and self.is_in_log_set(LOG_SET_ITEM.PmuAvg) else None
        strPmuMax = "IMax" if en_icc and self.is_in_log_set(LOG_SET_ITEM.PmuMax) else None
        strPmuSamples = "ISamples" if en_icc and self.is_in_log_set(LOG_SET_ITEM.PmuSamples) else None

        for ch in ch_list:
            # set part address
            datalog.set_ce_vector(ce_list, len(ce_list))
            if len(lun_list) == 1 and len(block_list) == 1:
                datalog.set_address(lun_list[0], block_list[0], page)
            else:
                datalog.set_lun_vector(lun_list, len(lun_list))
                datalog.set_block_vector(block_list, len(block_list))
                datalog.set_page(page)
            # TODO add array to serie name
            series = "Ce{0}_L{1}_B{2}_P{3}_Cy{4}".format(ce_list[0], lun_list[0], block_list[0], page, self.cycle)
            self.log_fpga_result(ch=ch, function=function, strRnB=strRnB, strRnB4Chunk = strRnB4Chunk,
                                 strFails = strFails, strFails4Chunk = strFails4Chunk,
                                 strPmuAvg=strPmuAvg, strPmuMax=strPmuMax, strPmuSamples=strPmuSamples, text = series,
                                 chunk_number = self.chunks_number)


    def log_erase(self, function, ch_list, ce_list, lun_list, block_list, rbTimeName = "tERS", en_rnb = True, en_i = True):

        self.log(function, ch_list, ce_list, lun_list, block_list, 0, rbTimeName, en_fails = False, en_rnb = en_rnb, en_icc = en_i)


    def log_program(self, function, ch_list, ce_list, lun_list, block_list, page, rbTimeName = "tPRG", en_rnb = True, en_i = True):

        self.log(function, ch_list, ce_list, lun_list, block_list, page, rbTimeName, en_fails = False, en_rnb = en_rnb, en_icc = en_i)


    def log_read(self, function, ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = True, en_rnb = True, en_i = True):

        # in read operation collect fails
        if en_fails and self.is_in_log_set(LOG_SET_ITEM.FAILS):
            self.collect_fails(ch_list)

            # TODO just for debug
            # for ch in ch_list:
            #     hw.select_channel(ch)
            #     res, bytes = hw.custom_sequence_get_readbyte_count()
            #     res, fails = hw.custom_sequence_get_fail_count()
            #     if fails >= 2000:
            #         ws.warning("{0} - CH: {1} - Block: {2} - Page: {3} - Bytes reads: {4}".format(function, ch, block_list, page, bytes))
            # TODO end debug

        if en_fails and self.is_in_log_set(LOG_SET_ITEM.FAILS4CHUNK):
            self.collect_fails_4_chunk(ch_list, self.chunks_number)

        self.log(function, ch_list, [ce], [lun], block_list, page, rbTimeName, en_fails, en_rnb, en_i)


    def log_fails(self, condition, ch, ce, lun_list, block_list, page_list, fails: [], function = "READ"):

        datalog.set_condition(condition)
        datalog.set_function(function)
        datalog.set_channel(ch)
        datalog.set_ce(ce)
        datalog.set_lun_vector(lun_list, len(lun_list))
        datalog.set_block_vector(block_list, len(block_list))
        datalog.set_page_vector(page_list, len(page_list))
        datalog.add_data_vector("FAILS", fails, len(fails), 0)
        datalog.set_condition("")

    def log_result_user(self, condition, ch, ce, lun_list, block_list, page_list, datainfo, data: [], function = "READ"):

        datalog.set_condition(condition)
        datalog.set_function(function)
        datalog.set_channel(ch)
        datalog.set_ce(ce)
        datalog.set_lun_vector(lun_list, len(lun_list))
        datalog.set_block_vector(block_list, len(block_list))
        datalog.set_page_vector(page_list, len(page_list))
        datalog.add_data_vector(datainfo, data, len(data), 0)
        datalog.set_condition("")

    def log_buffer(self, function, ch, ce, lun_list, block_list, page_list, buffer = []):   #hang, add for log read buffer
        datalog.set_function(function)
        datalog.set_channel(ch)
        datalog.set_ce(ce)
        datalog.set_lun_vector(lun_list, len(lun_list))
        datalog.set_block_vector(block_list, len(block_list))
        datalog.set_page_vector(page_list, len(page_list))
        datalog.add_data_vector("BUFFER", buffer, len(buffer), 0)

""" The static instance of result logger class """
the_result_logger = CResultLogger()