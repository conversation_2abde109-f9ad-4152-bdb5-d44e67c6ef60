import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from lib.ResultLogger import the_result_logger as logger
from Devices.YMTC import CYMTC as CYMTC
from Devices.YMTC import YMTC_CMD as YMTC_CMD


###########################################################################
### Reference Datasheet:
###########################################################################


class CYMTCX39070(CYMTC):
    def __init__(self):
        CYMTC.__init__(self)
        self.DEVICE_MANUFACTURER = "YMTC"
        self.DEVICE_NAME = "X39070"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2 # Eric: per CH
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18368
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 6  # 6 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.WL_NUMBER = 1392
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 363*6 # it is just a number it depends on device size
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # per block

        self.LUN_START_BIT_ADDRESS = 26
        self.BLOCK_START_BIT_ADDRESS = 13
        self.VALID_LUN_MASK = 0x07
        self.VALID_BLOCK_MASK = 0xFFF
        self.VALID_PAGE_MASK = 0x1FFF
        self.ROW_BYTE_ADDRESS = 4

        self.MAX_ERASE_TIME = 20000  # 20 msec
        self.MAX_PROG_TIME = 4000  # 4 msec
        self.MAX_READ_TIME = 200  # 200 usec

        # self.tCS = 25 # Eric: 1000 -> 1500
        # self.tRR = 25 # Eric: 300 -> 20
        # self.tRR_PB = 200 # Eric: 200 -> 350
        # self.tPRE = 70 # nsec Eric: 100 -> 250
        # self.tRRC = 4000
        # self.tRR_SLC = 200
        # self.tRR_FAST = 200

        self.tCS = 400
        self.tRRC = 4000
        self.tRR_PB = 200
        self.tRR = 200
        self.tRR_SLC = 200
        self.tRR_FAST = 200

        self.VCC = 2.5
        self.VCCQ = 1.2
        self.VT_LEVEL_VALID = [[0,4],[1,3,5],[2,6]]
        self.VT_SCAN = [0, 2, 4, 6]

    def get_valid_level_for_page(self, page):
        return self.VT_LEVEL_VALID[page % self.LEVEL_NUMBER]

    def is_level_valid_for_page(self, level, page):
        return level in self.VT_LEVEL_VALID[page % self.LEVEL_NUMBER]

    def calib_setup(self, data_rate_mhz, calib_length, ce = 0, lun = 0):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(ce)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        # opcbuilder.set_column_address(column_address)
        # opcbuilder.set_row_address(row_address)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tADL=400-> tcals=20
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre Eric: 20 -> 25
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_length)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth Eric: 30 -> 50
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.page_buffer_write_builder(opcbuilder)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
        # opcbuilder.set_column_address(column_address)
        # opcbuilder.set_row_address(row_address)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.page_buffer_read_builder(opcbuilder)

        opcbuilder.cleanup()

        return True


    # def mpi_read(self, ce, lun, block_list: [], page_list:[], column_address, page_length):
    #
    #     opcbuilder = nanocycler.opcodebuilder(ce)
    #     opcbuilder.set_ce_low()
    #     opcbuilder.set_wp_high()
    #     hw.custom_sequence_run(opcbuilder)
    #
    #     fuSecBusyTime = []
    #
    #     for block_idx in range(len(block_list)):
    #         block = block_list[block_idx]
    #         page = page_list[(block%4)//2]
    #         res, row_address = CBaseDevice.build_row_address(self, lun, block, page)
    #         opcbuilder.clear()
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #         if self.pmu_algo == CPmuAlgo.Icc1 or self.pmu_algo == CPmuAlgo.IccQ1:
    #             pmu.PMU_START_TRIGGER(opcbuilder) # ICC 1 start
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
    #         opcbuilder.set_column_address(column_address)
    #         # opcbuilder.set_row_address(row_address)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
    #         if block_idx < (len(block_list) - 1):
    #             opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
    #         else:
    #             opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
    #         opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
    #         if self.pmu_algo == CPmuAlgo.Icc1 or self.pmu_algo == CPmuAlgo.IccQ1:
    #             pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #         hw.custom_sequence_run(opcbuilder)
    #
    #         # store read time for next processing
    #         bRes, lRTimeNsec = hw.custom_sequence_get_busy_time()
    #         fuSecBusyTime.append(lRTimeNsec / 1000.0)
    #
    #     for block_idx in range(len(block_list)):
    #         block = block_list[block_idx]
    #         page = page_list[(block % 4) // 2]
    #         CBaseDevice.recall_pattern(self, ce, lun, block, page)
    #         res, row_address = CBaseDevice.build_row_address(self, lun, block, page)
    #         opcbuilder.clear()
    #         opcbuilder.set_ce_low()
    #         opcbuilder.set_wp_high()
    #         opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
    #         opcbuilder.set_column_address(column_address)
    #         # opcbuilder.set_row_address(row_address)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #         opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
    #
    #         if self.CHUNK_NUMBER == 1:
    #             if self.pmu_algo == CPmuAlgo.Icc4 or self.pmu_algo == CPmuAlgo.IccQ4:
    #                 pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
    #             opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
    #             if self.pmu_algo == CPmuAlgo.Icc4 or self.pmu_algo == CPmuAlgo.IccQ4:
    #                 pmu.PMU_STOP_TRIGGER(opcbuilder)    # ICC 4 stop
    #         else:
    #             hw.custom_sequence_run(opcbuilder)
    #             self.logger.reset_result(ce, lun, block, page)
    #             opcbuilder.clear()
    #             opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.CHUNK_LENGTH)
    #             for chunk in range(self.CHUNK_NUMBER):
    #                 hw.custom_sequence_run(opcbuilder)
    #                 self.logger.collect_result(True, chunk == 0)
    #             opcbuilder.clear()
    #
    #         opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
    #         opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
    #         opcbuilder.set_wp_low()
    #         opcbuilder.set_ce_high()
    #         hw.custom_sequence_run(opcbuilder)
    #
    #         # if page == 0:
    #         #     bRes, buffer = hw.custom_sequence_get_out_buffer(16)
    #         #     str = "";
    #         #     for i in range(16):
    #         #         str += "{0};".format(hex(buffer[i]))
    #         #     ws.info("Read: {0}".format(str))
    #
    #         # pass to the logger the ready busy time previously stored and process other results
    #         if self.CHUNK_NUMBER == 1:
    #             self.logger.process_mp_read_result(ce, lun, block, page, fuSecBusyTime[block_idx])
    #         else:
    #             self.logger.process_mp_read_collected_result(fuSecBusyTime[block_idx])
    #
    #     opcbuilder.cleanup()
    #
    #     return True
    #
    #
    # def roic_read(self,ce, lun, block_list: [], page, column_address, page_length):
    #     opcbuilder = nanocycler.opcodebuilder(ce)
    #     opcbuilder.set_ce_low()
    #     opcbuilder.set_wp_high()
    #     hw.custom_sequence_run(opcbuilder)
    #
    #     fuSecBusyTime = []
    #
    #     for block_idx in range(len(block_list)):
    #         block = block_list[block_idx]
    #
    #         res, row_address = CBaseDevice.build_row_address(self, lun, block, page)
    #
    #         opcbuilder.clear()
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #         if self.pmu_algo == CPmuAlgo.Icc1 or self.pmu_algo == CPmuAlgo.IccQ1:
    #             pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
    #         opcbuilder.set_column_address(column_address)
    #         # opcbuilder.set_row_address(row_address)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
    #         if block_idx < (len(block_list) - 1):
    #             opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
    #         else:
    #             opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
    #         opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
    #         if self.pmu_algo == CPmuAlgo.Icc1 or self.pmu_algo == CPmuAlgo.IccQ1:
    #             pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #         hw.custom_sequence_run(opcbuilder)
    #
    #         # store read time for next processing
    #         bRes, lRTimeNsec = hw.custom_sequence_get_busy_time()
    #         fuSecBusyTime.append(lRTimeNsec / 1000.0)
    #
    #     for block_idx in range(len(block_list)):
    #         block = block_list[block_idx]
    #
    #         CBaseDevice.recall_pattern(self, ce, lun, block, page)
    #
    #         bRes, row_address = CBaseDevice.build_row_address(self, lun, block, page)
    #
    #         opcbuilder.clear()
    #         opcbuilder.set_ce_low()
    #         opcbuilder.set_wp_high()
    #         opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
    #         opcbuilder.set_column_address(column_address)
    #         # opcbuilder.set_row_address(row_address)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #         opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
    #
    #         if self.CHUNK_NUMBER == 1:
    #             if self.pmu_algo == CPmuAlgo.Icc4 or self.pmu_algo == CPmuAlgo.IccQ4:
    #                 pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
    #             opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
    #             if self.pmu_algo == CPmuAlgo.Icc4 or self.pmu_algo == CPmuAlgo.IccQ4:
    #                 pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
    #         else:
    #             hw.custom_sequence_run(opcbuilder)
    #             self.logger.reset_result(ce, lun, block, page)
    #             opcbuilder.clear()
    #             opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.CHUNK_LENGTH)
    #             for chunk in range(self.CHUNK_NUMBER):
    #                 hw.custom_sequence_run(opcbuilder)
    #                 self.logger.collect_result(True, chunk == 0)
    #             opcbuilder.clear()
    #
    #         opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
    #         opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
    #         opcbuilder.set_wp_low()
    #         opcbuilder.set_ce_high()
    #         hw.custom_sequence_run(opcbuilder)
    #
    #         # if page == 0:
    #         #     bRes, buffer = hw.custom_sequence_get_out_buffer(16)
    #         #     str = "";
    #         #     for i in range(16):
    #         #         str += "{0};".format(hex(buffer[i]))
    #         #     ws.info("Read: {0}".format(str))
    #
    #         # pass to the logger the ready busy time previously stored and process other results
    #         if self.CHUNK_NUMBER == 1:
    #             self.logger.process_mp_read_result(ce, lun, block, page, fuSecBusyTime[block_idx])
    #         else:
    #             self.logger.process_mp_read_collected_result(fuSecBusyTime[block_idx])
    #
    #     opcbuilder.cleanup()
    #
    #     return True

    def die_configure(self, ch_list: [], ce_list: [], odt = 4, driver_strength = 4):

        # for hard reset
        for ce in ce_list:
            for lun in range(self.LUN_NUMBER):
                self.get_status_enhanced_78h(ch_list, ce, lun, 0)
                self.device_reset(ch_list, [ce], ONFI_CMD.RESET_CMD_FDh)

        self.device_reset(ch_list, ce_list, ONFI_CMD.RESET_CMD_FFh)

        p1 = 0
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x10, p1, p2, p3, p4)

        p1 = 0xf
        p2 = 0x0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        p1 = (odt << 4) | (0x01 << 2) | (0x01 << 1) | (0x01 << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4)

    def is_bad_block(self, ch_list: [], ce, lun, block):
        
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        bRes, lRowAddress = self.build_row_address(lun, block, 0)
        column_address = 0
        
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.SLC_ENTER_CMD_DAh)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        # opcbuilder.set_column_address(column_address)
        # opcbuilder.set_row_address(lRowAddress)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        self.get_status_enhanced_78h(ch_list, ce, lun, block)

        for ch in ch_list:
            self.select_channel(ch)
            sr = hw.custom_sequence_sdr_get_data_byte(0)
            is_bad = (sr & 0x1) != 0
            logger.log_bad_block(ch, ce, lun, block, is_bad)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.SLC_EXIT_CMD_DFh)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    # #################################################
    # DQ Calib training

    # dcc training by set feature
    # def dcc_training(self, ch, ce, lun):

    #     column_address = 0
    #     res, row_address = self.build_row_address(lun, 0, 0)

    #     #ws.info("dcc_training - CH: {0} - CE: {1} .....".format(ch,ce))

    #     ch_mask = self.index_list_to_mask([ch])
    #     ce_mask = self.index_list_to_mask([ce])

    #     opcbuilder = nanocycler.opcodebuilder(0)

    #     self.get_feature([ch], ce, 0x20)
    #     hw.select_channel(ch)
    #     p1 = hw.custom_sequence_sdr_get_data_byte(0)
    #     p2 = hw.custom_sequence_sdr_get_data_byte(1)
    #     p3 = hw.custom_sequence_sdr_get_data_byte(2)
    #     p4 = hw.custom_sequence_sdr_get_data_byte(3)
    #     ws.info("Get Feature 0x20 before DCC: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

    #     self.set_feature([ch], [ce], 0x20, p1 | 1, p2, p3, p4)

    #     opcbuilder.set_ce_low()
    #     opcbuilder.set_wp_high()
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
    #     opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
    #     self.apply_col_address(opcbuilder, column_address)
    #     self.apply_row_address(opcbuilder, row_address)
    #     opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
    #     opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #     opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
    #     opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
    #     opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
    #     opcbuilder.set_ce_high()
    #     opcbuilder.set_wp_low()
    #     hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
    #     p1 = hw.custom_sequence_sdr_get_data_byte(0)
    #     p2 = hw.custom_sequence_sdr_get_data_byte(1)
    #     p3 = hw.custom_sequence_sdr_get_data_byte(2)
    #     p4 = hw.custom_sequence_sdr_get_data_byte(3)
    #     p5 = hw.custom_sequence_sdr_get_data_byte(4)
    #     p6 = hw.custom_sequence_sdr_get_data_byte(5)
    #     p7 = hw.custom_sequence_sdr_get_data_byte(6)
    #     p8 = hw.custom_sequence_sdr_get_data_byte(7)
    #     ws.info("Get output after DCC training: {0} {1} {2} {3} {4} {5} {6} {7}".format(hex(p1), hex(p2), hex(p3), hex(p4), hex(p5), hex(p6), hex(p7), hex(p8)))
    #     opcbuilder.clear()

    #     self.get_status([ch],ce)

    #     hw.select_channel(ch)
    #     sr = hw.custom_sequence_sdr_get_data_byte(0)
    #     ws.info("DCC Training SR: {0}".format(hex(sr)))

    #     # self.set_feature([ch], [ce], 0x20, p1, p2, p3, p4)

    #     opcbuilder.cleanup()
    #     return
    
    # dcc training by set feature
    def dcc_training(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("dcc_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        self.get_feature([ch], ce, 0x20)
        hw.select_channel(ch)
        p1 = hw.custom_sequence_sdr_get_data_byte(0)
        p2 = hw.custom_sequence_sdr_get_data_byte(1)
        p3 = hw.custom_sequence_sdr_get_data_byte(2)
        p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 before DCC: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        self.set_feature([ch], [ce], 0x20, p1 | 0x81, p2, p3, p4)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)     # tWHRT
        # opcbuilder.add(eNandBackdoor.BD_R_NB, 0) # R/B not change based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("DCC Training SR: {0}".format(hex(sr)))

        # self.set_feature([ch], [ce], 0x20, p1, p2, p3, p4)

        opcbuilder.cleanup()
        return
    
    def read_training(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("read_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)    #start DQ(/IO) driving
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)   #DQS signal driven by Nanocycler
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X62)  # read dq training
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)   # LUN stress
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X35)  # invert mask
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X5A)  # first 8bit pattern
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X82)  # second 8bit pattern
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)     # tWHRT Eric: 400 -> 500
        ## opcbuilder.add(eNandBackdoor.BD_R_NB, 0)    #Ready Bot Busy goes low than high 
        # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)    #stop DQ(/IO) driving
        # opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB) #200ns
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)  #set RE_T low
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  #set DQS_T low
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)   #DQS signal driven by device
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE) #100ns
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 16)   #Data out to register        
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)  #set DQS_T high
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)  #set RE_T high
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        hw.select_channel(ch)
        buffer = self.get_read_buffer(ch, 16) #self.PAGE_LENGTH
        ws.info("Read Training page buffer:{0}".format(self.format_array(buffer)))

        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("Read Training SR: {0}".format(hex(sr)))

        opcbuilder.cleanup()
        return

    def write_training_Tx_side(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("dcc_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        self.get_feature([ch], ce, 0x20)
        hw.select_channel(ch)
        p1 = hw.custom_sequence_sdr_get_data_byte(0)
        p2 = hw.custom_sequence_sdr_get_data_byte(1)
        p3 = hw.custom_sequence_sdr_get_data_byte(2)
        p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 before Write: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        self.set_feature([ch], [ce], 0x20, p1, p2, p3 | 0xF, p4)

        # self.get_feature([ch], ce, 0x20)
        # hw.select_channel(ch)
        # p1 = hw.custom_sequence_sdr_get_data_byte(0)
        # p2 = hw.custom_sequence_sdr_get_data_byte(1)
        # p3 = hw.custom_sequence_sdr_get_data_byte(2)
        # p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 during Write: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X63)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, 128)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X64)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tWHRT
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB) #200ns
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 128)
        
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        hw.select_channel(ch)
        buffer = self.get_read_buffer(ch, 128) #self.PAGE_LENGTH
        ws.info("Write Tx Training page buffer:{0}".format(self.format_array(buffer)))
        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("Write Tx Training SR: {0}".format(hex(sr)))

        self.set_feature([ch], [ce], 0x20, p1, p2, p3, p4)

        opcbuilder.cleanup()
        return

    # #################################################
    # Just for debug ...
    #

    def page_buffer_write(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PBW", ch_list, ce_list, lun_list, [block], page)


    def page_buffer_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # buffer = self.get_read_buffer(ch_list[0], 64) #self.PAGE_LENGTH)
        # ws.info("{0}".format(self.format_array(buffer)))

        logger.log_read("PBR", ch_list, ce, lun, [block], page)



    def edge_wl_group(self, edge_wl):
        if 0 <= edge_wl <= 56:
            return range(101, 126), 5       # Group5: Open WL0-56 edge WL, RR: [101-125]
        elif 57 <= edge_wl <= 116:
            return range(126, 151), 6       # Group6: Open WL57-116 edge WL, RR: [126-150]
        elif 117 <= edge_wl <= 172:
            return range(151, 176), 7       # Group7: Open WL117-172 edge WL, RR: [151-175]
        else:
            return range(26, 51), 2         # Group2: Open WL173-231 edge WL, RR: [26-50]
        
    def inner_wl_group(self, inner_wl):
        if 0 <= inner_wl <= 56:
            return range(26, 51), 2
        elif 57 <= inner_wl <= 116:
            return range(51, 76), 3
        elif 117 <= inner_wl <= 172:
            return range(76, 101), 4
        else:
            return range(1, 26), 1
        
    # def inner_wl_group(self, inner_wl):
    #     if inner_wl <=56:
    #         return 2
    #     elif inner_wl <=116:
    #         return 3
    #     elif inner_wl <=172:
    #         return 4
    #     else:
    #         return 1