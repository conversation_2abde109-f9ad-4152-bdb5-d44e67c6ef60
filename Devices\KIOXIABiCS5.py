import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
# from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
# from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultMgr import the_result_logger as logger
# from lib.ResultMgr import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.Toshiba import CToshiba as CToshiba


###########################################################################
### Reference Datasheet: TH58LKTxY25BAxx_132BGA_D_20210218_0.2(TLC-2PL).pdf
###########################################################################

class CKIOXIABiCS5(CToshiba):
    def __init__(self):
        CToshiba.__init__(self)
        self.DEVICE_MANUFACTURER = "KIOXIA"
        self.DEVICE_NAME = "BiCS5"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2 
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 2  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER = 2    # TH58LKT0Y25BA4C, only 1 lun
        self.BLOCK_NUMBER = 3324
        self.WL_NUMBER = 448
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # 1344 per block

        self.LUN_START_BIT_ADDRESS = 22
        self.BLOCK_START_BIT_ADDRESS = 9
        self.VALID_LUN_MASK = 0x03
        self.VALID_WL_MASK = 0x1FF
        self.VALID_BLOCK_MASK = 0x1FFF
        
        self.VCC = 2.5
        self.VCCQ = 1.2
        self.VT_LEVEL_VALID = [[3],[0,2,5],[1,4,6]]

    def get_valid_level_for_page(self, page):
        return self.VT_LEVEL_VALID[page % self.LEVEL_NUMBER]

    def is_level_valid_for_page(self, level, page):
        return level in self.VT_LEVEL_VALID[page % self.LEVEL_NUMBER]

    # Figure 4‐2. SDR to DDR, DDR to SDR Mode Transition Diagram
    def die_configure(self, ch_list: [], ce_list: [], odt = 4, driver_strength = 4):

        self.device_reset(ch_list, ce_list)

        # Driver strength 10h
        # 00h~01h Reserved
        # 02h Ron = Driver Multiplier : Underdrive
        # 03h Reserved
        # 04h Ron = 35 Driver Multiplier : 1 (default)
        # 05h Reserved
        # 06h Ron = Driver Multiplier : Overdrive 1
        # 07h~FFh Reserved
        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        lAddress = 0x10
        self.set_feature(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # AIPR configuration
        lAddress = 0x91
        p1 = 0x01 if self.aipr_mode else 0x00
        p2 = p3 = p4 = 0
        for lun in range(0, self.LUN_NUMBER):
            self.set_feature_by_lun(ch_list, ce_list, lun, lAddress, p1, p2, p3, p4)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        odt_bit = odt & 0xF
        re_c_bit = 1
        dqs_c_bit = 1
        vref_bit = 1
        p1 = (odt_bit << 4) | (re_c_bit << 2) | (dqs_c_bit << 1) | (vref_bit << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        lAddress = 0x02
        self.set_feature(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # External VPP configuration
        p1 = 0
        # if self.external_vpp_enable:
        #     p1 = 1
        #     hw.set_vpp_on()
        # else:
        #     hw.set_vpp_off()
        p2 = p3 = p4 = 0
        lAddress = 0x30
        self.set_feature(ch_list, ce_list, lAddress, p1, p2, p3, p4)

    # #############################################################################à
    # Read Retry

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):

        if self.option_buffer:

            if retry_option >= len(self.option_buffer):
                return False, ""

            #ws.info(">>> Option {0}/{1}: {2}".format(retry_option, len(self.option_buffer), self.option_buffer[retry_option]))

            # csv file should contains lines of 7 parameters
            if len(self.option_buffer[retry_option]) < 7:
                return False, ""

            self.set_feature_by_lun(ch_list, [ce], lun, 0x89, int(self.option_buffer[retry_option][0], 16),
                                           int(self.option_buffer[retry_option][2], 16),
                                           int(self.option_buffer[retry_option][3], 16),
                                           int(self.option_buffer[retry_option][5], 16))
            self.set_feature_by_lun(ch_list, [ce], lun, 0x8A, int(self.option_buffer[retry_option][1], 16),
                                           int(self.option_buffer[retry_option][4], 16),
                                           int(self.option_buffer[retry_option][6], 16),
                                           0) # according spec P3 of address 0x8A is always zero

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""


    def reset_read_retry_option(self, ch_list: [], ce, lun):

        self.set_feature_by_lun(ch_list, [ce], lun, 0x89, 0x00, 0x00, 0x00, 0x00)
        self.set_feature_by_lun(ch_list, [ce], lun, 0x8A, 0x00, 0x00, 0x00, 0x00)

        return True


    # #############################################################################à
    # Read Offset

    def set_read_offset_code(self, ch_list, ce, lun, level, offset):
        if level > 6:
            return False, 0

        # in order to have linear scan from min offeset to max offset
        offset_code = self.offset_to_code(offset)
        offset_voltage = offset
        address = 0x89

        # TLC
        if level == 0:
            address = 0x89
            self.set_feature_by_lun(ch_list, [ce], lun, address, offset_code, 0x00, 0x00, 0x00)
        if level == 1:
            address = 0x8A
            self.set_feature_by_lun(ch_list, [ce], lun, address, offset_code, 0x00, 0x00, 0x00)
        if level == 2:
            address = 0x89
            self.set_feature_by_lun(ch_list, [ce], lun, address, 0x00, offset_code, 0x00, 0x00)
        if level == 3:
            address = 0x89
            self.set_feature_by_lun(ch_list, [ce], lun, address, 0x00, 0x00, offset_code, 0x00)
        if level == 4:
            address = 0x8A
            self.set_feature_by_lun(ch_list, [ce], lun, address, 0x00, offset_code, 0x00, 0x00)
        if level == 5:
            address = 0x89
            self.set_feature_by_lun(ch_list, [ce], lun, address, 0x00, 0x00, 0x00, offset_code)
        if level == 6:
            address = 0x8A
            self.set_feature_by_lun(ch_list, [ce], lun, address, 0x00, 0x00, offset_code, 0x00)

        # self.get_feature_by_lun(ch_list, ce, lun, address)

        return True, offset_voltage

    def set_read_offset_code_multi_level(self, ch, ce, lun, page, offset4level):

        dacs = [0,0,0]
        for level in self.get_valid_level_for_page(page):
            dacs[self.get_valid_level_for_page(page).index(level)] = self.offset_to_code(offset4level[level])
        if page == 0:
            self.set_feature_by_lun([ch], [ce], lun, 0x89, 0, 0, dacs[0], 0)  # lower
        elif page == 1:
            self.set_feature_by_lun([ch], [ce], lun, 0x89, dacs[0], dacs[1], 0, dacs[2])  # middle
        elif page == 2:
            self.set_feature_by_lun([ch], [ce], lun, 0x8A, dacs[0], dacs[1], dacs[2], 0)  # upper
        else:
            raise Exception("Wrong Page!")

        return True

    def set_read_offset_page(self, ch_list: [], ce, lun, page, dacs):

        if page == 0:
            offset_code1 = self.offset_to_code(dacs[0])
            self.set_feature_by_lun(ch_list, [ce], lun, 0x89, 0x00, 0x00, offset_code1, 0x00)  # lower
        elif page == 1:
            offset_code1 = self.offset_to_code(dacs[0])
            offset_code2 = self.offset_to_code(dacs[1])
            offset_code3 = self.offset_to_code(dacs[2])
            self.set_feature_by_lun(ch_list, [ce], lun, 0x89, offset_code1, offset_code2, 0x00, offset_code3)  # middle
        elif page == 2:
            offset_code1 = self.offset_to_code(dacs[0])
            offset_code2 = self.offset_to_code(dacs[1])
            offset_code3 = self.offset_to_code(dacs[2])
            self.set_feature_by_lun(ch_list, [ce], 0x8A, lun, offset_code1, offset_code2, offset_code3, 0x00)  # upper
        else:
            raise Exception("Wrong Page!")

        return True
