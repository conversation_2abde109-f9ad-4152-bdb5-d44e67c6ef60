// cal_pattern.h - Pattern calculation library for bit operations
#ifndef CAL_PATTERN_H
#define CAL_PATTERN_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>

/**
 * Calculate page FBC with XOR operation
 */
void xor_cal_page_fbc_pattern_and_fbc(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    uint8_t *fbc_pattern,
    size_t len,
    size_t *page_fbc_16KB,
    size_t *page_fbc_4KB_list);

/**
 * Count the number of set bits (1s) in a buffer
 */
int64_t get_bit_count(const uint8_t *buf, size_t len);

/**
 * AND operation for up to three patterns with optional inversion
 */
void and_cal_state_cell_pattern(
    const uint8_t *pattern1, const char *operation1,
    const uint8_t *pattern2, const char *operation2,
    const uint8_t *pattern3, const char *operation3,
    uint8_t *result, size_t len);

/**
 * AND operation for two patterns, result written to result
 */
void and_cal_state_fbc_pattern(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    uint8_t *result,
    size_t len);

/**
 * AND operation for two patterns, return total number of 1s
 */
int64_t and_cal_state_fbc(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    size_t len);

#ifdef __cplusplus
}
#endif

#endif // CAL_PATTERN_H