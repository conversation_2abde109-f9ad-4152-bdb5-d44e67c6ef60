from nanocycler import ncdb as ncdb
# from nanocycler import ws as ws

class PATTERN_SEED:
    """ Codes stored in pattern manager database, positive or null number are used to store the seed of the Random Generator.
    The seed is stored for block, the page seed is calculated as block seed plus the page index """

    VIRGIN = -128
    """ Virgin (not tested) block """

    USER = -1
    """ it indicates the block was programmed using the user buffer """

    ALL0 = -2
    """ it indicates the block was programmed all0 """

    ALL1 = -3
    """ it indicates the the block was erased, all 1 """

    AA_55 = -4
    """ it indicates the block was programmed alternation of 0xAA and 0x55 pattern """

    FF_00 = -5
    """ it indicates the block was programmed alternation of 0xFF and 0x00 pattern """

    INCREMENTAL = -6
    """ it indicates the block was programmed with incremental (0,1,2,...254,255,0,1, ..) pattern """




class eBadBlockCode:
    GoodBlock = 0
    FactoryBadBlock = 1
    FunctionalBadBlock = 2

class CDataMgr:
    """A class to manage:
    > the current pattern selection. For each channel, ce, lun and block the class stores the last programmed (or erased) pattern seed.
    > the bad blocks. For each channel, ce, lun the class stores the list of bad blocks.
    The class can store and load info from local and central database according the unique id of the die """

    def __init__(self):

        # create a dictionary for the 2 channels
        self.seed_dict = {
            0: {},
            1: {}
        }

        # allocate for 2 channels
        self.bb_dict = {
            0: {},
            1: {}
        }

    @staticmethod
    def insert_or_update_device(uid, dev_name, dev_id, dev_model, manufacturer):
        ncdb.insert_or_update_device(uid, dev_name, dev_id, dev_model, manufacturer)

    @staticmethod
    def load_from_central(uid):
        res = True
        for r in range(10):
            res = ncdb.load_from_central(uid)
            if res:
                break
        if not res:
            Exception("load_from_central max retry fails")

    @staticmethod
    def store_to_central(uid):
        res = True
        for r in range(10):
            res = ncdb.store_to_central(uid)
            if res:
                break
        if not res:
            Exception("store_to_central max retry fails")


    def set_seed(self, ch, ce, lun, block, seed):
        if not ce in self.seed_dict[ch]:
            self.seed_dict[ch].update({ce: {}})
        if not lun in self.seed_dict[ch][ce]:
            self.seed_dict[ch][ce].update({lun: {}})
        self.seed_dict[ch][ce][lun].update({block: seed})
        return 0


    def get_seed(self, ch, ce, lun, block):
        if not ce in self.seed_dict[ch]:
            return PATTERN_SEED.VIRGIN
        if not lun in self.seed_dict[ch][ce]:
            return PATTERN_SEED.VIRGIN
        if not block in self.seed_dict[ch][ce][lun]:
            return PATTERN_SEED.VIRGIN

        seed = self.seed_dict[ch][ce][lun][block]
        return seed


    # load last programmed seed for each block from database for a given  device unique id on a specific channel
    def load_seeds(self, ch, ce, uid):

        # check for valid (not empty) uid
        if not uid:
            return False

        self.seed_dict[ch].update({ce: {'uid': uid}})

        res, seed_dict = ncdb.pattern_seed_load(uid)

        for lun, blk4lun in seed_dict.items():
            for block, seed in blk4lun.items():
                self.set_seed(ch, ce, lun, block, seed)
                # ws.info("***************load_seeds: ch({0}) - ce({1}) - lun({2}) - block({3}) - seed({4})".format(ch, ce, lun, block, seed))

        return True


    # store last programmed pattern into the central database
    def store_seeds(self):

        for ch, ps4ch in self.seed_dict.items():
            for ce, ps4ce in ps4ch.items():
                # without uid we can't store
                if 'uid' in ps4ce.keys():
                    uid = ps4ce['uid']

                    ncdb.pattern_seed_remove_all(uid)

                    ncdb.pattern_seed_batch_init(uid)
                    for k, ps4lun in ps4ce.items():
                        if k == 'uid':
                            continue
                        lun = k
                        for block, seed in ps4lun.items():
                            # ncdb.pattern_seed_store(uid, lun, block, seed)
                            ncdb.pattern_seed_batch_store(lun, block, seed)
                            # ws.info("***************store_seeds: uid({0}) - lun({1}) - block({2}) - seed({3})".format(uid, lun, block, seed))
                    ncdb.pattern_seed_batch_commit()
    

    def add_bad_block(self, ch: int, ce: int, lun: int, block: int, category: int = eBadBlockCode.FactoryBadBlock):

        if not ce in self.bb_dict[ch]:
            self.bb_dict[ch].update({ce: {}})
        if not lun in self.bb_dict[ch][ce]:
            self.bb_dict[ch][ce].update({lun: {}})
        self.bb_dict[ch][ce][lun].update({block: category})
        return 0


    def is_bad_block(self, ch, ce, lun, block):
        if not ce in self.bb_dict[ch]:
            return False
        if not lun in self.bb_dict[ch][ce]:
            return False
        if not block in dict(self.bb_dict[ch][ce][lun]):
            return False
        return True


    def get_bad_block_category(self, ch, ce, lun, block):
        if self.is_bad_block(ch, ce, lun, block):
            _category = self.bb_dict[ch][ce][lun][block]
            return _category
        else:
            return eBadBlockCode.GoodBlock


    # load bad block from database for specific device unique_id on a specific channel
    def load_bad_blocks(self, ch, ce, uid):

        self.bb_dict[ch].update({ce: {'uid': uid}})

        res, bad_block_list = ncdb.bad_block_load(uid)
        for lun, block4lun in bad_block_list.items():
            for block, category in block4lun:
                self.add_bad_block( ch, ce, lun, block, category )

        return True


    def store_bad_blocks(self):

        for ch, bb4ch in self.bb_dict.items():
            for ce, bb4ce in bb4ch.items():
                # without uid we can't store
                if 'uid' in bb4ce.keys():
                    uid = bb4ce['uid']

                    ncdb.bad_block_remove_all(uid)

                    ncdb.bad_block_batch_init(uid)
                    for k, bb4lun in bb4ce.items():
                        if k == 'uid':
                            continue
                        lun = k

                        for block, category in bb4lun.items():
                            # ncdb.bad_block_store(uid, lun, block, category)
                            ncdb.bad_block_batch_store(lun, block, category)
                            # ws.info("***************store_bad_blocks: uid({0}) - lun({1}) - block({2}) - category({3})".format(uid, lun, block, category))
                    ncdb.bad_block_batch_commit()


""" The static instance of the data manager class """
the_data_mgr = CDataMgr()
