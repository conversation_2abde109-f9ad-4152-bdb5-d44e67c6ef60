
from nanocycler import hardware as hw
from nanocycler import enumPmuChannel as enumPmuChannel
from nanocycler import opcodebuilder as opcodebuilder
from nanocycler import enumNandBackdoor as eNandBackdoor

MAX_SAMPLES_COUNT = 2048
PMU_SAMPLE_TIME = 50  # 20Mhz sampling -> 50 nsec

PMU_AVG = 100
PMU_MIN_AVG = 20

def PMU_SAMPLE_USEC_PERIOD():
    return PMU_AVG * PMU_SAMPLE_TIME / 1000

def PMU_SETUP(ePmuChannel: enumPmuChannel, usecMaxTime: int):
    global PMU_AVG
    for i in range(1, 1001, 1): # from 1usec to 1msec period length
        PMU_AVG = i * PMU_MIN_AVG
        if (PMU_AVG * PMU_SAMPLE_TIME * MAX_SAMPLES_COUNT) >= (usecMaxTime * 1000):
            break
    hw.pmu_offset_compensation(ePmuChannel)
    hw.pmu_setup(ePmuChannel, hw.enumPmuTriggerMode.PmuHardware, MAX_SAMPLES_COUNT, PMU_AVG)


def PMU_START_TRIGGER(builder: opcodebuilder):
    builder.add(eNandBackdoor.BD_PMU_START_TRIGGER, 0)


def PMU_STOP_TRIGGER(builder: opcodebuilder):
    builder.add(eNandBackdoor.BD_PMU_STOP_TRIGGER, 0)


def PMU_GET_AVG_CURRENT():
    return hw.pmu_get_average_of_samples()


def PMU_GET_MAX_CURRENT():
    return hw.pmu_get_max_of_samples()


def PMU_GET_SAMPLES(afSamples, lBufferLength: int):
    return hw.pmu_get_samples(afSamples, lBufferLength)
