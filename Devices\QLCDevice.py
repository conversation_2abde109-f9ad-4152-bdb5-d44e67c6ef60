import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM



###########################################################################
### Device specific
###########################################################################

class CQLC_CMD:
    PROGRAMMING_STEP_1_0Dh = 0x0D



############################################################
### the Device generic class
############################################################

class CQLCDevice(COnfiDevice):
    """This is the base class for a generic Nand device. Each device class should inherit from this class.
     It contains the default implementation of several Nand function. Overriding a method it is possible to
     customize the implementation of a function for a specific device.
      """
    def __init__(self):
        COnfiDevice.__init__(self)
        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_MANUFACTURER = "NandManufacturer"
        self.DEVICE_NAME = "QLCDevice"
        self.LEVEL_NUMBER = 4  # QLC 4 levels

        # ##########################################################
        # # change this configuration according device parameters
        # Toshiba QLC like
        self.DEVICE_CE_NUMBER = 2
        self.toshiba_like = True
        self.PAGE_LENGTH = 18976
        self.PLANE_NUMBER = 2
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.WL_NUMBER = 448
        self.PAGE_NUMBER = self.LEVEL_NUMBER * self.WL_NUMBER
        self.LUN_START_BIT_ADDRESS = 23
        self.BLOCK_START_BIT_ADDRESS = 9
        self.VALID_LUN_MASK = 0x01
        self.VALID_PAGE_MASK = 0x1FF
        self.VALID_BLOCK_MASK = 0x3FFF
        self.PAGE_PARAM_ADDR = 0x40
        # ##########################################################

        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.MAX_ERASE_TIME = 15000 # 15 msec
        self.MAX_PROG_TIME = 10000 # 10 msec
        self.MAX_READ_TIME = 1000 # 1 msec

        self.VCC = 2.5
        self.VCCQ = 1.2

        self.PROGRAM_STEPS = 2


    # ###########################################################
    # QLC  Program Sequence

    def qlc_program_page(self, ch_list: [], ce_list: [], lun_list: [], block, wordline, page_in_wl, program_step):

        page = wordline * self.LEVEL_NUMBER + page_in_wl

        # ws.info(">>> Program Step {0} - WL: {1} - String: {2} - Page: {3}".format(program_step, wordline, page_in_wl, page))

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)
            column = 0

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if program_step == 0:
                opcbuilder.add(eNandBackdoor.BD_CLE, CQLC_CMD.PROGRAMMING_STEP_1_0Dh)
            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, page_in_wl + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            # be carefull in case of multi block multi lun only the last will be acquired
            if bI4:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if (page_in_wl + 1) == self.LEVEL_NUMBER:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG_STP_{0}".format(program_step + 1), ch_list, ce_list, lun_list, [block], page, en_i=bI2 or bI4)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list : []):
        """
        QLC single plane program block.\n
        Two step programming of all wordline selected using single plane algorithm.
        The routine calculates the first and last wordline for the selected pages,
        then all pages belonging to these wordline range will be programmed.
        """

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)

        for wl in range(wl_min, wl_max+1):
            for program_step in range(self.PROGRAM_STEPS):
                for page_in_wl in range(self.LEVEL_NUMBER):
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, wl, page_in_wl, program_step)

        # optimized code for QLC program
        # for wl in range(wl_min, 4):
        #     step_0_prog_wl = wl
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_program_page(ch_list, ce_list, lun_list, block, step_0_prog_wl, page_in_wl, 0)
        #
        # for wl in range(max(wl_min, 4), min(448, wl_max + 1 + 4)):
        #     step_0_prog_wl = wl
        #     step_1_prog_wl = wl - 4
        #
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_program_page(ch_list, ce_list, lun_list, block, step_0_prog_wl, page_in_wl, 0)
        #
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_program_page(ch_list, ce_list, lun_list, block, step_1_prog_wl, page_in_wl, 1)
        #
        # for wl in range(448, wl_max + 1 + 4):
        #     step_1_prog_wl = wl - 4
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_program_page(ch_list, ce_list, lun_list, block, step_1_prog_wl, page_in_wl, 1)


    # ###########################################################
    # QLC  Multiplane Program Sequence

    def qlc_multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], wordline, page_in_wl, program_step):

        page = wordline * self.LEVEL_NUMBER + page_in_wl

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)
                column = 0

                # pattern is the same between ch and ce use the first
                # CBaseDevice.recall_pattern(self, ce_list[0], lun, block, page)
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2)  # 2 to use register seed

                if program_step == 0:
                    opcbuilder.add(eNandBackdoor.BD_CLE, CQLC_CMD.PROGRAMMING_STEP_1_0Dh)
                if self.toshiba_like:
                    opcbuilder.add(eNandBackdoor.BD_CLE, page_in_wl + 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                # be carefull in case of multi block multi lun only the last will be acquired
                if bI4:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if page_in_wl + 1 == self.LEVEL_NUMBER:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("MP_PRG_STP_{0}".format(program_step + 1), ch_list, ce_list, lun_list, block_list, page, en_i=bI2 or bI4)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):
        """
        QLC multi plane plane program block.\n
        Two step programming of all word line selected using multi plane algorithm.
        The routine calculates the first and last wordline for the selected pages,
        then all pages belonging to these wordline range will be programmed.
        """

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)

        for wl in range(wl_min, wl_max+1):
            for program_step in range(self.PROGRAM_STEPS):
                for page_in_wl in range(self.LEVEL_NUMBER):
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, wl, page_in_wl, program_step)

        # optimized code for QLC program
        # for wl in range(wl_min, 4):
        #     step_0_prog_wl = wl
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, step_0_prog_wl, page_in_wl, 0)
        #
        # for wl in range(max(wl_min, 4), min(448, wl_max + 1 + 4)):
        #     step_0_prog_wl = wl
        #     step_1_prog_wl = wl - 4
        #
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, step_0_prog_wl, page_in_wl, 0)
        #
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, step_1_prog_wl, page_in_wl, 1)
        #
        # for wl in range(448, wl_max + 1 + 4):
        #     step_1_prog_wl = wl - 4
        #     for page_in_wl in range(self.LEVEL_NUMBER):
        #         self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, step_1_prog_wl, page_in_wl, 1)


    # #################################################
    # ERASE

    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block], en_i=bI3)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                opcbuilder.set_row_address(row_address)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    # #################################################
    # PAGE READ

    def get_read_buffer(self, ch, buffer_length):
        hw.select_channel(ch)
        bRes, buffer = hw.custom_sequence_get_out_buffer(buffer_length)
        return buffer

    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        if self.toshiba_like:
            opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # the sequence will be executed by run_parallel in order to run time change the ce
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start
        if self.toshiba_like:
            opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        if bI4r:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        if bI4r:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)

        opcbuilder.cleanup()

    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list : [], page, column, page_length):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            if self.toshiba_like:
                opcbuilder.add(eNandBackdoor.BD_CLE, (page % self.LEVEL_NUMBER) + 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName="tR", en_fails=False, en_rnb=True,
                        en_i=bI1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)
            column_address = column

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False, en_i=bI4r)

        opcbuilder.cleanup()
