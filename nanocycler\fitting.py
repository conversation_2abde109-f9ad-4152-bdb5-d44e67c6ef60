from nanocycler import ws as ws
from nanocycler import hardware as hw

def fit_curve_and_find_minimum_poly(x_data, y_data, poly_degree=3):
    """
    对给定的x_data和y_data进行多项式曲线拟合,并找出拟合曲线的最低点
    
    参数:
    x_data: x轴数据,例如vt_list
    y_data: y轴数据,例如fail_list
    poly_degree: 多项式的阶数,默认为3(三次多项式)
    
    返回:
    x_min: 拟合曲线最低点的x坐标
    y_min: 拟合曲线最低点的y坐标
    coeffs: 多项式系数
    """
    x = x_data
    y = y_data

    #多项式拟合
    coeffs = poly_fit_with_method(x, y, poly_degree)

    # 在数据范围内找到最小值点
    x_range = max(x_data) - min(x_data)
    x_min, y_min = find_min_poly(coeffs, min(x_data), max(x_data))

    return x_min, y_min, coeffs

def poly_fit_with_method(x_data, y_data, degree=3, method='original', epsilon=1e-10):
    """
    使用不同的求解方法进行多项式拟合
    :param x_data: x坐标列表
    :param y_data: y坐标列表
    :param degree: 多项式次数
    :param method: 求解方法，可选 'original', 'safe', 'pivot'
    :param epsilon: 数值稳定性的最小阈值
    :return: 多项式系数，从高到低排列
    """
    # 确保x和y的长度相同
    if len(x_data) != len(y_data):
        ws.info("x and y data point number are different")
        return None
    # 确保有足够的数据点
    if len(x_data) <= degree:
        ws.info("data point number must be greater than polynomial degree {degree}")
        return None
    
    n = len(x_data)
    
    # 检查x数据中是否包含零值
    has_zero = any(abs(x) < epsilon for x in x_data)
    
    if has_zero:
        # 对x值应用微小偏移，避免精确零值
        x_shifted = [x + epsilon if abs(x) < epsilon else x for x in x_data]
        # ws.info("has zero in x_data, apply small offset to avoid exact zero")
    else:
        x_shifted = x_data
    
    # 创建方程组的系数矩阵
    matrix = []
    for i in range(degree + 1):
        row = []
        for j in range(degree + 1):
            # 计算 Σ(x^(i+j))
            sum_x = sum(x**(i+j) for x in x_shifted)
            # 为对角线元素添加正则化项，提高数值稳定性
            if i == j:
                sum_x += epsilon
            row.append(sum_x)
        # 计算 Σ(y*x^i)
        sum_xy = sum(y * (x**i) for x, y in zip(x_shifted, y_data))
        row.append(sum_xy)
        matrix.append(row)
    
    # 根据选择的方法求解线性方程组
    if method == 'original':
        try:
            coeffs = gaussian_elimination(matrix)
        except:
            ws.info("original gaussian elimination failed, try safe version")
            coeffs = gaussian_elimination_safe(matrix, epsilon)
    elif method == 'safe':
        coeffs = gaussian_elimination_safe(matrix, epsilon)
    elif method == 'pivot':
        coeffs = gaussian_elimination_pivot(matrix, epsilon)
    else:
        ws.info("unknown method: {}, use safe version".format(method))
        coeffs = gaussian_elimination_safe(matrix, epsilon)
    
    if coeffs is None:
        ws.info("cannot solve polynomial coefficients, maybe data cause matrix singular")
        # 降级到简单线性拟合或常数拟合
        if degree > 1:
            ws.info("try downgrade to linear fit")
            return poly_fit_with_method(x_data, y_data, 1, method, epsilon)
        else:
            ws.info("use constant fit (mean)")
            avg = sum(y_data) / len(y_data)
            return [0, avg]  # 一阶常数多项式 [0*x + avg]
    
    return coeffs[::-1]  # 返回从高次项到常数项的系数

def calculate_matrix_norm(matrix, norm_type='frobenius'):
    """
    计算矩阵范数
    :param matrix: 输入矩阵
    :param norm_type: 范数类型 ('frobenius' 或 'one')
    :return: 矩阵范数
    """
    if norm_type == 'frobenius':
        # Frobenius范数：所有元素平方和的平方根
        return sum(sum(x*x for x in row) for row in matrix) ** 0.5
    elif norm_type == 'one':
        # 1范数：列和的最大值
        n = len(matrix)
        col_sums = [sum(abs(matrix[i][j]) for i in range(n)) for j in range(n)]
        return max(col_sums)

def matrix_inverse(matrix):
    """
    计算矩阵的逆
    :param matrix: 输入矩阵
    :return: 矩阵的逆
    """
    n = len(matrix)
    # 创建增广矩阵 [A|I]
    aug = [row[:] + [1 if i == j else 0 for j in range(n)] for i, row in enumerate(matrix)]
    
    # 高斯-约当消元
    for i in range(n):
        # 找主元
        pivot = aug[i][i]
        if abs(pivot) < 1e-10:
            raise ValueError("Matrix is singular")
        
        # 归一化当前行
        for j in range(i, 2*n):
            aug[i][j] /= pivot
        
        # 消元
        for k in range(n):
            if k != i:
                factor = aug[k][i]
                for j in range(i, 2*n):
                    aug[k][j] -= factor * aug[i][j]
    
    # 提取逆矩阵部分
    return [[aug[i][j+n] for j in range(n)] for i in range(n)]

def calculate_condition_number(matrix):
    """
    手动计算矩阵的条件数
    :param matrix: 输入矩阵
    :return: 条件数
    """
    try:
        # 计算矩阵的范数
        matrix_norm = calculate_matrix_norm(matrix)
        
        # 计算逆矩阵的范数
        inverse = matrix_inverse(matrix)
        inverse_norm = calculate_matrix_norm(inverse)
        
        # 条件数是这两个范数的乘积
        return matrix_norm * inverse_norm
    except:
        return float('inf')  # 如果矩阵奇异或接近奇异

def gaussian_elimination(matrix):
    """
    优化的高斯消元法，添加进度报告和超时检测
    :param matrix: 增广矩阵（系数矩阵加上常数项）
    :return: 方程的解
    """
    n = len(matrix)
    start_time = hw.get_nsec_time()
    last_report_time = start_time
    ws.info("Gaussian elimination started")
    # 或使用手动计算版本
    cond_num = calculate_condition_number([row[:-1] for row in matrix])
    ws.info("condition count: {}".format(cond_num))
    
    # 创建矩阵副本避免修改原始数据
    matrix = [row[:] for row in matrix]
    
    # 消元过程（前向消元）
    for i in range(n):
        # 每500ms报告一次进度，保持设备连接活跃
        current_time = hw.get_nsec_time()
        if current_time - last_report_time > 500:  # 500ms报告一次
            progress = (i + 1) / n * 100
            ws.info("Gaussian elimination progress: {:.1f}%".format(progress))
            last_report_time = current_time
            
            # 检查是否超时（比如10秒）
            if current_time - start_time > 10000000000:  # 10秒
                ws.error("Gaussian elimination timeout")
                raise TimeoutError("Gaussian elimination took too long")
        
        # 寻找主元
        max_row = i
        max_val = abs(matrix[i][i])
        for k in range(i + 1, n):
            if abs(matrix[k][i]) > max_val:
                max_row = k
                max_val = abs(matrix[k][i])
        
        # 如果主元太小，可能导致数值不稳定
        if max_val < 1e-10:
            ws.warning("Small pivot detected: {max_val}")
        
        # 交换行
        if max_row != i:
            matrix[i], matrix[max_row] = matrix[max_row], matrix[i]
        
        # 消元，使用向量化操作提高效率
        pivot = matrix[i][i]
        for j in range(i + 1, n):
            factor = matrix[j][i] / pivot
            for k in range(i, n + 1):
                matrix[j][k] -= factor * matrix[i][k]
    
    # 回代过程（后向代入）
    solution = [0] * n
    for i in range(n - 1, -1, -1):
        solution[i] = matrix[i][n]
        for j in range(i + 1, n):
            solution[i] -= matrix[i][j] * solution[j]
        solution[i] /= matrix[i][i]
        
        # 检查解的合理性
        if abs(solution[i]) > 1e6:  # 如果系数太大，可能有问题
            ws.warning("Large coefficient detected: {}".format(solution[i]))
    
    total_time = hw.get_nsec_time() - start_time
    ws.info("Gaussian elimination completed in {}ms".format(total_time/1000000))
    
    return solution

def gaussian_elimination_safe(matrix, epsilon=1e-10):
    """
    使用安全的高斯消元法求解线性方程组，处理零元素
    :param matrix: 增广矩阵（系数矩阵加上常数项）
    :param epsilon: 数值稳定性的最小阈值
    :return: 方程的解或None（如果无法求解）
    """
    n = len(matrix)
    
    # 创建矩阵的副本，避免修改原始数据
    mat = [row[:] for row in matrix]
    
    # 消元过程（前向消元）
    for i in range(n):
        # 寻找主元
        max_row = i
        max_val = abs(mat[i][i])
        
        for k in range(i + 1, n):
            if abs(mat[k][i]) > max_val:
                max_row = k
                max_val = abs(mat[k][i])
        
        # 检查主元是否接近零
        if max_val < epsilon:
            # 尝试在整列中寻找非零元素
            found_nonzero = False
            for k in range(i, n):
                if abs(mat[k][i]) >= epsilon:
                    max_row = k
                    found_nonzero = True
                    break
            
            # 如果找不到非零元素，矩阵可能奇异
            if not found_nonzero:
                ws.info("warning: all elements in column {i+1} are close to zero, matrix may be singular")
                return None
        
        # 交换行
        if max_row != i:
            mat[i], mat[max_row] = mat[max_row], mat[i]
        
        # 再次检查主元
        if abs(mat[i][i]) < epsilon:
            # 增加对角元素的微小扰动，确保数值稳定性
            sign = 1 if mat[i][i] >= 0 else -1
            mat[i][i] = sign * epsilon
            ws.info("warning: the {i+1} diagonal element is close to zero, add small offset")
        
        # 消元
        for j in range(i + 1, n):
            factor = mat[j][i] / mat[i][i]
            mat[j][i] = 0  # 显式设置为零以避免精度问题
            
            for k in range(i + 1, n + 1):
                mat[j][k] -= factor * mat[i][k]
    
    # 回代过程（后向代入）
    solution = [0] * n
    for i in range(n - 1, -1, -1):
        if abs(mat[i][i]) < epsilon:
            ws.info("warning: the {i+1} diagonal element is close to zero in backward substitution")
            # 处理特殊情况：检查右侧常数项是否也接近零
            if abs(mat[i][n]) < epsilon:
                # 如果左右两侧都接近零，可以假设该变量为零（特解）
                solution[i] = 0
                ws.info("set variable x{i+1}=0 as special solution")
                continue
            else:
                # 如果左侧接近零但右侧不为零，方程可能无解
                return None
        
        solution[i] = mat[i][n]
        for j in range(i + 1, n):
            solution[i] -= mat[i][j] * solution[j]
        solution[i] /= mat[i][i]
    
    return solution

def gaussian_elimination_pivot(matrix, epsilon=1e-10):
    """
    使用完全主元消去法求解线性方程组
    :param matrix: 增广矩阵（系数矩阵加上常数项）
    :param epsilon: 数值稳定性的最小阈值
    :return: 方程的解
    """
    n = len(matrix)
    mat = [row[:] for row in matrix]  # 复制矩阵
    
    # 跟踪列的置换
    perm = list(range(n))
    
    # 消元过程（前向消元）
    for i in range(n):
        # 寻找整个子矩阵中的最大元素作为主元
        max_val = 0
        max_row = i
        max_col = i
        
        for r in range(i, n):
            for c in range(i, n):
                if abs(mat[r][c]) > max_val:
                    max_val = abs(mat[r][c])
                    max_row = r
                    max_col = c
        
        # 检查是否找到了有效的主元
        if max_val < epsilon:
            ws.info("warning: matrix is close to singular, cannot find valid pivot")
            # 添加微小扰动到对角线
            for k in range(i, n):
                mat[k][k] += epsilon
            max_row = i
            max_col = i
        
        # 交换行
        if max_row != i:
            mat[i], mat[max_row] = mat[max_row], mat[i]
        
        # 交换列（需要记录置换）
        if max_col != i:
            perm[i], perm[max_col] = perm[max_col], perm[i]
            for k in range(n):
                mat[k][i], mat[k][max_col] = mat[k][max_col], mat[k][i]
        
        # 消元
        for j in range(i + 1, n):
            factor = mat[j][i] / mat[i][i]
            mat[j][i] = 0
            
            for k in range(i + 1, n + 1):
                mat[j][k] -= factor * mat[i][k]
    
    # 回代过程（后向代入）
    solution_perm = [0] * n
    for i in range(n - 1, -1, -1):
        solution_perm[i] = mat[i][n]
        for j in range(i + 1, n):
            solution_perm[i] -= mat[i][j] * solution_perm[j]
        solution_perm[i] /= mat[i][i]
    
    # 根据置换恢复原始变量顺序
    solution = [0] * n
    for i in range(n):
        solution[perm[i]] = solution_perm[i]
    
    return solution

def evaluate_poly(coeffs, x):
    """
    计算多项式在特定x值处的结果
    :param coeffs: 多项式系数列表，从高次项到常数项
    :param x: x值
    :return: 多项式值
    """
    result = 0
    for i, coeff in enumerate(coeffs):
        result += coeff * (x ** (len(coeffs) - 1 - i))
    return result

def find_min_poly(coeffs, x_min, x_max):
    """
    在指定区间内找到多项式的最小值点
    :param coeffs: 多项式系数列表，从高次项到常数项
    :param x_min: 区间最小值
    :param x_max: 区间最大值
    :return: (x_min, y_min) - 最小值点坐标
    """
    step_size = 1 #强制设置为1
    steps = int(x_max - x_min)
    min_x = x_min
    min_y = evaluate_poly(coeffs, x_min)
    
    # 网格搜索最小值
    for i in range(1, steps + 1):
        x = x_min + i * step_size
        y = evaluate_poly(coeffs, x)
        
        if y < min_y:
            min_y = y
            min_x = x
    
    # # 使用黄金分割法精细搜索
    # epsilon = 1e-6
    # a, b = min_x - step_size * 10, min_x + step_size * 10
    # a = max(a, x_min)
    # b = min(b, x_max)
    
    # # 黄金比例
    # phi = (1 + 5 ** 0.5) / 2
    # c = b - (b - a) / phi
    # d = a + (b - a) / phi
    
    # while abs(b - a) > epsilon:
    #     fc = evaluate_poly(coeffs, c)
    #     fd = evaluate_poly(coeffs, d)
        
    #     if fc < fd:
    #         b = d
    #     else:
    #         a = c
            
    #     c = b - (b - a) / phi
    #     d = a + (b - a) / phi
    
    # # 取中点
    # min_x = (a + b) / 2
    # min_y = evaluate_poly(coeffs, min_x)
    
    # 四舍五入x到最近的整数
    # min_x_rounded = round(min_x)
    # min_y_rounded = evaluate_poly(coeffs, min_x_rounded)
    
    return min_x, min_y



def fit_curve_and_find_minimum_spline(x_data, y_data):
    """
    对给定的x_data和y_data进行三次样条插值,并找出拟合曲线的最低点
    
    参数:
    x_data: x轴数据,例如vt_list
    y_data: y轴数据,例如fail_list
    
    返回:
    x_min: 拟合曲线最低点的x坐标
    y_min: 拟合曲线最低点的y坐标
    spline_params: 样条插值的参数
    """
    
    # 确保数据是有序的
    if len(x_data) != len(y_data) or len(x_data) < 2:
        raise ValueError("data is invalid, please check vt_list and fail_list")
    
    # 按x值排序数据
    data_points = sorted(zip(x_data, y_data), key=lambda point: point[0])
    x_data = [point[0] for point in data_points]
    y_data = [point[1] for point in data_points]
    
    n = len(x_data) - 1  # 区间数量
    
    # 计算h_i = x_{i+1} - x_i
    h = [x_data[i+1] - x_data[i] for i in range(n)]
    
    # 构建三对角矩阵方程组
    A = [[0.0 for _ in range(n+1)] for _ in range(n+1)]
    b = [0.0] * (n+1)
    
    # 设置边界条件: 自然边界条件 (第二导数为0)
    A[0][0] = 1.0
    A[n][n] = 1.0
    
    # 填充矩阵
    for i in range(1, n):
        A[i][i-1] = h[i-1]
        A[i][i] = 2 * (h[i-1] + h[i])
        A[i][i+1] = h[i]
        
        b[i] = 3 * ((y_data[i+1] - y_data[i]) / h[i] - (y_data[i] - y_data[i-1]) / h[i-1])
    
    # 解三对角矩阵方程组 (Thomas算法)
    c = solve_tridiagonal(A, b)
    
    # 计算其他样条系数
    a = y_data[:-1]
    b = [(y_data[i+1] - y_data[i]) / h[i] - h[i] * (2 * c[i] + c[i+1]) / 3 for i in range(n)]
    d = [(c[i+1] - c[i]) / (3 * h[i]) for i in range(n)]
    
    # 存储样条参数
    spline_params = []
    for i in range(n):
        spline_params.append((a[i], b[i], c[i], d[i], x_data[i], x_data[i+1]))

    x_min, _, x_dense, y_dense = find_min_cubic_spline(x_data, spline_params) #step size = 1 dac
    
    return x_min, x_dense, y_dense

def find_min_cubic_spline(x_data, spline_params):
    # 在样条曲线上搜索最小值
    x_dense = []
    y_dense = []

    min_x = x_data[0]
    max_x = x_data[-1]
    step_size = 1
    steps = int(max_x - min_x)
    
    x_min = min_x
    y_min = evaluate_spline(spline_params, min_x)

    x_dense.append(x_min)
    y_dense.append(y_min)
    
    for i in range(1, steps + 1):
        x = min_x + i * step_size
        y = evaluate_spline(spline_params, x)

        x_dense.append(int(x))
        y_dense.append(int(y))
        
        if y < y_min:
            y_min = y
            x_min = int(x)
    
    return x_min, y_min, x_dense, y_dense

def solve_tridiagonal(A, d):
    """
    使用Thomas算法求解三对角矩阵方程 Ax = d
    """
    n = len(d)
    c_prime = [0] * n
    d_prime = [0] * n
    x = [0] * n
    
    # 前向消元
    c_prime[0] = A[0][1] / A[0][0] if 0 < n-1 else 0
    d_prime[0] = d[0] / A[0][0]
    
    for i in range(1, n):
        denominator = A[i][i] - A[i][i-1] * c_prime[i-1]
        c_prime[i] = A[i][i+1] / denominator if i < n-1 else 0
        d_prime[i] = (d[i] - A[i][i-1] * d_prime[i-1]) / denominator
    
    # 回代求解
    x[n-1] = d_prime[n-1]
    for i in range(n-2, -1, -1):
        x[i] = d_prime[i] - c_prime[i] * x[i+1]
    
    return x

def evaluate_spline(spline_params, x):
    """
    在给定点x评估三次样条函数
    """
    for a, b, c, d, x_i, x_i_plus_1 in spline_params:
        if x_i <= x <= x_i_plus_1:
            dx = x - x_i
            return a + b * dx + c * dx**2 + d * dx**3
    
    # 如果x超出范围,使用最近的样条段
    if x < spline_params[0][4]:  # x < x_0
        a, b, c, d, x_i, _ = spline_params[0]
        dx = x - x_i
        return a + b * dx + c * dx**2 + d * dx**3
    else:  # x > x_n
        a, b, c, d, x_i, _ = spline_params[-1]
        dx = x - x_i
        return a + b * dx + c * dx**2 + d * dx**3