import nanocycler

#from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM
from lib.DataMgr import the_data_mgr as data_mgr

###########################################################################
### Reference Datasheet:
###########################################################################

class YMTC_CMD:
    FAST_READ_CMD_20h = 0x20

    BLOCK_ERASE_SUSPEND_67h = 0x67
    BLOCK_ERASE_RESUME_D7h = 0xD7

    PROGRAM_SUSPEND_CMD_87h = 0x87
    PROGRAM_RESUME_CMD_17h = 0x17

    SLC_ENTER_CMD_DAh = 0xDA
    SLC_EXIT_CMD_DFh = 0xDF

class CYMTC(COnfiDevice):
    def __init__(self):
        COnfiDevice.__init__(self)
        self.DEVICE_MANUFACTURER = "YMTC"
        self.DEVICE_NAME = "x"
        self.toshiba_like = False
        self.ROW_BYTE_ADDRESS = 4

    def read_unique_id(self, ch_list, ce, lun = 0):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xED)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 16) # SDR to have lower speed (valid only for HS because data out is performed with custom_sequence_ddr_get_data_byte)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 32)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            res, buffer = hw.custom_sequence_get_out_buffer(32)

            res = True

            for i in range(16):
                check = buffer[i] | buffer[16 + i]
                if check != 0xFF:
                    ws.error("Unique ID is not valid")
                    res = False
                    break

            if res:
                uid = self.format_array(buffer[0: 16])
                logger.log_device_unique_id([ch], [ce], lun, "UID", uid)
                self.load_info(ch, ce, lun, uid, self.DEVICE_NAME, self.device_id, self.manufacturer_code,
                                      self.model)
                break
            else:
                ws.error("Ch: {0} - Ce: {1} - Lun: {2} - UID: {3} is not valid".format(ch, ce, lun, buffer))



    def odt_configure(self, ch_list: [], ce_list: [], p1, p2, p3, p4):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xE2)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p1)   # it uses DQS pulse
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p2)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p3)
        opcbuilder.add(eNandBackdoor.BD_SINGLE_DDR_DATA_IN, p4)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_feature("ODT_CONFIGURE", ch_list, ce_list, 0, 0, [p1, p2, p3, p4])

    def identification(self):

        # clear uid list if already present, but before update central db
        for uid in self.uid_list:
            data_mgr.store_to_central(uid)
        self.uid_list.clear()

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        lun_list = range(0, self.LUN_NUMBER)

        # some initial setting for channels
        self.fpga_setup(0, 0, self.CHUNK_NUMBER)

        self.die_configure(ch_list, ce_list, self.odt, self.driver_strength)

        self.fpga_setup(self.read_latency_cycles, self.write_latency_cycles, self.CHUNK_NUMBER)

        # for ch in ch_list:
        #     self.die_configure([ch], ce_list, self.odt, self.driver_strength)

        for ch in ch_list:
            for ce in ce_list:
                hw.select_ce(ce)

                # Read Device Id
                self.device_id_read([ch], ce)

                # Read page parameters
                self.page_parameter_read([ch], ce, 0)

                for lun in range(self.LUN_NUMBER):
                    # Read Unique id for each lun
                    self.read_unique_id([ch], ce, lun)

        # before exit perform perform ZQ Calib Long
        for lun in lun_list:
            self.zq_calib_long(ch_list, ce_list, lun)


    def die_configure(self, ch_list: [], ce_list: [], odt = 4, driver_strength = 4):

        # for hard reset
        for ce in ce_list:
            for lun in range(self.LUN_NUMBER):
                self.get_status_enhanced_78h(ch_list, ce, lun, 0)
                self.device_reset(ch_list, [ce], ONFI_CMD.RESET_CMD_FDh)

        self.device_reset(ch_list, ce_list, ONFI_CMD.RESET_CMD_FFh)

        p1 = 0
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x10, p1, p2, p3, p4)

        p1 = 0xf
        p2 = 0x0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        p1 = (odt << 4) | (0x01 << 2) | (0x01 << 1) | (0x01 << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4, True)

    def page_convert(self, level, page):
        if level == 0:                                              # A status
            current_read_page = page                                # read LSB page
        elif level == 1:                                            # B status
            current_read_page = page + 1                            # read CSB page
        elif level == 2:                                            # C status
            current_read_page = page + 2                            # to read MSB page
        elif level == 3:                                            # D status
            current_read_page = page + 1                            # to read CSB page
        elif level == 4:                                            # E status
            current_read_page = page                                # to read LSB page
        elif level == 5:                                            # F status
            current_read_page = page + 1                            # to read CSB page
        elif level == 6:                                            # G status
            current_read_page = page + 2                            # to read MSB page
        return current_read_page
    
    # 0x80<=shift_value<=0xFF(即：Dec=[128, 255], shift voltage=[-1.28V, -0.01V]); step=10.0mV
    # 0x00<=shift_value<=0x7F(即：Dec=[0, 127], shift voltage=[0V, 1.27V])
    def vt_init(self, ch_list, ce, lun, level):
        start_dac, end_dac, voltage_offset = 0, 256, 0
        if level == 0:                                                # A status -> scan range [0x80, 0x50]
            start_scan = 128                                          # start_point: 0x80: dac=128
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80 -> shift to +800mV from 0 of the A status' default level

        elif level == 2:                                              # C status -> scan range [0xb3, 0x50]
            start_scan = int(hex(0xb3), 16)                           # start_point: 0xb3: dac=179 -> scan range: negative shift to -770mV [(0xff-0xb3)*10.0mV] from 0 of the C status' default level
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80  -> scan range: positive shift to +800mV from 0 of the C status' default level 
            voltage_offset = (770)+(800)                              # C start voltage point following A scan end point of 800mV (A voltage widow width from 0mV to 0x50), (962.5+800)mV is overlapped point btw A and C

        elif level == 4:                                              # E status -> scan range [0xa5, 0x50]
            start_scan = int(hex(0xa5), 16)                           # start_point: 0xa5: dac=165 -> scan range: negative shift to -910mV from 0 of the E status' default level
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80  -> scan range: positive shift to +800mV from 0 of the E status' default level 
            voltage_offset = (910)+(770+800)+(800)                    # E start voltage point following C scan end point: 800mV is A positive end point; (770+800)mV is C Vt width
            
        elif level == 6:                                              # G status -> scan range [0xa6, 0xff]
            start_scan = int(hex(0xa6), 16)                           # start_point: 0xa6: dac=166 -> scan range: negative shift to -900mV from this status' default 0
            end_scan = 256                                            # end_point
            voltage_offset = (900)+(910+800)+(770+800)+(800)          # G start voltage point following E scan end point: (800)mV is A end point, (770+800)mV is C Vt width, (910+800)mV is E Vt width
            
        if 128 <= start_scan <= 255: 
            start_dac = start_scan - 128 # 0X80 TO 0XFF
        elif 0 <= start_dac <= 127:
            start_dac = start_scan + 128 # 0X00 TO 0X7F
        
        if 128 <= end_scan <= 255: 
            end_dac = end_scan - 128 # 0X80 TO 0XFF
        elif 0 <= end_scan <= 127:
            end_dac = end_scan + 128 # 0X00 TO 0X7F
        
        return start_dac, end_dac, voltage_offset
    
    def shift_to_code(self, offset):
        code = offset & 0xFF
        return code
    
    def shift_to_voltage(self, offset):
        offset_voltage = -1280 + offset * 10

        return offset_voltage

    def set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 6:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0xA0, shift_code, 0x00, 0x00, 0x00) # page LSB
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA1, shift_code, 0x00, 0x00, 0x00) # page CSB
        if level == 2:
            self.set_feature(ch_list, [ce], 0xA2, shift_code, 0x00, 0x00, 0x00) # page MSB
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, shift_code, 0x00, 0x00) # page CSB
        if level == 4:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, shift_code, 0x00, 0x00) # page LSB
        if level == 5:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, shift_code, 0x00) # page CSB
        if level == 6:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, shift_code, 0x00, 0x00) # page MSB

        # SLC
        self.set_feature(ch_list, [ce], 0x14, shift_code, 0x00, 0x00, 0x00)

        return True, shift_voltage
    
    def reset_vt_code(self, ch_list, ce, lun):
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        return True
    
    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2() & ((page%3)//2)
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
            
        # pattern is the same between ce, use the first
        self.recall_pattern(ch_list, ce_list[0], lun_list[0], block, page)
        
        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # record I4w only on the last lun
            bI4w = self.pmu_algo_is_I4w()
            if lun_idx != len(lun_list) - 1:
                bI4w = False

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            # opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if (page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        #if bI2:
        #    pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if (page%3) == 2:
            logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page%3) == 2):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # record I2 and I4w only for upper page (2) of the last block
                bI2 = self.pmu_algo_is_I2()
                bI4w = self.pmu_algo_is_I4w()
                if (page % 3) != 2 or block_idx != (len(block_list) - 1):
                    bI2 = False
                    bI4w = False

                res, row_address = self.build_row_address(lun, block, page)

                # pattern is the same between ce use the first
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2) # 2 to use register seed

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if (page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1):
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                            
                if bI2:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                if bI2:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
            
            if (page%3) == 2:
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()
                logger.log_program("MP-PRG", ch_list, ce_list, lun_list, block_list, page, rbTimeName="tPROG", en_i=bI2 or bI4w)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page%3) == 2):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)




    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        # opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)

        self.set_ram_data_2(ch_list, column_address, row_address)

        bI1 = self.pmu_algo_is_I1()
        bI4 = self.pmu_algo_is_I4r()

        # in order to improve the performance, the sequenceis created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 7) # row address byte 3, is stored as byte 3 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            if bI1:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 4 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 4 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4)

    
    def multi_plane_read_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):
        return self.multi_plane_retry_page_compare(ch_list, ce, lun, block_list, page, column, page_length)
    
    def multi_plane_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):
        
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        for block_idx in range(len(block_list)):
            block = block_list[block_idx]
            res, row_address = self.build_row_address(lun, block, page)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                bI1 = False
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, en_fails = False)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column)
            # opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False)

        opcbuilder.cleanup()

    def read_offset_roic_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length, level, offset):
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)
        roic_address = self.build_roic_address(level, offset)

        self.recall_pattern(ch_list, ce, lun, block, page)

        self.set_ram_data_3(ch_list, column_address, row_address, roic_address)

        bI1 = self.pmu_algo_is_I1()
        bI4 = self.pmu_algo_is_I4r()

        # in order to improve the performance, the sequenceis created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 7) # row address byte 3, is stored as byte 3 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 8) # roic address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 9) # roic address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 10) # roic address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 11) # roic address byte 3, is stored as byte 3 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            if bI1:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 4 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 4 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4)

    def page_fast_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        self.recall_pattern(ch_list, ce, lun, block, page)

        bRes, row_address = self.build_row_address(lun, block, page)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)

        bI1 = self.pmu_algo_is_I1()
        bI4 = self.pmu_algo_is_I4r()

        if bI1 or bI4:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for chunk in range(0, self.CHUNK_NUMBER):
            column_address = column + chunk * self.CHUNK_LENGTH

            # create fast read sequence to repeat
            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)

            opcbuilder.set_column_address(column_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.FAST_READ_CMD_20h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.CHUNK_LENGTH)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 1000)

        if bI1 or bI4:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_read("FAST-READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4)


    def multi_plane_single_page_read_disturb(self, ch_list: [], ce, lun, list_block_list: [[]], page, column_address, page_length, cycles, loop):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        for block_list in list_block_list:
            
            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()

            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # bRes, row_address = COnfiDevice.build_row_address(self, lun, block, page)
                bRes, row_address = self.build_row_address(lun, block, page)

                # opcbuilder.clear()
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
                opcbuilder.set_column_address(column_address)
                # opcbuilder.set_row_address(row_address)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)

            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()

        startT = hw.get_nsec_time()
        for cycle in range(0, cycles):
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            if (cycle+1) == cycles:
                endT = hw.get_nsec_time()
                elapsed = (endT - startT) / 1000000000
                ws.info("Done: SPRD with {0} times - Block {1} - Page {2} - Elapsed Time {3:.3f} sec".format(cycles*(loop+1), list_block_list, page, elapsed))
                startT = hw.get_nsec_time()

        opcbuilder.cleanup()


    def multi_plane_block_read_disturb(self, ch_list: [], ce, lun, block_list: [], page, column_address, page_length):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            # opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()
        

    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column_address, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()

        #bI1 = self.pmu_algo_is_I1()
        bI4 = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        bI1 = False
        #if bI1:
        #    pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bI1 = self.pmu_algo_is_I1()
            
            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                bI1 = False
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            if bI1:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i= bI1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            # opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False, en_i=bI4)

        opcbuilder.cleanup()

    def multi_plane_page_read_pattern(self, ch_list: [], ce, lun, block_list: [], page, column_address, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()

        #bI1 = self.pmu_algo_is_I1()
        bI4 = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        bI1 = False
        #if bI1:
        #    pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bI1 = self.pmu_algo_is_I1()
            
            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                bI1 = False
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            if bI1:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i= bI1)

        read_pattern_dict = {}
        for ch in ch_list:
            read_pattern_dict[ch] = {}

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            # hang, just read pattern, no page compare,don't need recall pattern for read pattern
            # self.recall_pattern(ch_list, ce, lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            # opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4:
                pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            # hang, comment for preventing get fail data and log
            # logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails=True, en_rnb=False, en_i=bI4)

            # get read pattern for each channel
            for ch in ch_list:
                buffer = self.get_read_buffer(ch, page_length)
                read_pattern_dict[ch][block] = buffer

        opcbuilder.cleanup()

        return read_pattern_dict

    def is_bad_block(self, ch_list: [], ce, lun, block):

        is_bad = [False, False]

        # read first page of a block
        self.page_read(ch_list, ce, lun, block, 0, 0, self.PAGE_LENGTH)

        for ch in ch_list:
            hw.select_channel(ch)
            buffer = self.get_read_buffer(ch, self.PAGE_LENGTH)
            is_bad[ch] |= (buffer[16384] == 00)

        # read last page of a block
        self.page_read(ch_list, ce, lun, block, self.PAGE_NUMBER - 1, 0, self.PAGE_LENGTH)

        for ch in ch_list:
            hw.select_channel(ch)
            buffer = self.get_read_buffer(ch, self.PAGE_LENGTH)
            is_bad[ch] |= (buffer[16384] == 00)

        # log result per channel
        for ch in ch_list:
            logger.log_bad_block(ch, ce, lun, block, is_bad[ch])



    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            bRes, lRowAddress = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            # opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block])

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)



    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block in block_list:
                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111
                bRes, lRowAddress = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                # opcbuilder.set_row_address(lRowAddress)
                opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 24) & 0xFF)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def get_status_enhanced_78h(self, ch_list, ce, lun, block):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        res, row_address = self.build_row_address(lun, block, 0)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SRE78h", ch_list, ce, lun, block)

    # def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):
    #
    #     ch_mask = self.index_list_to_mask(ch_list)
    #     ce_mask = self.index_list_to_mask(ce_list)
    #
    #     bI3 = self.pmu_algo_is_I3()
    #
    #     for lun in lun_list:
    #         opcbuilder = nanocycler.opcodebuilder(0)
    #         if bI3:
    #             pmu.PMU_START_TRIGGER(opcbuilder)
    #         opcbuilder.set_ce_low()
    #         opcbuilder.set_wp_high()
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #
    #         for block in block_list:
    #             self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
    #             bRes, lRowAddress = self.build_row_address(lun, block, 0)
    #
    #             opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
    #             # opcbuilder.set_row_address(lRowAddress)
    #             opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 0) & 0xFF)
    #             opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 8) & 0xFF)
    #             opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 16) & 0xFF)
    #             opcbuilder.add(eNandBackdoor.BD_ALE, (lRowAddress >> 24) & 0xFF)
    #
    #         # in case of multi-lun (interleaving operation), we wait only on last lun
    #         opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)
    #
    #         opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
    #         opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #         opcbuilder.set_ce_high()
    #         opcbuilder.set_wp_low()
    #         if bI3:
    #             pmu.PMU_STOP_TRIGGER(opcbuilder)
    #         hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
    #         opcbuilder.cleanup()
    #
    #         logger.log_erase("MP-ERS", ch_list, ce_list, [lun], block_list)
    #
    #         if logger.is_in_log_set(LOG_SET_ITEM.SR):
    #             for ce in ce_list:
    #                 for block in block_list:
    #                     self.get_status_enhanced_78h(ch_list, ce, lun, block)

    # #################################################
    # Temperature Sensor Readout
    def read_internal_temperature(self, ch_list: [], ce):

        self.page_read(ch_list, ce, 0, 0, 0, 0, self.PAGE_LENGTH)

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        lun = 0
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xD4)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0xE7)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            data = hw.custom_sequence_sdr_get_data_byte(0)

            if data > 167:
                temp = 125
            else:
                temp = data - 42

            logger.log_die_temperature("READ_INT_TEMP", [ch], [ce], "{:.1f}".format(temp))
            self.temperature_4_channel[ch] = temp


    # #################################################
    # READ RETRY

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:

            # ws.info(">>> Option {0}/{1}".format(retry_option, len(self.option_buffer)))

            if retry_option >= len(self.option_buffer):
                return False, ""

            # csv file, each line should contain:
            if len(self.option_buffer[retry_option]) >= 7: # Eric
                # 7 parameters for TLC
                self.set_feature(ch_list, [ce], 0xA0, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][4], 16), 0x00, 0x00)
                self.set_feature(ch_list, [ce], 0xA1, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), 0x00)
                self.set_feature(ch_list, [ce], 0xA2, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), 0x00, 0x00)
            
            elif len(self.option_buffer[retry_option]) >= 8:
                # 1 parameter for SLC
                self.set_feature(ch_list, [ce], 0xA3, int(self.option_buffer[retry_option][7], 16), 0x00, 0x00, 0x00)
            else:
                return  False, ""

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""
            

    def reset_read_retry_option(self, ch_list: [], ce, lun):
        # TLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)

        # SLC
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)

        return True # Eric add


    # #################################################
    # READ OFFSET

    def offset_to_voltage(self, offset):
        offset_voltage = -1600 + offset * 12.5

        return offset_voltage


    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):

        if level > 6:
            return False, 0.0

        offset_code = self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0xA0, offset_code, 0x00, 0x00, 0x00)  # lower
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA1, offset_code, 0x00, 0x00, 0x00)  # middle
        if level == 2:
            self.set_feature(ch_list, [ce], 0xA2, offset_code, 0x00, 0x00, 0x00)  # upper
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, offset_code, 0x00, 0x00)  # middle
        if level == 4:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, offset_code, 0x00, 0x00)  # lower
        if level == 5:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, offset_code, 0x00)  # middle
        if level == 6:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, offset_code, 0x00, 0x00)  # upper

        # SLC
        self.set_feature(ch_list, [ce], 0xA3, offset_code, 0x00, 0x00, 0x00)

        return True, offset_voltage


    def set_read_offset_code_multi_level(self, ch, ce, lun, page, offset4level):

        dacs = [0,0,0]
        for level in self.get_valid_level_for_page(page):
            dacs[self.get_valid_level_for_page(page).index(level)] = self.offset_to_code(offset4level[level])
        if page == 0:
            #ws.warning("A0 {0} {1} {2} {3}".format(dacs[0], dacs[1], 0, 0))
            self.set_feature([ch], [ce], 0xA0, dacs[0], dacs[1], 0x00, 0x00)  # lower
        elif page == 1:
            #ws.warning("A1 {0} {1} {2} {3}".format(dacs[0], dacs[1], dacs[2], 0))
            self.set_feature([ch], [ce], 0xA1, dacs[0], dacs[1], dacs[2], 0x00)  # middle
        elif page == 2:
            #ws.warning("A2 {0} {1} {2} {3}".format(dacs[0], dacs[1], dacs[2], 0))
            self.set_feature([ch], [ce], 0xA2, dacs[0], dacs[1], 0x00, 0x00)  # upper
        else:
            raise Exception("Wrong Page!")

        return True

    def set_read_offset_page(self, ch_list: [], ce, lun, page, dacs):

        if page == 0:
            offset_code1 = self.offset_to_code(dacs[0])
            offset_code2 = self.offset_to_code(dacs[1])
            self.set_feature(ch_list, [ce], 0xA0, offset_code1, offset_code2, 0x00, 0x00)  # lower
        elif page == 1:
            offset_code1 = self.offset_to_code(dacs[0])
            offset_code2 = self.offset_to_code(dacs[1])
            offset_code3 = self.offset_to_code(dacs[2])
            self.set_feature(ch_list, [ce], 0xA1, offset_code1, offset_code2, offset_code3, 0x00)  # middle
        elif page == 2:
            offset_code1 = self.offset_to_code(dacs[0])
            offset_code2 = self.offset_to_code(dacs[1])
            self.set_feature(ch_list, [ce], 0xA2, offset_code1, offset_code2, 0x00, 0x00)  # upper
        else:
            raise Exception("Wrong Page!")

        return True

    def reset_read_offset_code(self, ch_list: [], ce, lun, level):
        # TLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)

        # SLC
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)


    # ##############################################################
    # Erase suspend

    def erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        #delay than suspend all luns
        opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

        for lun in lun_list:
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_SUSPEND_67h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS-SPD", ch_list, ce_list, lun_list, [block], rbTimeName="tESPD")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        # apply resume for each lun
        for lun in lun_list:

            # enhanced SR read just to select the lun
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_RESUME_D7h)

        if not suspend_cmd_delay == -1:
            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:

                # enhanced SR read just to select the lun
                res, row_address = self.build_row_address(lun, block, 0)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_SUSPEND_67h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS-RSM", ch_list, ce_list, lun_list, [block], rbTimeName="tBERS" if suspend_cmd_delay == -1 else "tESPD")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        row_address = 0

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                # opcbuilder.set_row_address(row_address)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_SUSPEND_67h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        # get suspend time
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS-SPD", ch_list, ce_list, lun_list, block_list, rbTimeName="tESPD")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        # apply resume for each lun
        for lun in lun_list:
            block = block_list[0]

            # enhanced SR read just to select the lun
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_RESUME_D7h)

        if not suspend_cmd_delay == -1:
            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                block = block_list[0]

                # enhanced SR read just to select the lun
                res, row_address = self.build_row_address(lun, block, 0)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.BLOCK_ERASE_SUSPEND_67h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS-RSM", ch_list, ce_list, lun_list, block_list, rbTimeName="tBERS" if suspend_cmd_delay == -1 else "tESPD")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)

    # ##############################################################
    # Program suspend

    def program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay):

        page_suspended = False
        column = 0

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        time_measure = "tPROG"

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if (page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1):
                time_measure = "tPSPD"
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                # delay than suspend all luns
                opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)
                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_SUSPEND_CMD_87h)
                opcbuilder.set_column_address(column)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                page_suspended = True
            else:
                time_measure = "tBSY"
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)

            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG-SPD", ch_list, ce_list, lun_list, [block], page, rbTimeName=time_measure)

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)

        return page_suspended


    def program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        column_address = 0

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            # resume is for lun, use block to calc the address
            res, row_address = self.build_row_address(lun, block, 0)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_RESUME_CMD_17h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)


        if not suspend_cmd_delay == -1:

            # delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                # suspend is for lun use block to calc the address
                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_SUSPEND_CMD_87h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG-RSM", ch_list, ce_list, lun_list, [block], page, rbTimeName = "tPROG" if suspend_cmd_delay == -1 else "tPSPD")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay):

        page_suspended = False

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)
                column_address = 0

                # pattern is the same between ce use the first
                self.recall_pattern(ch_list, ce_list[0], lun, block, page)

                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth


                if block_idx < (len(block_list) - 1):
                    time_measure = "tBSY"
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if (page % self.LEVEL_NUMBER) == (self.LEVEL_NUMBER - 1):
                        time_measure = "tPSPD"
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                        # delay than suspend all luns
                        opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)
                        opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_SUSPEND_CMD_87h)
                        opcbuilder.set_column_address(column_address)
                        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                        page_suspended = True
                    else:
                        time_measure = "tBSY"
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)

                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()

                logger.log_program("MP-PRG-SPD", ch_list, ce_list, [lun], [block], page, rbTimeName=time_measure)

        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)

        return page_suspended


    def multi_plane_program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, suspend_cmd_delay=-1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        column_address = 0

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            # resume is for lun, use block 0 to calc the address
            res, row_address = self.build_row_address(lun, 0, 0)

            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_RESUME_CMD_17h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        if not suspend_cmd_delay == -1:

            # delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                # suspend is for lun use block 0 to calc the address
                res, row_address = self.build_row_address(lun, 0, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.PROGRAM_SUSPEND_CMD_87h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("MP-PRG-RSM", ch_list, ce_list, lun_list, block_list, page,
                           rbTimeName="tPROG" if suspend_cmd_delay == -1 else "tPGMSL")

        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)
