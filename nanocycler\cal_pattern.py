"""
Pattern calculation module, providing bit counting and pattern pre-calculation functions.
This module is a Python wrapper for the cal_pattern.so C standard library.
"""
import ffi
from nanocycler import ws as ws
from .libmanager import CLibManagerSingleton
from .utility import address_of

# Load cal_pattern.so library
_lib_handle = CLibManagerSingleton.load_libcalpattern()

# Import C functions
_xor_cal_page_fbc_pattern_and_fbc = None
_get_bit_count = None
_and_cal_state_cell_pattern = None
_and_cal_state_fbc_pattern = None
_and_cal_state_fbc = None

if _lib_handle is not None:
    try:
        # Updated signature: 'i' for int return (error code), 'PPP' for three pointer arguments
        _xor_cal_page_fbc_pattern_and_fbc = CLibManagerSingleton.load_func(
            _lib_handle, "v", "xor_cal_page_fbc_pattern_and_fbc", "pppLpp"
        )
        _get_bit_count = CLibManagerSingleton.load_func(
            _lib_handle, "l", "get_bit_count", "pL"
        )
        _and_cal_state_cell_pattern = CLibManagerSingleton.load_func(
            _lib_handle, "v", "and_cal_state_cell_pattern", "pppppppL"
        )
        _and_cal_state_fbc_pattern = CLibManagerSingleton.load_func(
            _lib_handle, "v", "and_cal_state_fbc_pattern", "pppL"
        )
        _and_cal_state_fbc = CLibManagerSingleton.load_func(
            _lib_handle, "l", "and_cal_state_fbc", "ppL"
        )
    except Exception as e:
        ws.info("Warning: Error loading functions from cal_pattern.so: {}".format(str(e)))


def xor_cal_page_fbc_pattern_and_fbc(pattern1: bytearray, pattern2: bytearray, cal_result: bytearray, pattern_len:int, page_FBC_16KB: bytearray, page_FBC_4KB_list: bytearray):
    """
    Calculate the logical XOR result of two patterns and count the related bit counts.
    This is typically used for calculating page FBC (Fail Bit Count).

    Parameters:
        pattern1 (bytes/bytearray): The first pattern.
        pattern2 (bytes/bytearray): The second pattern.
        Both patterns must have the same length and the length should be a multiple of 4.

    Returns:
        dict: A dictionary containing FBC results or None on error.
    """

    if _xor_cal_page_fbc_pattern_and_fbc is None:
        ws.info("Error: C function _xor_cal_page_fbc_pattern_and_fbc is not loaded")
        return
    
    # Type checks
    if not isinstance(pattern1, (bytes, bytearray)) or not isinstance(pattern2, (bytes, bytearray)) or not isinstance(cal_result, (bytes, bytearray)):
        ws.info("Error: pattern1 and pattern2 must be bytes or bytearray types")
        return
    
    # Length checks
    if len(pattern1) != pattern_len or len(pattern2) != pattern_len or len(cal_result) != pattern_len:
        ws.info("Error: pattern1 and pattern2 and cal_result must have the same length (DEVICE.PAGE_LENGTH)")
        return

    if pattern_len % 4 != 0:
        ws.info("Error: Pattern length must be a multiple of 4")
        return

    try:
        addr_pattern1 = address_of(pattern1)
        addr_pattern2 = address_of(pattern2)
        cal_result_addr = address_of(cal_result)
        page_FBC_16KB_addr = address_of(page_FBC_16KB)
        page_FBC_4KB_list_addr = address_of(page_FBC_4KB_list)

        _xor_cal_page_fbc_pattern_and_fbc(addr_pattern1, addr_pattern2, cal_result_addr, pattern_len, page_FBC_16KB_addr, page_FBC_4KB_list_addr)

    except Exception as e:
        ws.info("Error: Exception occurred while calling C function _xor_cal_page_fbc_pattern_and_fbc: {}".format(str(e)))
        # Fall through to error handling based on error_code


def get_bit_count(buf: bytearray, length: int) -> int:
    if _get_bit_count is None:
        ws.info("Error: C function get_bit_count is not loaded")
        return
    if not isinstance(buf, (bytes, bytearray)) or len(buf) != length:
        ws.info("Error: buf must be bytes/bytearray and length must match")
        return
    addr_buf = address_of(buf)
    return _get_bit_count(addr_buf, length)

def and_cal_state_cell_pattern(pattern1, operation1, pattern2, operation2, pattern3, operation3, result, length):
    if _and_cal_state_cell_pattern is None:
        ws.info("Error: C function and_cal_state_cell_pattern is not loaded")
        return
    addr_p1 = address_of(pattern1) if pattern1 is not None else 0
    addr_op1 = address_of(operation1.encode() if operation1 else b"")
    addr_p2 = address_of(pattern2) if pattern2 is not None else 0
    addr_op2 = address_of(operation2.encode() if operation2 else b"")
    addr_p3 = address_of(pattern3) if pattern3 is not None else 0
    addr_op3 = address_of(operation3.encode() if operation3 else b"")
    addr_result = address_of(result)
    _and_cal_state_cell_pattern(addr_p1, addr_op1, addr_p2, addr_op2, addr_p3, addr_op3, addr_result, length)

def and_cal_state_fbc_pattern(pattern1, pattern2, result, length):
    if _and_cal_state_fbc_pattern is None:
        ws.info("Error: C function and_cal_state_fbc_pattern is not loaded")
        return
    addr_p1 = address_of(pattern1)
    addr_p2 = address_of(pattern2)
    addr_result = address_of(result)
    _and_cal_state_fbc_pattern(addr_p1, addr_p2, addr_result, length)

def and_cal_state_fbc(pattern1, pattern2, length) -> int:
    if _and_cal_state_fbc is None:
        ws.info("Error: C function and_cal_state_fbc is not loaded")
        return 0
    addr_p1 = address_of(pattern1)
    addr_p2 = address_of(pattern2)
    return _and_cal_state_fbc(addr_p1, addr_p2, length)

ws.info("cal_pattern.py module loaded and modified to use FFI")