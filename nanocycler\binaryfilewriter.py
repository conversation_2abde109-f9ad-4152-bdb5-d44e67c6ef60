## @package nanocycler.binaryfilewriter
#  <summary> Contains the writer of a binary file. </summary>
#
# @ingroup wsApiGroupPyLanguage

from .libmanager import *
from .utility import *


## <summary>	The writer of binary file.</summary>
#
# @ingroup wsApiGroup
# @snippet std_examples.py BinaryFileWriter Example
class binaryfilewriter:

    # ! @cond Doxygen_Suppress
    def __init__(self, enable_nfs):

        self._lib_handle = CLibManagerSingleton.load_libwslibpy()

        if enable_nfs:
            self._binaryfilewritercreate = CLibManagerSingleton.load_func(self._lib_handle, "p", "NFSBinaryFileWriterApi_Create", "")
            self._binaryfilewriterdelete = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Delete", "p")
            self._binaryfilewriteropen = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Open", "ps")
            self._binaryfilewriterclose = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Close", "p")
            self._binaryfilewriterflush = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Flush", "p")
            self._binaryfilewriterisopen = CLibManagerSingleton.load_func(self._lib_handle, "b", "NFSBinaryFileWriterApi_IsOpen", "p")
            self._binaryfilewriterwrite = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Write", "ppL")
        else:
            self._binaryfilewritercreate = CLibManagerSingleton.load_func(self._lib_handle, "p", "BinaryFileWriterApi_Create", "")
            self._binaryfilewriterdelete = CLibManagerSingleton.load_func(self._lib_handle, "v", "BinaryFileWriterApi_Delete", "p")
            self._binaryfilewriteropen = CLibManagerSingleton.load_func(self._lib_handle, "v", "BinaryFileWriterApi_Open", "ps")
            self._binaryfilewriterclose = CLibManagerSingleton.load_func(self._lib_handle, "v", "BinaryFileWriterApi_Close", "p")
            self._binaryfilewriterflush = CLibManagerSingleton.load_func(self._lib_handle, "v", "BinaryFileWriterApi_Flush", "p")
            self._binaryfilewriterisopen = CLibManagerSingleton.load_func(self._lib_handle, "b", "BinaryFileWriterApi_IsOpen", "p")
            self._binaryfilewriterwrite = CLibManagerSingleton.load_func(self._lib_handle, "v", "BinaryFileWriterApi_Write", "ppL")

        self.fwptr = None
	# ! @endcond

    ## <summary>	Only for compatibility.</summary>
    def cleanup(self):
        return

    ## <summary>	Opens the given file name with exetension ( e.g Test.bin).
    # 					The file is created to temporary folder and copied at the end of test to Result folder
    # 					</summary>
    #
    # <param name="fileNameWithExt">	file name with extension.</param>
    def open(self, fileNameWithExt: str):
		# ! @cond Doxygen_Suppress
        self.fwptr = self._binaryfilewritercreate()
        if self.fwptr is not None:
            self._binaryfilewriteropen(self.fwptr, fileNameWithExt)
		# ! @endcond

    ## <summary>	Closes the file.</summary>
    def close(self):
		# ! @cond Doxygen_Suppress
        if self.fwptr is not None:
            self._binaryfilewriterclose(self.fwptr)
            self._binaryfilewriterdelete(self.fwptr)
            self.fwptr = None
		# ! @endcond

    ## <summary>	Flush the content on disk.</summary>
    def flush(self):
        if self.fwptr is not None:
            self._binaryfilewriterflush(self.fwptr)

    ## <summary>	Query if this file is open.</summary>
    #
    # <returns>	true if open, false if not.</returns>
    def is_open(self):
        if self.fwptr is not None:
            return bool(self._binaryfilewriterisopen(self.fwptr))

        return False

    ## <summary>	Writes a byte buffer of provided legth to disk.</summary>
    #
    # <param name="data">	The byte buffer.</param>
    # <param name="length">	length of byte buffer.</param>
    def write(self, data: bytearray, length: len):
        if self.fwptr is not None:
            dataaddr = address_of(data)
            self._binaryfilewriterwrite(self.fwptr, dataaddr, length)


## <summary>	The writer of binary file on NFS share.</summary>
#
# @ingroup wsApiGroup
# @snippet std_examples.py NFSBinaryFileWriter Example
class nfsbinaryfilewriter:
    # ! @cond Doxygen_Suppress
    def __init__(self):
        self._lib_handle = CLibManagerSingleton.load_libwslibpy()
        self._nfsbinaryfilewritercreate = CLibManagerSingleton.load_func(self._lib_handle, "p", "NFSBinaryFileWriterApi_Create", "")
        self._nfsbinaryfilewriterdelete = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Delete", "p")
        self._nfsbinaryfilewriteropen = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Open", "ps")
        self._nfsbinaryfilewriterclose = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Close", "p")
        self._nfsbinaryfilewriterflush = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Flush", "p")
        self._nfsbinaryfilewriterisopen = CLibManagerSingleton.load_func(self._lib_handle, "b", "NFSBinaryFileWriterApi_IsOpen", "p")
        self._nfsbinaryfilewriterwrite = CLibManagerSingleton.load_func(self._lib_handle, "v", "NFSBinaryFileWriterApi_Write", "ppL")

        self.fwptr = None

    # ! @endcond

    ## <summary>	OBSOLETE !!!! Cleanup function to unload the library from memory.</summary>
    def cleanup(self):
        return

    ## <summary>	Opens the given file name with exetension ( e.g Test.bin).
    # 					The file is created to NFS shared folder of tester unit.
    # 					</summary>
    #
    # <param name="fileNameWithExt">	file name with extension.</param>
    def open(self, fileNameWithExt: str):
		# ! @cond Doxygen_Suppress
        self.fwptr = self._nfsbinaryfilewritercreate()
        if self.fwptr is not None:
            self._nfsbinaryfilewriteropen(self.fwptr, fileNameWithExt)
		# ! @endcond

    ## <summary>	Closes the file.</summary>
    def close(self):
		# ! @cond Doxygen_Suppress
        if self.fwptr is not None:
            self._nfsbinaryfilewriterclose(self.fwptr)
            self._nfsbinaryfilewriterdelete(self.fwptr)
        self.fwptr = None
		# ! @endcond

    ## <summary>	Flush the content on disk.</summary>
    def flush(self):
        if self.fwptr is not None:
            self._nfsbinaryfilewriterflush(self.fwptr)

    ## <summary>	Query if this file is open.</summary>
    #
    # <returns>	true if open, false if not.</returns>
    def is_open(self):
        if self.fwptr is not None:
            return bool(self._nfsbinaryfilewriterisopen(self.fwptr))
        return False

    ## <summary>	Writes a byte buffer of provided legth to disk.</summary>
    #
    # <param name="data">	The byte buffer.</param>
    # <param name="length">	length of byte buffer.</param>
    def write(self, data: bytearray, length: int):
        if self.fwptr is not None:
            dataaddr = address_of(data)
            self._nfsbinaryfilewriterwrite(self.fwptr, dataaddr, length)
