import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from lib.ResultLogger import the_result_logger as logger
from Devices.YMTC import CYMTC as CYMTC
from Devices.YMTC import YMTC_CMD as YMTC_CMD


###########################################################################
### Reference Datasheet:
###########################################################################

class YMTC_X36070_CMD:
    COARSE_PROGRAMMING_DCh = 0xDC


class CYMTCX36070(CYMTC):
    def __init__(self):
        CYMTC.__init__(self)
        self.DEVICE_MANUFACTURER = "YMTC"
        self.DEVICE_NAME = "X36070"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2 # Eric: per CH
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18816
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 4  # 4 plane inside the lun
        self.STRING_NUMBER = 6 # self.PLANE_NUMBER
        self.LEVEL_NUMBER = 4  # QLC 4 levels
        self.WL_NUMBER = 1416   #1386->1416
        self.LUN_NUMBER = 4 # Eric: per CE?
        self.BLOCK_NUMBER = 403 * self.PLANE_NUMBER # 657 * self.PLANE_NUMBER # it is just a number it depends on device size
        self.PAGE_NUMBER = 5544  # per block

        self.LUN_START_BIT_ADDRESS = 25
        self.BLOCK_START_BIT_ADDRESS = 13
        self.VALID_LUN_MASK = 0x07
        self.VALID_BLOCK_MASK = 0x07FF      #hang, 1FF->7FF, 511->2047, 1FF导致block超出511时高位的两个bit无效，得到的row_address会出错, 比如block544得到的是block32
        self.VALID_PAGE_MASK = 0x1FFF

        #hang, change for different wl
        self.SLC_VT_LEVEL = 1 # SLC device
        self.SLC_VT_SCAN = [0]
        self.TLC_VT_LEVEL = 7 # TLC device
        self.TLC_VT_SCAN = [0, 2, 4, 6]

        self.VT_LEVEL = 15 # QLC device
        self.VT_SCAN = [1, 5, 9, 13]

        self.QLC_VT_LEVEL_VALID = [[1,7,13],[2,6,8,12],[4,9,11,14],[0,3,5,10]] #hang, add for read_offset_best
        self.SLC_VT_LEVEL_VALID = [[0]] #hang, add for read_offset_best
        self.TLC_VT_LEVEL_VALID = [[0,4],[1,3,5],[2,6]] #hang, add for read_offset_best

        self.MAX_ERASE_TIME = 20000  # 20 msec
        self.MAX_PROG_TIME = 10000  # 10 msec
        self.MAX_READ_TIME = 400  # 200 usec

        self.VCC = 2.5
        self.VCCQ = 1.2


    def get_valid_level_for_page(self, page):   #hang, add for read_offset_best
        _,level,levels,_ = self.page_info(page)
        if levels == 1:
            return self.SLC_VT_LEVEL_VALID[level]
        elif levels == 3:
            return self.TLC_VT_LEVEL_VALID[level]
        elif levels == 4:
            return self.QLC_VT_LEVEL_VALID[level]
        else:
            raise Exception("Wrong Page!")

    def die_configure(self, ch_list: [], ce_list: [], odt, driver_strength):

        # for hard reset
        for ce in ce_list:
            for lun in range(self.LUN_NUMBER):
                self.get_status_enhanced_78h(ch_list, ce, lun, 0)
                self.device_reset(ch_list, [ce], ONFI_CMD.RESET_CMD_FDh)

        self.device_reset(ch_list, ce_list, ONFI_CMD.RESET_CMD_FFh)

        p1 = 0
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        self.set_feature(ch_list, ce_list, 0x10, p1, p2, p3, p4)

        p1 = 0xf
        p2 = 0x0
        self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)

        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        p1 = (odt << 4) | (0x01 << 2) | (0x01 << 1) | (0x01 << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4)

    def page_convert(self, level, page):
        if level == 1 or level == 7 or level == 13:                   # RL2/8/14 status
            current_read_page = page                                # read LP
        elif level == 2 or level == 6 or level == 8 or level == 12:    # RL3/7/9/13 status
            current_read_page = page + 1                            # read MP
        elif level == 4 or level == 9 or level == 11 or level == 14:   # RL5/10/12/15 status
            current_read_page = page + 2                            # to read UP
        elif level == 0 or level == 3 or level == 5 or level == 10:    # RL1/3/6/11 status
            current_read_page = page + 3                            # to read XP
        return current_read_page
    
    def slc_page_convert(self, level, page):
        current_read_page = page                                # read SLC
        return current_read_page

    def tlc_page_convert(self, level, page):
        if level == 0:                                              # A status
            current_read_page = page                                # read LSB page
        elif level == 1:                                            # B status
            current_read_page = page + 1                            # read CSB page
        elif level == 2:                                            # C status
            current_read_page = page + 2                            # to read MSB page
        elif level == 3:                                            # D status
            current_read_page = page + 1                            # to read CSB page
        elif level == 4:                                            # E status
            current_read_page = page                                # to read LSB page
        elif level == 5:                                            # F status
            current_read_page = page + 1                            # to read CSB page
        elif level == 6:                                            # G status
            current_read_page = page + 2                            # to read MSB page
        return current_read_page

    
    # 0x80<=shift_value<=0xFF(即：Dec=[128, 255], shift voltage=[-1.28V, -0.01V]); step=10.0mV
    # 0x00<=shift_value<=0x7F(即：Dec=[0, 127], shift voltage=[0V, 1.27V])
    def vt_init(self, ch_list, ce, lun, level):
        start_dac, end_dac, voltage_offset = 0, 256, 0
        if level == 1:                                                # RL1 status
            start_scan = 128                                          # start_point: 0x80: dac=128
            end_scan = int(hex(0x50), 16) + 1                         # end_point: +800mV

        elif level == 5:                                              # RL5 status
            start_scan = int(hex(0xb0), 16)                           # start_point: -800mV 
            end_scan = int(hex(0x50), 16) + 1                         # end_point:  +800mV  
            voltage_offset = (800)+(800)                              # RL5 start voltage point RL1+800+800

        elif level == 9:                                              # RL9 status
            start_scan = int(hex(0xb4), 16)                           # start_point:-760mV
            end_scan = int(hex(0x50), 16) + 1                         # end_point: +800mV 
            voltage_offset = (760)+(800+800)+(800)                    # RL9 start voltage point RL1+800+800+800+760
            
        elif level == 13:                                             # RL13 status -> scan range [0xa4, 0xff]
            start_scan = int(hex(0xA4), 16)                           # start_point: -920mV
            end_scan = 256                                            # end_point
            voltage_offset = (920)+(760+800)+(800+800)+(800)          # RL13 start voltage point RL1+800+800+800+760+800+920
            
        if 128 <= start_scan <= 255: 
            start_dac = start_scan - 128 # 0X80 TO 0XFF
        elif 0 <= start_dac <= 127:
            start_dac = start_scan + 128 # 0X00 TO 0X7F
        
        if 128 <= end_scan <= 255: 
            end_dac = end_scan - 128 # 0X80 TO 0XFF
        elif 0 <= end_scan <= 127:
            end_dac = end_scan + 128 # 0X00 TO 0X7F
        
        return start_dac, end_dac, voltage_offset

    def slc_vt_init(self, ch_list, ce, lun, level):
        start_dac, end_dac, voltage_offset = 0, 256, 0
                                                       
        start_scan = 128                                         
        end_scan = 256                        
            
        if 128 <= start_scan <= 255: 
            start_dac = start_scan - 128 # 0X80 TO 0XFF
        elif 0 <= start_dac <= 127:
            start_dac = start_scan + 128 # 0X00 TO 0X7F
        
        if 128 <= end_scan <= 255: 
            end_dac = end_scan - 128 # 0X80 TO 0XFF
        elif 0 <= end_scan <= 127:
            end_dac = end_scan + 128 # 0X00 TO 0X7F
        
        return start_dac, end_dac, voltage_offset

    def tlc_vt_init(self, ch_list, ce, lun, level):
        start_dac, end_dac, voltage_offset = 0, 256, 0
        if level == 0:                                                # A status -> scan range [0x80, 0x50]
            start_scan = 128                                          # start_point: 0x80: dac=128
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80 -> shift to +800mV from 0 of the A status' default level

        elif level == 2:                                              # C status -> scan range [0xb3, 0x50]
            start_scan = int(hex(0xb3), 16)                           # start_point: 0xb3: dac=179 -> scan range: negative shift to -770mV [(0xff-0xb3)*10.0mV] from 0 of the C status' default level
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80  -> scan range: positive shift to +800mV from 0 of the C status' default level 
            voltage_offset = (770)+(800)                              # C start voltage point following A scan end point of 800mV (A voltage widow width from 0mV to 0x50), (962.5+800)mV is overlapped point btw A and C

        elif level == 4:                                              # E status -> scan range [0xa5, 0x50]
            start_scan = int(hex(0xa5), 16)                           # start_point: 0xa5: dac=165 -> scan range: negative shift to -910mV from 0 of the E status' default level
            end_scan = int(hex(0x50), 16) + 1                         # end_point: 0x50: dac=80  -> scan range: positive shift to +800mV from 0 of the E status' default level 
            voltage_offset = (910)+(770+800)+(800)                    # E start voltage point following C scan end point: 800mV is A positive end point; (770+800)mV is C Vt width
            
        elif level == 6:                                              # G status -> scan range [0xa6, 0xff]
            start_scan = int(hex(0xa6), 16)                           # start_point: 0xa6: dac=166 -> scan range: negative shift to -900mV from this status' default 0
            end_scan = 256                                            # end_point
            voltage_offset = (900)+(910+800)+(770+800)+(800)          # G start voltage point following E scan end point: (800)mV is A end point, (770+800)mV is C Vt width, (910+800)mV is E Vt width
            
        if 128 <= start_scan <= 255: 
            start_dac = start_scan - 128 # 0X80 TO 0XFF
        elif 0 <= start_dac <= 127:
            start_dac = start_scan + 128 # 0X00 TO 0X7F
        
        if 128 <= end_scan <= 255: 
            end_dac = end_scan - 128 # 0X80 TO 0XFF
        elif 0 <= end_scan <= 127:
            end_dac = end_scan + 128 # 0X00 TO 0X7F
        
        return start_dac, end_dac, voltage_offset
    
    def set_read_offset_code_multi_level(self, ch, ce, lun, page, offset4level):    #hang, add for read_offset_best

        dacs = [0,0,0,0]
        for level in self.get_valid_level_for_page(page):
            dacs[self.get_valid_level_for_page(page).index(level)] = self.offset_to_code(offset4level[level])
        levels = self.page_info(page)[2]    #hang, add for X36070
        if levels == 4:
            if page == 0:
                #ws.warning("A0 {0} {1} {2} {3}".format(dacs[0], dacs[1], 0, 0))
                self.set_feature([ch], [ce], 0xA0, dacs[0], dacs[1], dacs[2], 0x00)  # LP
            elif page == 1:
                #ws.warning("A1 {0} {1} {2} {3}".format(dacs[0], dacs[1], dacs[2], 0))
                self.set_feature([ch], [ce], 0xA1, dacs[0], dacs[1], dacs[2], dacs[3])  # MP
            elif page == 2:
                #ws.warning("A2 {0} {1} {2} {3}".format(dacs[0], dacs[1], dacs[2], 0))
                self.set_feature([ch], [ce], 0xA2, dacs[0], dacs[1], dacs[2], dacs[3])  # UP
            elif page == 3:
                #ws.warning("A3 {0} {1} {2} {3}".format(dacs[0], dacs[1], dacs[2], dacs[3]))
                self.set_feature([ch], [ce], 0xA3, dacs[0], dacs[1], dacs[2], dacs[3])  # XP
        elif levels == 3:
            if page == 0:
                self.set_feature([ch], [ce], 0x90, dacs[0], dacs[1], 0x00, 0x00)  # LP
            elif page == 1:
                self.set_feature([ch], [ce], 0x91, dacs[0], dacs[1], dacs[2], 0x00)  # MP
            elif page == 2:
                self.set_feature([ch], [ce], 0x92, dacs[0], dacs[1], dacs[2], 0x00)  # UP
        elif levels == 1:
            self.set_feature([ch], [ce], 0xB0, dacs[0], 0x00, 0x00, 0x00)  # SLC
        else:
            raise Exception("Wrong Page!")

        return True

    def set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 15:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # QLC
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA0, shift_code, 0x00, 0x00, 0x00)
        if level == 7:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, shift_code, 0x00, 0x00)
        if level == 13:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, shift_code, 0x00)

        if level == 2:
            self.set_feature(ch_list, [ce], 0xA1, shift_code, 0x00, 0x00, 0x00)
        if level == 6:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, shift_code, 0x00, 0x00)
        if level == 8:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, shift_code, 0x00)
        if level == 12:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, shift_code)

        if level == 4:
            self.set_feature(ch_list, [ce], 0xA2, shift_code, 0x00, 0x00, 0x00)
        if level == 9:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, shift_code, 0x00, 0x00)
        if level == 11:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, shift_code, 0x00)
        if level == 14:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, shift_code)

        if level == 0:
            self.set_feature(ch_list, [ce], 0xA3, shift_code, 0x00, 0x00, 0x00)
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, shift_code, 0x00, 0x00)
        if level == 5:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, shift_code, 0x00)
        if level == 10:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, shift_code)

        return True, shift_voltage

    def tlc_set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 6:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0x90, shift_code, 0x00, 0x00, 0x00) # page LSB
        if level == 1:
            self.set_feature(ch_list, [ce], 0x91, shift_code, 0x00, 0x00, 0x00) # page CSB
        if level == 2:
            self.set_feature(ch_list, [ce], 0x92, shift_code, 0x00, 0x00, 0x00) # page MSB
        if level == 3:
            self.set_feature(ch_list, [ce], 0x91, 0x00, shift_code, 0x00, 0x00) # page CSB
        if level == 4:
            self.set_feature(ch_list, [ce], 0x90, 0x00, shift_code, 0x00, 0x00) # page LSB
        if level == 5:
            self.set_feature(ch_list, [ce], 0x91, 0x00, 0x00, shift_code, 0x00) # page CSB
        if level == 6:
            self.set_feature(ch_list, [ce], 0x92, 0x00, shift_code, 0x00, 0x00) # page MSB

        return True, shift_voltage
    
    def slc_set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 1:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # SLC
        self.set_feature(ch_list, [ce], 0xB0, shift_code, 0x00, 0x00, 0x00)

        return True, shift_voltage
    
    def reset_vt_code(self, ch_list, ce, lun):
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)

        self.set_feature(ch_list, [ce], 0x90, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x91, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x92, 0x00, 0x00, 0x00, 0x00)

        self.set_feature(ch_list, [ce], 0xB0, 0x00, 0x00, 0x00, 0x00)
        return True


    CALIB_LEN = 4096

    def calib_setup(self, data_rate_mhz, calib_length, ce = 0, lun = 0):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)
        
        opcbuilder = nanocycler.opcodebuilder(ce)
        
        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_length)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.page_buffer_write_builder(opcbuilder)
        
        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.page_buffer_read_builder(opcbuilder)

        opcbuilder.cleanup()

        return True


    # ###########################################################
    # Utilities

    def page_info(self, page):

        if page < 18:
            levels = 3 # TLC
            idx = page - 0
            offset = 0    # Layer
        elif page < 2706:
            levels = 4 # QLC
            idx = page - 18
            offset = 1    # Layer
        elif page < 2778:
            levels = 3 # TLC
            idx = page - 2706
            offset = 113    # Layer
        elif page < 5466:
            levels = 4 # QLC
            idx = page - 2778
            offset = 117
        elif page < 5520:
            levels = 3 # TLC
            idx = page - 5466
            offset = 229
        else:
            levels = 1 # SLC
            idx = page - 5520
            offset = 232

        return  idx // (levels * self.STRING_NUMBER) + offset, idx % levels, levels, idx // levels + offset * self.STRING_NUMBER      #return layer, page_level, levels, wl


    def wl_info(self, wl): # wl is layer
        if wl < 1:
            levels = 3  # TLC
            first_page = 0
        elif wl < 113:
            levels = 4  # QLC
            first_page = 18 + (wl - 1) * levels * self.STRING_NUMBER
        elif wl < 117:
            levels = 3  # TLC
            first_page = 2706 + (wl - 113) * levels * self.STRING_NUMBER
        elif wl < 229:
            levels = 4  # QLC
            first_page = 2778 + (wl - 117) * levels * self.STRING_NUMBER
        elif wl < 232:
            levels = 3  # TLC
            first_page = 5466 + (wl - 229) * levels * self.STRING_NUMBER
        else:
            levels = 1  # SLC
            first_page = 5520 + (wl - 232) * levels * self.STRING_NUMBER

        return first_page, levels


    def pages_to_wl_range(self, page_list):
        wl_min = 235
        wl_max = 0
        for page in page_list:
            wl, _, _, _ = self.page_info(page)
            if wl < wl_min:
                wl_min = wl
            if wl > wl_max:
                wl_max = wl

        return wl_min, wl_max

    def edge_wl_group(self, edge_wl):   # here wl is layer
        if 0 <= edge_wl <= 70:
            return range(123, 176), 1   #0~30%, edge
        elif 71 <= edge_wl <= 141:
            return range(176, 229), 2   #30~60%, edge
        elif 142 <= edge_wl <= 234:
            return range(229, 282), 3   #60~99%, edge
        
    def inner_wl_group(self, inner_wl): # here wl is layer
        if 0 <= inner_wl <= 70:
            return range(282, 335), 4   #0~30%, inner
        elif 71 <= inner_wl <= 141:
            return range(335, 388), 5   #30~60%, inner
        elif 142 <= inner_wl <= 188:
            return range(388, 441), 6   #60~80%, inner
        elif 189 <= inner_wl <= 234:
            return range(70, 123), 0   #80~99%, inner
        

    def qlc_inner_edge_wl_assign(self, end_page):

        end_layer, _, end_levels, end_wl = self.page_info(end_page)
        end_string = end_wl % self.STRING_NUMBER
        _, last_2nd_layer_levels = self.wl_info(end_layer-1)
        _, last_3rd_layer_levels = self.wl_info(end_layer-2)

        # 最后三个layer对应的mode分别为：
        # 1. QLC/QLC/QLC: inner_wl为range(0, end_wl-2*STRING_NUMBER+1), edge_wl为range(end_wl-2*STRING_NUMBER+1, end_wl-STRING_NUMBER+1), 否则为empty_wl                 
        # 2. QLC/QLC/TLC: inner_wl为range(0, end_wl-2*STRING_NUMBER+1) + range(end_wl-STRING_NUMBER-end_string, end_wl-STRING_NUMBER+1), edge_wl为range(end_wl-2*STRING_NUMBER+1, end_wl-STRING_NUMBER-end_string)+range(end_wl-end_string, end_wl+1), 否则为empty_wl                 
        # 3. QLC/TLC/TLC: inner_wl为range(0, end_wl-STRING_NUMBER+1), edge_wl为range(end_wl-STRING_NUMBER+1, end_wl+1)
        # 4. TLC/TLC/QLC: inner_wl为range(0, end_wl-self.STRING_NUMBER-end_string), edge_wl为range(end_wl-self.STRING_NUMBER-end_string, end_wl-end_string), 否则为empty_wl
        # 5. TLC/QLC/QLC: inner_wl为range(0, end_wl-2*STRING_NUMBER+1), edge_wl为range(end_wl-2*STRING_NUMBER+1, end_wl-STRING_NUMBER+1), 否则为empty_wl
        # 6. 其他情况： inner_wl为range(0, end_wl-STRING_NUMBER+1), edge_wl为range(end_wl-STRING_NUMBER+1, end_wl+1)
        
        inner_wl_range = []
        edge_wl_range = []
        
        if last_3rd_layer_levels == 4 and last_2nd_layer_levels == 4 and end_levels == 4:
            # QLC/QLC/QLC
            inner_wl_range = list(range(0, end_wl-2*self.STRING_NUMBER+1))
            edge_wl_range = list(range(end_wl-2*self.STRING_NUMBER+1, end_wl-self.STRING_NUMBER+1))
        elif last_3rd_layer_levels == 4 and last_2nd_layer_levels == 4 and end_levels == 3:
            # QLC/QLC/TLC
            inner_wl_range = list(range(0, end_wl-2*self.STRING_NUMBER+1)) + list(range(end_wl-self.STRING_NUMBER-end_string, end_wl-self.STRING_NUMBER+1))
            edge_wl_range = list(range(end_wl-2*self.STRING_NUMBER+1, end_wl-self.STRING_NUMBER-end_string)) + list(range(end_wl-end_string, end_wl+1))
        elif last_3rd_layer_levels == 4 and last_2nd_layer_levels == 3 and end_levels == 3:
            # QLC/TLC/TLC
            inner_wl_range = list(range(0, end_wl-self.STRING_NUMBER+1))
            edge_wl_range = list(range(end_wl-self.STRING_NUMBER+1, end_wl+1))
        elif last_3rd_layer_levels == 3 and last_2nd_layer_levels == 3 and end_levels == 4:
            # TLC/TLC/QLC
            inner_wl_range = list(range(0, end_wl-self.STRING_NUMBER-end_string))
            edge_wl_range = list(range(end_wl-self.STRING_NUMBER-end_string, end_wl-end_string))
        elif last_3rd_layer_levels == 3 and last_2nd_layer_levels == 4 and end_levels == 4:
            # TLC/QLC/QLC
            inner_wl_range = list(range(0, end_wl-2*self.STRING_NUMBER+1))
            edge_wl_range = list(range(end_wl-2*self.STRING_NUMBER+1, end_wl-self.STRING_NUMBER+1))
        else:
            # 其他情况
            inner_wl_range = list(range(0, end_wl-self.STRING_NUMBER+1))
            edge_wl_range = list(range(end_wl-self.STRING_NUMBER+1, end_wl+1))
        
        return inner_wl_range, edge_wl_range


    # ###########################################################
    # QLC  Program Sequence

    def qlc_program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column, is_coarse = False):

        wl, weight, levels,_  = self.page_info(page)

        # ws.info("Coarse Program Page: {0} - WL: {1} - Weight: {2} - Levels: {3}".format(page, wl, weight,  levels))

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if is_coarse:
                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_X36070_CMD.COARSE_PROGRAMMING_DCh)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            # be carefull in case of multi block multi lun only the last will be acquired
            if bI4:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if weight == levels - 1:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if weight == levels - 1:
            logger.log_program("coarse" if is_coarse else "fine", ch_list, ce_list, lun_list, [block], page, en_i=bI2 or bI4)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list : []):
        bOk = True

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)

        last_program_pages = []

        for wl in range(wl_min, wl_max+1):

            first_page, levels = self.wl_info(wl)

            # pages from program coarse
            current_program_pages = range(first_page, first_page + levels * self.STRING_NUMBER)

            if levels == 4:
                # coarse program on current
                for page in current_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, True)

                # fine program on last
                for page in last_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                last_program_pages = current_program_pages
            else:
                # fine program on last
                for page in last_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                # fine program on current
                for page in current_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                last_program_pages = []

        # fine program on last
        for page in last_program_pages:
            self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

        return bOk


    # ###########################################################
    # QLC  Multiplane Program Sequence

    def qlc_multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column, is_coarse = False):

        _, weight, levels, _ = self.page_info(page)   #layer, page_level, levels

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        if is_coarse:
            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_X36070_CMD.COARSE_PROGRAMMING_DCh)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)
                # if page == 0:   #hang, add for debug
                #     ws.info("ch_list:{0}, ce_list:{1}, lun:{2}, block:{3}, page:{4}, row_address:{5}".format(ch_list, ce_list, lun, block, page, row_address))

                # pattern is the same between ce and ch use the first
                # CBaseDevice.recall_pattern(self, ce_list[0], lun, block, page)
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2)  # 2 to use register seed

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                # be carefull in case of multi block multi lun only the last will be acquired
                if bI4:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if weight == levels - 1:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if weight == levels - 1:
            logger.log_program("coarse" if is_coarse else "fine", ch_list, ce_list, lun_list, block_list, page, rbTimeName="tPROG", en_i=bI2 or bI4)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):
        bOk = True

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)  #wl is layer

        last_program_pages = []

        for wl in range(wl_min, wl_max+1):

            first_page, levels = self.wl_info(wl)

            # pages from program coarse
            current_program_pages = range(first_page, first_page + levels * self.STRING_NUMBER)

            if levels == 4:
                # coarse program on current
                for page in current_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, True)

                # ws.info("current program wl:{0}, level:{1}, program stage:coarse, page:{2}".format(wl, levels,current_program_pages))

                # fine program on last
                for page in last_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)

                # ws.info("current program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages))

                last_program_pages = current_program_pages
            else:
                # fine program on current   #hang  #change this part to before to fix last 2 QLC layers FBC high issue
                for page in current_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                # ws.info("current program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,current_program_pages))

                # fine program on last
                for page in last_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                # ws.info("current program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages))

                last_program_pages = []

        # fine program on last
        for page in last_program_pages:
            self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
        # ws.info("current program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages))

        return bOk
    
    def multi_plane_sawtooth_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):
        bOk = True

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)  #wl is wordline
        previous_first_page, previous_wl_levels = self.wl_info(wl_min-1) if (wl_min -1 >= 0) else (None, None)

        last_program_pages = [[] for i in range(self.STRING_NUMBER)]
        current_program_pages = [[] for i in range(self.STRING_NUMBER)]

        for wl in range(wl_min, wl_max+1):

            first_page, levels = self.wl_info(wl)   #layer first page and levels

            # start_string for program
            if wl == wl_min:
                start_string = (page_list[0] - first_page) // levels
                if page_list[0] > 2:   #close partial block
                    if previous_wl_levels == 4:
                        for string in range(start_string, self.STRING_NUMBER):
                            last_program_pages[string] = range(previous_first_page + string * previous_wl_levels, previous_first_page + previous_wl_levels * (string + 1))
                    if levels == 4:
                        for string in range(0, start_string):
                            last_program_pages[string] = range(first_page + string * levels, first_page + levels * (string + 1))
            else:
                start_string = 0
            
            # end_string for program
            if wl == wl_max:
                end_string = (page_list[-1] - first_page) // levels + 1
            else:
                end_string = self.STRING_NUMBER
            
            for string in range(start_string, end_string):
                if levels == 4:
                    # coarse program on current
                    current_program_pages[string] = range(first_page + string * levels, first_page + levels * (string + 1))
                    for page in current_program_pages[string]:
                        self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, True)
                    # ws.info("current program wl:{0}, level:{1}, program stage:coarse, page:{2}".format(wl, levels,current_program_pages[string]))

                    # fine program on last
                    for page in last_program_pages[string]:
                        self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                    # ws.info("last program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages[string]))

                    last_program_pages[string] = current_program_pages[string]
                else:
                    # fine program on current   #hang  #change this part to before to fix last 2 QLC layers FBC high issue
                    current_program_pages[string] = range(first_page + string * levels, first_page + levels * (string + 1))
                    for page in current_program_pages[string]:
                        self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                    # ws.info("current program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,current_program_pages[string]))

                    # fine program on last
                    for page in last_program_pages[string]:
                        self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                    # ws.info("last program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages[string]))

                    last_program_pages[string] = []

        # close partial block, fine program on last layer; partial program don't need to program last layer
        if wl_max == self.WL_NUMBER // self.STRING_NUMBER:
            for string in range(0, end_string):
                for page in last_program_pages[string]:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)
                # ws.info("last program wl:{0}, level:{1}, program stage:fine, page:{2}".format(wl, levels,last_program_pages[string]))

        return bOk


    # #################################################
    # READ OFFSET

    def offset_to_voltage(self, offset):
        offset_voltage = -1280 + offset * 10.0

        return offset_voltage


    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):

        if level > 14:
            return False, 0.0

        offset_code = self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        # QLC
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA0, offset_code, 0x00, 0x00, 0x00)
        if level == 7:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, offset_code, 0x00, 0x00)
        if level == 13:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, offset_code, 0x00)

        if level == 2:
            self.set_feature(ch_list, [ce], 0xA1, offset_code, 0x00, 0x00, 0x00)
        if level == 6:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, offset_code, 0x00, 0x00)
        if level == 8:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, offset_code, 0x00)
        if level == 12:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, offset_code)

        if level == 4:
            self.set_feature(ch_list, [ce], 0xA2, offset_code, 0x00, 0x00, 0x00)
        if level == 9:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, offset_code, 0x00, 0x00)
        if level == 11:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, offset_code, 0x00)
        if level == 14:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, offset_code)

        if level == 0:
            self.set_feature(ch_list, [ce], 0xA3, offset_code, 0x00, 0x00, 0x00)
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, offset_code, 0x00, 0x00)
        if level == 5:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, offset_code, 0x00)
        if level == 10:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, offset_code)

        return True, offset_voltage
    
    def reset_read_offset_code(self, ch_list: [], ce, lun, level):

        # QLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)

        return True
    


    # #################################################
    # READ RETRY

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:

            # ws.info(">>> Option {0}/{1}".format(retry_option, len(self.option_buffer)))

            if retry_option >= len(self.option_buffer):
                return False, ""

            # csv file should contains lines of 15 parameters
            # if len(self.option_buffer[retry_option]) < 15:
            #     return False, ""

            if retry_option == 0:
                self.set_feature(ch_list, [ce], 0xB0, int(self.option_buffer[retry_option][0], 16), 0x00, 0x00, 0x00)
                self.set_feature(ch_list, [ce], 0x90, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][4], 16), 0x00, 0x00)
                self.set_feature(ch_list, [ce], 0x91, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), 0x00)
                self.set_feature(ch_list, [ce], 0x92, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), 0x00, 0x00)
                self.set_feature(ch_list, [ce], 0xA0, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][7], 16), int(self.option_buffer[retry_option][13], 16), 0x00)
                self.set_feature(ch_list, [ce], 0xA1, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), int(self.option_buffer[retry_option][8], 16), int(self.option_buffer[retry_option][12], 16))
                self.set_feature(ch_list, [ce], 0xA2, int(self.option_buffer[retry_option][4], 16), int(self.option_buffer[retry_option][9], 16), int(self.option_buffer[retry_option][11], 16), int(self.option_buffer[retry_option][14], 16))
                self.set_feature(ch_list, [ce], 0xA3, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), int(self.option_buffer[retry_option][10], 16))
            else:
                current_retry_feature = [item for item in self.option_buffer[retry_option] if item!= ''] #hang
                # ws.info(">>> Option {0}/{1}".format(retry_option, current_retry_feature))   #hang
                if len(current_retry_feature) == 1:
                    #SLC
                    self.set_feature(ch_list, [ce], 0xB0, int(current_retry_feature[0], 16), 0x00, 0x00, 0x00)
                elif len(current_retry_feature) == 7:
                    #TLC
                    self.set_feature(ch_list, [ce], 0x90, int(current_retry_feature[0], 16), int(current_retry_feature[4], 16), 0x00, 0x00)
                    self.set_feature(ch_list, [ce], 0x91, int(current_retry_feature[1], 16), int(current_retry_feature[3], 16), int(current_retry_feature[5], 16), 0x00)
                    self.set_feature(ch_list, [ce], 0x92, int(current_retry_feature[2], 16), int(current_retry_feature[6], 16), 0x00, 0x00)
                elif len(current_retry_feature) == 15:
                    # QLC
                    self.set_feature(ch_list, [ce], 0xA0, int(current_retry_feature[1], 16), int(current_retry_feature[7], 16), int(current_retry_feature[13], 16), 0x00)
                    self.set_feature(ch_list, [ce], 0xA1, int(current_retry_feature[2], 16), int(current_retry_feature[6], 16), int(current_retry_feature[8], 16), int(current_retry_feature[12], 16))
                    self.set_feature(ch_list, [ce], 0xA2, int(current_retry_feature[4], 16), int(current_retry_feature[9], 16), int(current_retry_feature[11], 16), int(current_retry_feature[14], 16))
                    self.set_feature(ch_list, [ce], 0xA3, int(current_retry_feature[0], 16), int(current_retry_feature[3], 16), int(current_retry_feature[5], 16), int(current_retry_feature[10], 16))
                else:
                    return  False, ""


            # if len(self.option_buffer[retry_option]) == 1:
            #     #SLC
            #     self.set_feature(ch_list, [ce], 0xB0, int(self.option_buffer[retry_option][0], 16), 0x00, 0x00, 0x00)
            # elif len(self.option_buffer[retry_option]) == 7:
            #     #TLC
            #     self.set_feature(ch_list, [ce], 0x90, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][4], 16), 0x00, 0x00)
            #     self.set_feature(ch_list, [ce], 0x91, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), 0x00)
            #     self.set_feature(ch_list, [ce], 0x92, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), 0x00, 0x00)
            # elif len(self.option_buffer[retry_option]) == 15:
            #     # QLC
            #     self.set_feature(ch_list, [ce], 0xA0, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][7], 16), int(self.option_buffer[retry_option][13], 16), 0x00)
            #     self.set_feature(ch_list, [ce], 0xA1, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), int(self.option_buffer[retry_option][8], 16), int(self.option_buffer[retry_option][12], 16))
            #     self.set_feature(ch_list, [ce], 0xA2, int(self.option_buffer[retry_option][4], 16), int(self.option_buffer[retry_option][9], 16), int(self.option_buffer[retry_option][11], 16), int(self.option_buffer[retry_option][14], 16))
            #     self.set_feature(ch_list, [ce], 0xA3, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), int(self.option_buffer[retry_option][10], 16))
            # else:
            #     return  False, ""
            # add here other column if necessary

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""


    def reset_read_retry_option(self, ch_list: [], ce, lun):
        #SLC
        self.set_feature(ch_list, [ce], 0xB0, 0x00, 0x00, 0x00, 0x00)
        #TLC
        self.set_feature(ch_list, [ce], 0x90, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x91, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x92, 0x00, 0x00, 0x00, 0x00)
        # QLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)
        # add here other column if necessary
        return True

    def is_bad_block(self, ch_list: [], ce, lun, block):
        
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        bRes, lRowAddress = self.build_row_address(lun, block, 0)
        column_address = 0
        
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.SLC_ENTER_CMD_DAh)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        # opcbuilder.set_column_address(column_address)
        # opcbuilder.set_row_address(lRowAddress)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        self.get_status_enhanced_78h(ch_list, ce, lun, block)

        for ch in ch_list:
            self.select_channel(ch)
            sr = hw.custom_sequence_sdr_get_data_byte(0)
            is_bad = (sr & 0x1) != 0
            logger.log_bad_block(ch, ce, lun, block, is_bad)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_CMD.SLC_EXIT_CMD_DFh)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

    # dcc training by set feature
    def dcc_training(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("dcc_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        self.get_feature([ch], ce, 0x20)
        hw.select_channel(ch)
        p1 = hw.custom_sequence_sdr_get_data_byte(0)
        p2 = hw.custom_sequence_sdr_get_data_byte(1)
        p3 = hw.custom_sequence_sdr_get_data_byte(2)
        p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 before DCC: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        self.set_feature([ch], [ce], 0x20, p1 | 0x81, p2, p3, p4)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_ALE, 0) # add based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        self.apply_col_address(opcbuilder, column_address)
        self.apply_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)     # tWHRT
        # opcbuilder.add(eNandBackdoor.BD_R_NB, 0) # R/B not change based on YMTC data sheet 
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("DCC Training SR: {0}".format(hex(sr)))

        # self.set_feature([ch], [ce], 0x20, p1, p2, p3, p4)

        opcbuilder.cleanup()
        return
    
    def read_training(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("read_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)    #start DQ(/IO) driving
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)   #DQS signal driven by Nanocycler
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X62)  # read dq training
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)   # LUN stress
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X35)  # invert mask
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X5A)  # first 8bit pattern
        opcbuilder.add(eNandBackdoor.BD_ALE, 0X82)  # second 8bit pattern
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)     # tWHRT Eric: 400 -> 500
        ## opcbuilder.add(eNandBackdoor.BD_R_NB, 0)    #Ready Bot Busy goes low than high 
        # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)    #stop DQ(/IO) driving
        # opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB) #200ns
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)  #set RE_T low
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  #set DQS_T low
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)   #DQS signal driven by device
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE) #100ns
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 16)   #Data out to register        
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)  #set DQS_T high
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)  #set RE_T high
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        hw.select_channel(ch)
        buffer = self.get_read_buffer(ch, 16) #self.PAGE_LENGTH
        ws.info("Read Training page buffer:{0}".format(self.format_array(buffer)))

        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("Read Training SR: {0}".format(hex(sr)))

        opcbuilder.cleanup()
        return

    def write_training_Tx_side(self, ch, ce, lun):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        #ws.info("dcc_training - CH: {0} - CE: {1} .....".format(ch,ce))

        ch_mask = self.index_list_to_mask([ch])
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)

        self.get_feature([ch], ce, 0x20)
        hw.select_channel(ch)
        p1 = hw.custom_sequence_sdr_get_data_byte(0)
        p2 = hw.custom_sequence_sdr_get_data_byte(1)
        p3 = hw.custom_sequence_sdr_get_data_byte(2)
        p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 before Write: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        self.set_feature([ch], [ce], 0x20, p1, p2, p3 | 0xF, p4)

        # self.get_feature([ch], ce, 0x20)
        # hw.select_channel(ch)
        # p1 = hw.custom_sequence_sdr_get_data_byte(0)
        # p2 = hw.custom_sequence_sdr_get_data_byte(1)
        # p3 = hw.custom_sequence_sdr_get_data_byte(2)
        # p4 = hw.custom_sequence_sdr_get_data_byte(3)
        # ws.info("Get Feature 0x20 during Write: {0} {1} {2} {3}".format(hex(p1), hex(p2), hex(p3), hex(p4)))

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X63)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, 128)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0X64)
        opcbuilder.add(eNandBackdoor.BD_ALE, lun)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tWHRT
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB) #200ns
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 128)
        
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        hw.select_channel(ch)
        buffer = self.get_read_buffer(ch, 128) #self.PAGE_LENGTH
        ws.info("Write Tx Training page buffer:{0}".format(self.format_array(buffer)))
        opcbuilder.clear()

        self.get_status([ch],ce)

        hw.select_channel(ch)
        sr = hw.custom_sequence_sdr_get_data_byte(0)
        ws.info("Write Tx Training SR: {0}".format(hex(sr)))

        self.set_feature([ch], [ce], 0x20, p1, p2, p3, p4)

        opcbuilder.cleanup()
        return