""" Singleton class to manage C libraries"""

# !@cond Doxygen_Suppress

try:
    import ffi

    def _open_library(lib_path: str):
        lib_handle = ffi.open(lib_path)
        return lib_handle

    def _load_func(lib_handle, ret: str, func_name: str, param: str):
        return lib_handle.func(ret, func_name, param)

    def _close_library(lib_handle):
        lib_handle.close()

except ImportError:

    def _open_library(lib_path: str):
        return None

    def _load_func(lib, ret: str, func_name: str, param: str):
        return None

    def _close_library(lib_handle):
        return


class CLibManagerSingleton:
    _libwslibpy = None
    _libonfilib = None
    _libncdblib = None
    _libwslib = None
    # _libnumpy = None    #hang, add for numpy
    _libcalpattern = None    # 添加 cal_pattern.so 库


    def __init__(self):
        """ Virtually private constructor. """
        raise Exception("This class is a singleton!")


    @classmethod
    def load_libwslibpy(cls):
        if cls._libwslibpy is None:
            cls._libwslibpy = _open_library("./workspace/libwslibpy.so")

        return cls._libwslibpy


    @classmethod
    def load_libonfilib(cls):
        if cls._libonfilib is None:
            cls._libonfilib = _open_library("./workspace/libonfilib.so")

        return cls._libonfilib


    @classmethod
    def load_libncdblib(cls):
        if cls._libncdblib is None:
            cls._libncdblib = _open_library("./workspace/libncdblib.so")

        return cls._libncdblib


    @classmethod
    def load_libwslib(cls):
        if cls._libwslib is None:
            cls._libwslib = _open_library("./workspace/libwslib.so")

        return cls._libwslib

    # @classmethod
    # def load_libnumpy(cls):     #hang, add for numpy
    #     if cls._libnumpy is None:
    #         cls._libnumpy = _open_library("./workspace/multiarray_umath_cpython_34m.so")

    #     return cls._libnumpy

    @classmethod
    def load_libcalpattern(cls):
        if cls._libcalpattern is None:
            cls._libcalpattern = _open_library("./workspace/cal_pattern.so")

        return cls._libcalpattern


    @classmethod
    def unload_all(cls):
        if cls._libwslibpy is not None:
            cls._libwslibpy.close()
            cls._libwslibpy = None

        if cls._libonfilib is not None:
            cls._libonfilib.close()
            cls._libonfilib = None

        if cls._libncdblib is not None:
            cls._libncdblib.close()
            cls._libncdblib = None

        if cls._libwslib is not None:
            cls._libwslib.close()
            cls._libwslib = None

        # if cls._libnumpy is not None:
        #     cls._libnumpy.close()
        #     cls._libnumpy = None

        if cls._libcalpattern is not None:
            cls._libcalpattern.close()
            cls._libcalpattern = None

    @staticmethod
    def load_func(lib_handle, ret: str, func_name: str, param: str):
        return _load_func(lib_handle, ret, func_name, param)

# !@endcond