import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM



class TOSHIBA_CMD:

    PROGRAM_SUSPEND_CMD_A7h = 0xA7
    PROGRAM_RESUME_CMD_48h = 0x48



class CToshiba(COnfiDevice):
    def __init__(self):
        COnfiDevice.__init__(self)
        self.DEVICE_MANUFACTURER = "Toshiba"
        self.DEVICE_NAME = "xxx"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 2  # plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER = 2
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.WL_NUMBER = 256
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # per block

        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 8
        self.VALID_LUN_MASK = 0x01
        self.VALID_WL_MASK = 0xFF
        self.VALID_BLOCK_MASK = 0x1FFF

        self.RR_OPTIONS = 32

        self.aLevels = ["0", "1", "2"]

    def address_info(self, lLun, lBlock, lPage):
        lWL = int(lPage / self.LEVEL_NUMBER)
        acLevel = self.aLevels[lPage % self.LEVEL_NUMBER]

        return True, lWL, acLevel

    def build_row_address(self, lLun, lBlock, lWordLine):
        lRowAddress = (((lLun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | (
                    (lBlock & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (lWordLine % self.WL_NUMBER))
        return True, lRowAddress



    def read_unique_id(self, ch_list: [], ce):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5A)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xB5)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x00)

        opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0x28)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)

        opcbuilder.add(eNandBackdoor.BD_CLE, 0x30)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 256)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        # opcbuilder.add(eNandBackdoor.BD_CLE, 0xFF)
        # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        # opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            res, buffer = hw.custom_sequence_get_out_buffer(256)

            for s in range(8):

                res = True

                for i in range(16):
                    check = buffer[s * 32 + i] | buffer[s * 32 + 16 + i]
                    if check != 0xFF:
                        res = False
                        break

                if res:
                    uid = COnfiDevice.format_array(self, buffer[s * 32: s * 32 + 16])
                    logger.log_device_id([ch], [ce], "UID", uid)
                    COnfiDevice.load_info(self, ch, ce, uid, self.DEVICE_NAME, self.device_id, self.manufacturer_code, self.model)
                    break
                else:
                    ws.error("Ch: {0} - Ce: {1} - Set {2} is not valid - {3} / {4}".format(ch, ce, s,
                                                                                           COnfiDevice.format_array(self, buffer[s * 32: s * 32 + 8]),
                                                                                           COnfiDevice.format_array(self, buffer[s * 32 + 8: s * 32 + 16])))




    def identification(self):

        # turn on the device with device specific level
        COnfiDevice.turn_on(self, self.VCC, self.VCCQ)

        # during initilize configure the default (min) frequency
        hw.set_datarate(hw.default_datarate_MTs())

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        lun_list = range(0, self.LUN_NUMBER)

        # assign default chunk length for each channel
        for ch in ch_list:
            hw.select_channel(ch)
            hw.set_chunk_length(self.CHUNK_LENGTH)

        # Don't know why this doesn't work
        # self.die_configure(ch_list, ce_list, 4, 4)

        for ch in ch_list:
            self.die_configure([ch], ce_list, 4, 4)

            for ce in ce_list:
                # Read Device Id
                COnfiDevice.device_id_read(self, [ch], ce, 0, self.DEVICE_ID_LEN)
                # Read page parameters
                COnfiDevice.page_parameter_read(self, [ch], ce, 0)
                # Read Unique id
                self.read_unique_id([ch], ce)

        # after read unique id it is necesarry to reset and reconfigure all die
        for ch in ch_list:
            self.die_configure([ch], ce_list, 4, 4)

        # before exit perform perform ZQ Calib Long
        for lun in lun_list:
            COnfiDevice.zq_calib_long(self, ch_list, ce_list, lun)




    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        opcbuilder.clear()
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        for lun_idx in range(len(lun_list)):

            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            COnfiDevice.recall_pattern(self, ch_list, ce_list[0], lun, block, page)

            bRes, lRowAddress = self.build_row_address(lun, block, wl)
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
            # be carefull in case of multiple lun only last lun should be acquired
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

            if page_cmd < self.LEVEL_NUMBER:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i=bI2 or bI4w)

        # skip if log id not enabled or we do not want to log SR
        # if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)



    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
        # hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # record I2 and I4w only for upper page (2) of the last block
                bI2 = self.pmu_algo_is_I2()
                bI4w = self.pmu_algo_is_I4w()
                if (page % 3) != 2 or block_idx != (len(block_list) - 1):
                    bI2 = False
                    bI4w = False

                res, row_address = self.build_row_address(lun, block, wl)
                # ws.info('lRowAddress:LUN-{}; Block-{}; Page-{}; {}'.format(lun, block, page, row_address))

                # pattern is the same between ce use the first
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                # ws.info('prog recall pattern -> block: {0}, page: {1}, seed_high: {2}, seed_low: {3}'.format(block, page, seed_high, seed_low))
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2)  # 2 to use register seed
                # opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)

                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.set_row_address(row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
                # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

                rbName = "tBSY"
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if page_cmd < self.LEVEL_NUMBER:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                    else:
                        rbName = "tPRG"
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)

                if bI2:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                if bI2:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)

                if (page % 3) == 2:
                    hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                    opcbuilder.clear()
                    logger.log_program("MP-PRG", ch_list, ce_list, [lun], block_list, page, rbTimeName="tPROG",
                                    en_i=bI2 or bI4w)
                    
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # skip if log id not enabled or we do not want to log SR
        # if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)


    # Figure 5‐1. Page Read Operation Sequence
    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = column
        res, row_address = self.build_row_address(lun, block, wl)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    # Figure 5‐1. Page Read Operation Sequence
    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        # TODO remove (just for debugging)
        # return self.page_buffer_compare(ch_list, ce, lun, block, page, column, page_length)

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        column_address = column
        bRes, row_address = self.build_row_address(lun, block, wl)

        COnfiDevice.recall_pattern(self, ch_list, ce, lun, block, page)

        COnfiDevice.set_ram_data_3(self, ch_list, column_address, row_address, page_cmd)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # in order to improve the performance, the sequence is created only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to execute on different ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            if bI1:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE_REG, 8) # page command is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
            if bI4r:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 4 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 4 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)


    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list: [], page, lColumn, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        lColumnAddress = lColumn

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        # hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder) # ICC 1 start

        # ws.info('block_list:{}'.format(block_list))
        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)
            # ws.info('lRowAddress:CE-{}; LUN-{}; Block-{}; Page-{}; {}'.format(ce, lun, block, page, lRowAddress))

            # opcbuilder.clear()
            # opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            
            opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(lColumnAddress)
            opcbuilder.set_row_address(lRowAddress)

            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i = bI1)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            seed_high, seed_low = self.recall_pattern(ch_list, ce, lun, block, page)
            # ws.info('read recall pattern -> block: {0}, page: {1}, seed_high: {2}, seed_low: {3}'.format(block, page, seed_high, seed_low))

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
            opcbuilder.set_column_address(lColumnAddress)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder) # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False, en_i = bI4r)

        opcbuilder.cleanup()


    # info not available in datasheet
    def read_internal_temperature(self, ch_list: [], ce):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xB9)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x7C)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            temp = hw.custom_sequence_sdr_get_data_byte(0) - 42
            logger.log_die_temperature("READ_INT_TEMP", [ch], [ce], temp)


    # Figure 3‐7. Area marked in First Page of Block indicating Defect
    def is_bad_block(self, ch_list: [], ce, lun, block):

        self.page_read(ch_list, ce, lun, block, 0, 0, self.PAGE_LENGTH)

        for ch in ch_list:
            hw.select_channel(ch)
            buffer = COnfiDevice.get_read_buffer(self, ch, self.PAGE_LENGTH)
            is_bad = (buffer[0] == 0 and buffer[16384] == 00)
            logger.log_bad_block(ch, ce, lun, block, is_bad)




    # #############################################################################à
    # Program Suspend

    def multi_plane_program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, suspend_cmd_delay):
        column = 0

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        page_suspended = False

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, wl)

                # pattern is the same between ce use the first
                COnfiDevice.recall_pattern(self, ch_list, ce_list[0], lun, block, page)

                suspend_flag = False

                opcbuilder.clear()
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.set_row_address(row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if page_cmd < self.LEVEL_NUMBER:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                        suspend_flag = True

                if suspend_flag:
                    # delay than suspend all luns
                    opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)
                    opcbuilder.add(eNandBackdoor.BD_CLE, TOSHIBA_CMD.PROGRAM_SUSPEND_CMD_A7h)


                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

                logger.log_program("MP-PRG-SPD", ch_list, ce_list, [lun], [block], page, rbTimeName = "tPSPD" if suspend_flag else "tPROG")

                page_suspended |= suspend_flag

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # skip if log id not enabled or we do not want to log SR
        # if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)

        return page_suspended



    def multi_plane_program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, suspend_cmd_delay=-1):

        page_cmd = page % self.LEVEL_NUMBER + 1

        # only last page of the word line was suspended
        if page_cmd  != self.LEVEL_NUMBER:
            ws.info("Page {0} was not suspended".format(page))
            return

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        # apply resume for each lun
        for lun in lun_list:
            block = block_list[0]

            # TODO CHECK ??? (maybe it is not ok for Bics3)
            # enhanced SR read just to select the lun
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)

            # then apply resume command
            opcbuilder.add(eNandBackdoor.BD_CLE, TOSHIBA_CMD.PROGRAM_RESUME_CMD_48h)

        if not suspend_cmd_delay == -1:

            # delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            # apply suspend for each lun
            for lun in lun_list:
                block = block_list[0]

                # enhanced SR read just to select the lun
                res, row_address = COnfiDevice.build_row_address(lun, block, 0)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)

                opcbuilder.add(eNandBackdoor.BD_CLE, TOSHIBA_CMD.PROGRAM_SUSPEND_CMD_A7h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("MP-PRG-SPD", ch_list, ce_list, lun_list, block_list, page,
                           rbTimeName="tPROG" if suspend_cmd_delay == -1 else "tPSPD")

        # skip if log id not enabled or we do not want to log SR
        # if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)


    # #############################################################################à
    # Read Retry

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):

        if self.option_buffer:

            if retry_option >= len(self.option_buffer):
                return False, ""

            #ws.info(">>> Option {0}/{1}: {2}".format(retry_option, len(self.option_buffer), self.option_buffer[retry_option]))

            # csv file should contains lines of 7 parameters
            if len(self.option_buffer[retry_option]) < 7:
                return False, ""

            COnfiDevice.set_feature_by_lun(self, ch_list, [ce], lun, 0x89, int(self.option_buffer[retry_option][0], 16),
                                           int(self.option_buffer[retry_option][2], 16),
                                           int(self.option_buffer[retry_option][4], 16),
                                           int(self.option_buffer[retry_option][6], 16))
            COnfiDevice.set_feature_by_lun(self, ch_list, [ce], lun, 0x8A, int(self.option_buffer[retry_option][1], 16),
                                           int(self.option_buffer[retry_option][3], 16),
                                           int(self.option_buffer[retry_option][5], 16),
                                           0) # according spec P3 of address 0x8A is always zero

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""


    def reset_read_retry_option(self, ch_list: [], ce, lun):

        res = True
        COnfiDevice.set_feature_by_lun(self, ch_list, [ce], lun, 0x89, 0x00, 0x00, 0x00, 0x00)
        COnfiDevice.set_feature_by_lun(self, ch_list, [ce], lun, 0x8A, 0x00, 0x00, 0x00, 0x00)

        return res

    # #############################################################################à
    # Read Offset

    def set_read_offset_code(self, ch_list, ce, lun, level, offset):
        if level > 6:
            return False, 0

        # in order to have linear scan from min offeset to max offset
        offset_code = (offset + 0x80) & 0xFF
        offset_voltage = offset

        # TLC
        if level == 0:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x89, offset_code, 0x00, 0x00, 0x00)
        if level == 1:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x8A, offset_code, 0x00, 0x00, 0x00)
        if level == 2:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x89, 0x00, offset_code, 0x00, 0x00)
        if level == 3:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x8A, 0x00, offset_code, 0x00, 0x00)
        if level == 4:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x89, 0x00, 0x00, offset_code, 0x00)
        if level == 5:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x8A, 0x00, 0x00, offset_code, 0x00)
        if level == 6:
            COnfiDevice.set_feature(self, ch_list, [ce], 0x89, 0x00, 0x00, 0x00, offset_code)

        return True, offset_voltage


    def reset_read_offset_code(self, ch_list, ce, lun, level):

        COnfiDevice.set_feature(self, ch_list, [ce], 0x89, 0x00, 0x00, 0x00, 0x00)
        COnfiDevice.set_feature(self, ch_list, [ce], 0x8A, 0x00, 0x00, 0x00, 0x00)

        return True


    # #############################################################################à

    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            COnfiDevice.recall_pattern(self, ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block])

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)


    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: []):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)

        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                COnfiDevice.recall_pattern(self, ch_list, ce_list[0], lun, block, 0)

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                opcbuilder.set_row_address(row_address)

            # in case of multi-lun (interleaving operation), cmd D0h is applied for each lun
            # but the wait is only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        # if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
        if logger.is_in_log_set(LOG_SET_ITEM.SR):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        COnfiDevice.get_status_enhanced_78h(self, ch_list, ce, lun, block)
