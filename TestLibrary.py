"""
.. image:: ./images/NplusT-Logo.png

Python Test library for NanoCycler HS and STD systems.\n

The library contains the test code of each test brick available in the recipe editor; the test bricks are grouped in six categories:\n
- Basic
- Composite
- Power Profile
- Temperature
- Special
- VT
- Block Selection

The library contains the NAND algorithm like erase, program or complex functions like cycling or program suspend.
The library is common between the devices. The specific code to perform a test is implemented in a device specific file.
The library supports these devices:\n
- OnfiDevice: Generic Device according Onfi spec
- HynixV5
- HynixV6
- HynixV7
- KIOXIABiCS5
- KIOXIABiCS5QLC: TH58LKB4F25BA8K - TH58LKB8F25BA8J
- MicronB27A: MT29FxxxxxxxxHBF
- MicronB27B: MT29FxxxxxxxxLCE
- MicronB37R: MT29FxxxxxxxxLDE
- MicronB47R: MT29FxxxxxxxxLEE
- SandiskBiCS4: 3D NAND SDUNCIAMA
- SandiskBiCS5: 3D NAND SDQDIDMB-512G
- ToshibaBiCS3: TH58TFxxT23BAxx
- ToshibaBiCS4:
- ToshibaVHT: TH58VHT2T42VA8xx
- YMTCX19050
- YMTCX29060
- YMTCX26070

If you need to add a new test brick to convert a new function, the process is:\n
- In the RecipeManifest.xml, add the test tag to the tests collections of the test library;
- In TestLibrary.py, add the test name to the tests collection objects at the end of the file;
- Implement the new method with this signature `def my_test_brick(segment):`;
- Add code inside the method body to access user interface variables: `chs = segment.variables("MY_VAR").value()`
and implement your specific code.

If you need to add a new device, the process is:\n
- Add the new device file in the Devices Folder, copying from an existing and similar device file;
- In the file, change the name of the class and set the relative parameters like device name, page length, number of pages, ...;
- In the recipe manifest .xml, add the name of the device to the list of supported device;
- in the initialize_brick, add the if case to support the creation of this new device.

The TestLibrary supports measuring and logging of these parameters:\n
- tRnB: Ready busy time;
- FAILS: during the read operation, the library reads and compares the device content with the expected content. For each read operation (for each page read)
the number of bit fails are available;
- tRnB4CHUNK: Ready busy time for chunk used in SNAP/FAST read operation;
- FAILS4CHUNK: Number of fails for each chunk. The chunk length is a device parameter;
- SR: the device status register can be read and logged;
- DATA: generic data byte read from the device (used, for example, in page parameter read);
- PmuAvg: The system can measure the average current in a specific operation (between PMU start and PMU stop commandS);
- PmuMax: The system can measure the max current in a specific operation (between PMU start and PMU stop commandS). Available for HS systems  only;
- PmuSamples: In order to visualize and log the current profile during an operation, it is possible to access the current samples. Available for HS systems only.

For each operation, the log is controlled by the log_set and log_enable parameters:

        erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}
        device.init_test(log_set=erase_log_set)

For each test, changing these configuration will result in different log generation and different execution time.
In the same case, in order to reduce the test time and datalog size generation (for example during read_offset_ber of entire blocks,
it could be useful to access the fails but avoid logging them for each page read operation. To enable this case, this configuration can be used:

        read_log_set = {LOG_SET_ITEM.FAILS}
        device.init_test(log_enable=False, log_set=read_log_set)

NanoCycler system uses a database to store the seeds necessary to build the random pattern used to program a block.
When the device is identified by a unique identifier, the library synchronizes an internal seeds table in order to be able to verify a device
which was previously programmed with another Tester Unit or in another system.\n
After each test brick the seeds are synchronized with the database. \n
The same database is used to store the device bad block information. \n

Library contains 2 custom tests. They don't have the implementation in the library but they call a custom method in the device class. \n
If a device needs a special test, the engineer can write the required code in the custom test method of the device class file
(for example Soft Read has been implemented for KIOXIA QLC as custom test 1).  \n

Revision History: \n
- 6.0 (01-09-2021) - First Release with double channel management
- 6.1 (30-09-2021) - Added KIOXIABiCS5QLC, do_read_offset_ber added second routine for per pages results
- 6.2 (18-10-2021) - Improved Data Out using BD_INIT_DATA_OUT back door in order to improve read (and calibration) stability, added device identify
- 6.3 (22-11-2021) - Several changes on Hynix management, bug fix in MP ber calculus, added bricks for sequence capture management, added management for warmup/latency
- 6.4 (23-11-2021) - Added StartDac and EndDac to read Offset test, added Well time after erase in cycling operation, added ReadOffsetDump
- 6.5 (01-12-2021) - Extended program/read pattern algorithms: Added all0, inc, alternate 0xff 0x00 and alternate 0xaa 0x55
- 6.5.1 (16-12-2021) - Bug fix in YMTC get_status_enhanced_78h
- 6.5.2 (22-12-2021) - Added parameter to the initialize_brick to global disable of sequence recording,  added delay_with_read brick, minor fix in libonfilib.so
- 7.1.1 (2-2-2022) - Added support for HS20, improved DQ align to support multi CE device, added Page Buffer Test brick. A Python call is 2 time faster in HS system. Some changes to respect tDQSRH.
-                    Divided Initialize brick in Initialize (to select device and initialize test library parameters) and Device Config (to initialize and configure the device).
-                    BinaryFieWriter and NFSBinaryFieWriter merged in a single object.
- 7.2.1 (19-2-2022) - Added DataRetention brick and ReadOffsetInCommand
- 7.3.1 (22-2-2022) - Added TestLibraryConfig brick, added ReadOffset_BEST brick
- 7.3.2 (24-2-2022) - Improved ReadOffset_BEST, added recipe name to test library settings
- 7.4.2 (24-2-2022) - Bug fix
- 7.4.3 (25-2-2022) - Minor code changes
- 7.5.1 (01-03-2022) - Improved support for generic Onfi Device
- 7.5.2 (20-03-2022) - Improved support for generic TLC and QLC Devices
- 7.6.1 (28-03-2022) - Bug fix in System Temperature monitor, improved store_bad_blocks and store_seeds, added fpga_setup for hardware configuring
- 7.6.2 (31-03-2022) - Bug fix in global_list management, some code cleaning in device specific files
- 7.7.1 (01-04-2022) - Added B47R ReadRetry, removed page_Buffer_write calls from page_buffer read
- 7.7.2 (02-04-2022) - Changed Onfi.set_feature_async in order to use BD_DATA_WR
- 7.8.1 (06-04-2022) - Added SandiskBics5, force store_to_central before clear the uid list during initialization

"""

version = "7.8.1"


# import sys
# import os
# import csv
import nanocycler
from nanocycler import libmanager as libmng
from Devices.OnfiDevice import PATTERN_ALGO as PATTERN_ALGO
from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from lib.DataMgr import PATTERN_SEED as PATTERN_SEED
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from nanocycler import utility as utility

# list of supported NAND Devices
from Devices.Dummy import CDummy as CDummy
from Devices.TLCDevice import CTLCDevice as CTLCDevice
from Devices.QLCDevice import CQLCDevice as CQLCDevice

try:
    from Devices.HynixV5 import CHynixV5 as CHynixV5
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CHynixV5

try:
    from Devices.HynixV6 import CHynixV6 as CHynixV6
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CHynixV7

try:
    from Devices.HynixV7 import CHynixV7 as CHynixV7
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CHynixV6

try:
    from Devices.KIOXIABiCS5 import CKIOXIABiCS5 as CKIOXIABiCS5
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CKIOXIABiCS5

try:
    from Devices.KIOXIABiCS5QLC import CKIOXIABiCS5QLC as CKIOXIABiCS5QLC
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CKIOXIABiCS5QLC

try:
    from Devices.MicronB27A import CMicronB27A as CMicronB27A
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CMicronB27A

try:
    from Devices.MicronB27B import CMicronB27B as CMicronB27B
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CMicronB27B

try:
    from Devices.MicronB37R import CMicronB37R as CMicronB37R
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CMicronB37R

try:
    from Devices.MicronB47R import CMicronB47R as CMicronB47R
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CMicronB47R

try:
    from Devices.SandiskBiCS4 import CSandiskBiCS4 as CSandiskBiCS4
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS4

try:
    from Devices.SandiskBiCS5 import CSandiskBiCS5 as CSandiskBiCS5
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS5

try:
    from Devices.SandiskBiCS6_512Gb import CSandiskBiCS6_512Gb as CSandiskBiCS6_512Gb
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS6_512Gb

try:
    from Devices.SandiskBiCS6_1Tb import CSandiskBiCS6_1Tb as CSandiskBiCS6_1Tb
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS6_1Tb

try:
    from Devices.SandiskBiCS8 import CSandiskBiCS8 as CSandiskBiCS8
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS8

# try:
#     from Devices.SandiskBiCS6 import CSandiskBiCS6 as CSandiskBiCS6
# except ImportError:
#     from Devices.NotValidDevice import CNotValidDevice as CSandiskBiCS6

try:
    from Devices.ToshibaBiCS3 import CToshibaBiCS3 as CToshibaBiCS3
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CToshibaBiCS3

try:
    from Devices.ToshibaBiCS4 import CToshibaBiCS4 as CToshibaBiCS4
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CToshibaBiCS4

try:
    from Devices.ToshibaVHT import CToshibaVHT as CToshibaVHT
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CToshibaVHT

try:
    from Devices.YMTCX19050 import CYMTCX19050 as CYMTCX19050
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX19050

try:
    from Devices.YMTCX26070 import CYMTCX26070 as CYMTCX26070
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX26070

try:
    from Devices.YMTCX29060 import CYMTCX29060 as CYMTCX29060
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX29060

try:
    from Devices.YMTCX39070 import CYMTCX39070 as CYMTCX39070
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX39070

try:
    from Devices.YMTCX36070 import CYMTCX36070 as CYMTCX36070
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX36070

try:
    from Devices.YMTCX49060 import CYMTCX49060 as CYMTCX49060
except ImportError:
    from Devices.NotValidDevice import CNotValidDevice as CYMTCX49060

#################
### ALIASES   ###
#################

from nanocycler import enumPmuChannel as enumPmuChannel
from nanocycler import enumBlinkStatusType as eBlinkStatusType
from nanocycler import enumScaleType as enumScaleType
from nanocycler import NanoTimer as time

from nanocycler import enumPatternType as ePatternType
from nanocycler import datalog as datalog
from nanocycler import ws as ws
from nanocycler import pmu as pmu
from nanocycler import hardware as hw
from nanocycler import fitting as fitting   #hang
from nanocycler import cal_pattern as cal_pattern   #hang

from lib.DataMgr import the_data_mgr as data_mgr
from lib.ResultLogger import the_result_logger as logger

# import sys
# ws.info("MicroPython:{}".format(sys.implementation))
# ws.info("Platform:{}".format(sys.platform))
# ws.info("boardversion:{}".format(hw.get_board_version()))
# ws.info("fpgaversion:{}".format(hw.get_fpga_version()))

#############################
### PDOC                  ###
#############################

__pdoc__ = {}

#############################
### CONSTANT DECLARATIONS ###
#############################

TEMP_ACCURACY = 1.0
TEMP_PROFILER_INTERVAL = 5
ROOM_TEMPERATURE = 30.0

#################
### Settings  ###
#################

class CTestLibrarySettings:
    """ This class contains the test library configuration.
      """
    def __init__(self):
        self.recipe_name = ""
        self.plot_id = 0
        self.enable_nfs = 0
        self.internal_list_block_list = []
        self.internal_block_list = []
        self.pattern_algo = PATTERN_ALGO.RANDOM
        self.disable_sequence_recording = False

    def set_pattern_algo(self, pattern_algo):
        # pattern algorithm
        if pattern_algo == 'RANDOM':
            self.pattern_algo = PATTERN_ALGO.RANDOM
        if pattern_algo == 'USER':
            self.pattern_algo = PATTERN_ALGO.USER
        if pattern_algo == 'ALL0':
            self.pattern_algo = PATTERN_ALGO.ALL0
        if pattern_algo == 'ALL1':
            self.pattern_algo = PATTERN_ALGO.ALL1
        if pattern_algo == 'RANDOM':
            self.pattern_algo = PATTERN_ALGO.RANDOM
        if pattern_algo == 'ALT_AA_55':
            self.pattern_algo = PATTERN_ALGO.ALT_AA_55
        if pattern_algo == 'ALT_FF_00':
            self.pattern_algo = PATTERN_ALGO.ALT_FF_00
        if pattern_algo == 'INCREMENTAL':
            self.pattern_algo = PATTERN_ALGO.INCREMENTAL
        if pattern_algo == 'RANDOM_0':
            self.pattern_algo = PATTERN_ALGO.RANDOM_0


#################
###  Globals  ###
#################

device = COnfiDevice()
test_library_settings = CTestLibrarySettings()

#################
### Utilities ###
#################

def parse_string_list(part_name, input_str):
    """
    This function parses an input string to extract a list of part (CH, CE, LUN, BLOCKS or PAGES) indexes.

        Parameters:
                part_name (str): The part name we are going to parse
                input_str (str): The input string to parse
                max_index (int): Max allowed index; if different from -1, it is used to validate the input string

        Returns:
                part_list (array): An array with the index of a selected part

        Examples:
                Option A) 10%, 30% ==> [10%, 30%]
    """

    selection = []
    invalid = []
    out_of_range = False

    # tokens are comma separated values
    tokens = [x.strip() for x in input_str.split(',')]
    for i in tokens:
        try:
            # typically tokens are plain old integers
            idx = str(i)
            selection.append(idx)
        
        except Exception:
            # not an int and not a range...
            invalid.append(i)

    # Report invalid tokens before returning valid selection
    if len(invalid) > 0:
        ws.warning("{0} Invalid tokes: {1}".format(part_name, str(invalid)))

    if out_of_range:
        raise Exception("{0}: Index out of Range".format(part_name))

    return selection

def parse_multi_string_list(part_name="STRING", input_str=''):
    """
    This function is used to parse a list of blocks for multi plane operations.
    It extracts a list of block lists from the input string; each element of the list is a list of blocks to test in parallel.

        Parameters:
             part_name (str): The part name we are going to parse (used for BLOCK only)
             input_str (str): The input string to parse
             max_index (int): Max allowed index; if it is different from -1, it is used to validate the input string

        Returns:[[]]
             list_of_part_list (array(array)): An array where each element is an array of parts

        Examples:
            Option A) 0,2-3 ==> (0,2,3)
            Option B) 0-10:4 ==> (0,1,2,3)(4,5,6,7)(8,9,10)
            Option C) [0,1,2,3][4-7][9,10] ==> (0,1,2,3)(4,5,6,7)(9,10)
            Option D) [0-7:4][100-107:4] ==> (0,1,2,3)(4,5,6,7)(100,101,102,103)(104,105,106,107)

     """

    part_list = []
    str = input_str

    while True:
        start = str.find("[")
        end = str.find("]", start)
        if start == -1:
            part_list = parse_string_list(part_name, str)
            break
        if end == -1:
            break

        elements = parse_string_list(part_name, str[start + 1:end])
        part_list.extend(elements)

        start = end + 1
        str = str[start:len(str)]
        if len(str) == 0:
            break

    return part_list

def parse_list(part_name, input_str, max_index=-1):
    """
    This function parses an input string to extract a list of part (CH, CE, LUN, BLOCKS or PAGES) indexes.

        Parameters:
                part_name (str): The part name we are going to parse
                input_str (str): The input string to parse
                max_index (int): Max allowed index; if different from -1, it is used to validate the input string

        Returns:
                part_list (array): An array with the index of a selected part

        Examples:
                Option A) 0,1,6-8 ==> 0,1,6,7,8
                Option B) ALL ==> 0,1,2,...,PART_NUMBER-1

    """

    if input_str == "ALL" and max_index != -1:
        input_str = "0-{0}".format(max_index - 1)

    selection = []
    invalid = []
    out_of_range = False
    # tokens are comma separated values
    tokens = [x.strip() for x in input_str.split(',')]
    for i in tokens:
        try:
            # typically tokens are plain old integers
            idx = int(i)

            if (max_index != -1 and idx >= max_index) or (idx < 0):
                out_of_range = True
                Exception("{0}:{1} Index out of Range{2}".format(part_name, idx, max_index))

            selection.append(idx)
        except Exception:
            # if not, then it might be a range
            try:
                token = [int(k.strip()) for k in i.split('-')]
                if len(token) > 1:
                    token.sort()
                    # we have items separated by a dash
                    # try to build a valid range
                    first = token[0]
                    last = token[len(token) - 1]

                    if (max_index != -1 and last >= max_index) or (first < 0):
                        out_of_range = True
                        Exception("{0}:{1} {2} Index out of Range{3}".format(part_name, first, last, max_index))

                    for x in range(first, last + 1):
                        selection.append(x)
            except Exception:
                # not an int and not a range...
                invalid.append(i)
    # Report invalid tokens before returning valid selection
    if len(invalid) > 0:
        ws.warning("{0} Invalid tokes: {1}".format(part_name, str(invalid)))

    if out_of_range:
        raise Exception("{0}: Index out of Range".format(part_name))

    return selection

def parse_str_token(label, token):
    group_pos = token.find(":")
    if group_pos == -1:
        return [parse_string_list(label, token)]
    else:
        range_pos = token.find("-")
        if range_pos != -1:
            range_start = int(token[0: range_pos])
            range_stop = int(token[range_pos + 1: group_pos])
            group = int(token[group_pos + 1: len(token)])
            sub_part_list = []

            for x in range(range_start, range_stop + 1, group):
                first = x
                last = min(range_stop + 1, x + group)

                if (max_index != -1 and last > max_index) or (first < 0):
                    raise Exception("{0}: Index out of Range".format(label))

                sub_part_list.append(list(range(first, last)))

            return sub_part_list
        

__pdoc__["parse_token"] = False
def parse_token(label, token, max_index=-1):
    group_pos = token.find(":")
    if group_pos == -1:
        return [parse_list(label, token, max_index)]
    else:
        range_pos = token.find("-")
        if range_pos != -1:
            range_start = int(token[0: range_pos])
            range_stop = int(token[range_pos + 1: group_pos])
            group = int(token[group_pos + 1: len(token)])
            sub_part_list = []

            for x in range(range_start, range_stop + 1, group):
                first = x
                last = min(range_stop + 1, x + group)

                if (max_index != -1 and last > max_index) or (first < 0):
                    raise Exception("{0}: Index out of Range".format(label))

                sub_part_list.append(list(range(first, last)))

            return sub_part_list

__pdoc__["parse_wl_list_token"] = False
def parse_wl_list_token(label, token, max_index=-1):
    group_pos = token.find(":")
    if group_pos == -1:
        return [parse_list(label, token, max_index)]
    else:
        range_pos = token.find("-")
        if range_pos != -1:
            range_start = int(token[0: range_pos])
            range_stop = int(token[range_pos + 1: group_pos])
            group = int(token[group_pos + 1: len(token)])
            sub_part_list = []
            new_wl_list = []

            for x in range(range_start, range_stop + 1, group):
                # first = x
                # last = min(range_stop + 1, x + group)

                if (max_index != -1 and x > max_index) or (x < 0):
                    raise Exception("{0}: Index out of Range".format(label))
                
                new_wl_list.append(x)

            sub_part_list.append(new_wl_list)

            return sub_part_list   

__pdoc__["parse_wl_token"] = False
def parse_wl_token(label, token, max_index=-1):
    group_pos = token.find(":")
    if group_pos == -1:
        return parse_list(label, token, max_index)
        # return [parse_list(label, token, max_index)]
    else:
        range_pos = token.find("-")
        if range_pos != -1:
            range_start = int(token[0: range_pos])
            range_stop = int(token[range_pos + 1: group_pos])
            group = int(token[group_pos + 1: len(token)])
            sub_part_list = []

            for x in range(range_start, range_stop + 1, group):
                # first = x
                # last = min(range_stop + 1, x + group)

                if (max_index != -1 and x > max_index) or (x < 0):
                    raise Exception("{0}: Index out of Range".format(label))

                sub_part_list.append(x)

            return sub_part_list


def parse_multi_list(part_name, input_str, max_index=-1):
    """
    This function is used to parse a list of blocks for multi plane operations.
    It extracts a list of block lists from the input string; each element of the list is a list of blocks to test in parallel.

        Parameters:
             part_name (str): The part name we are going to parse (used for BLOCK only)
             input_str (str): The input string to parse
             max_index (int): Max allowed index; if it is different from -1, it is used to validate the input string

        Returns:[[]]
             list_of_part_list (array(array)): An array where each element is an array of parts

        Examples:
            Option A) 0,2-3 ==> (0,2,3)
            Option B) 0-10:4 ==> (0,1,2,3)(4,5,6,7)(8,9,10)
            Option C) [0,1,2,3][4-7][9,10] ==> (0,1,2,3)(4,5,6,7)(9,10)
            Option D) [0-7:4][100-107:4] ==> (0,1,2,3)(4,5,6,7)(100,101,102,103)(104,105,106,107)

     """

    part_list = []

    str = input_str

    while True:
        start = str.find("[")
        end = str.find("]", start)
        if start == -1:
            elements = parse_token(part_name, str, max_index)
            for el in elements:
                part_list.append(el)
            break
        if end == -1:
            break

        elements = parse_token(part_name, str[start + 1:end], max_index)
        for el in elements:
            part_list.append(el)

        start = end + 1
        str = str[start:len(str)]
        if len(str) == 0:
            break

    return part_list


def parse_multi_gap_list(part_name, input_str, max_index=-1):
    """
    This function is used to parse a list of blocks for multi plane operations.
    It extracts a list of block lists from the input string; each element of the list is a list of blocks to test in parallel.

        Parameters:
             part_name (str): The part name we are going to parse (used for BLOCK only)
             input_str (str): The input string to parse
             max_index (int): Max allowed index; if it is different from -1, it is used to validate the input string

        Returns:
             list_of_part_list (array(array)): An array where each element is an array of parts

        Examples:
            Option A) 0,2-3 ==> (0,2,3)
            Option B) 0-10:4 ==> (0,4,8)
            Option C) [0,1,2,3][4-7][9,10] ==> (0,1,2,3)(4,5,6,7)(9,10)
            Option D) [0-7:4][100-107:4] ==> (0,4)(100,104)

     """

    part_list = []

    str = input_str

    Square_brackets_count = str.count("[")

    while True:
        start = str.find("[")
        end = str.find("]", start)
        if start == -1:
            if Square_brackets_count > 1:
                elements = parse_wl_list_token(part_name, str, max_index)   #返回一个含有列表的列表
            else:
                elements = parse_wl_token(part_name, str, max_index)    #返回一个含有数值的列表
            for el in elements:
                part_list.append(el)
            break
        if end == -1:
            break

        if Square_brackets_count > 1:
            elements = parse_wl_list_token(part_name, str[start + 1:end], max_index)
        else:
            elements = parse_wl_token(part_name, str[start + 1:end], max_index)

        for el in elements:
            part_list.append(el)

        start = end + 1
        str = str[start:len(str)]
        if len(str) == 0:
            break

    return part_list


def parse_ch_list(input_str):
    """ This function is used to parse a string as list of channel indexes. See: `TestLibrary.parse_list` """
    return parse_list("CH", input_str, device.CHANNEL_NUM)


def parse_ce_list(input_str):
    """ This function is used to parse a string as list of chip enable (CE) indexes. See: `TestLibrary.parse_list` """
    return parse_list("CE", input_str, device.DEVICE_CE_NUMBER)


def parse_lun_list(input_str):
    """ This function is used to parse a string as list of LUN indexes. See: `TestLibrary.parse_list` """
    return parse_list("LUN", input_str, device.LUN_NUMBER)


def parse_block_list(input_str):
    """ This function is used in single plane operation to parse a string as a list of block indexes. See: `TestLibrary.parse_list`
    If an internal list has been selected with `TestLibrary.select_good_block_list_brick` or `TestLibrary.select_virgin_block_list_brick`, the internal
    list will be used and the input string will be ignored.
    """
    # check if special block list has been enabled
    if len(test_library_settings.internal_block_list) > 0:
        ws.info("SP Internal Block list available, test will be executed here: {0}".format(test_library_settings.internal_block_list))
        return test_library_settings.internal_block_list

    return parse_list("BLOCK", input_str, device.BLOCK_NUMBER)


def parse_multi_block_list(input_str):
    """ This function is used in multi plane operation to parse a string as list of block list. See: `TestLibrary.parse_multi_block_list`
    If an internal list has been selected with `TestLibrary.select_good_block_list_brick` or `TestLibrary.select_virgin_block_list_brick`, the internal
    list will be used and the input string will be ignored.
    """
    # check if special block list has been enabled
    if len(test_library_settings.internal_list_block_list) > 0:
        ws.info("MP Internal Block list available, test will be executed here: {0}".format(test_library_settings.internal_list_block_list))
        return test_library_settings.internal_list_block_list

    return parse_multi_list("BLOCK", input_str, device.BLOCK_NUMBER)

def parse_multi_page_list(input_str):
    """ This function is used in multi plane operation to parse a string as list of block list. See: `TestLibrary.parse_multi_block_list`
    If an internal list has been selected with `TestLibrary.select_good_block_list_brick` or `TestLibrary.select_virgin_block_list_brick`, the internal
    list will be used and the input string will be ignored.
    """
    # check if special page list has been enabled
    if len(test_library_settings.internal_list_block_list) > 0:
        ws.info("MP Internal Page list available, test will be executed here: {0}".format(test_library_settings.internal_list_block_list))
        return test_library_settings.internal_list_block_list

    return parse_multi_list("PAGE", input_str, device.PAGE_NUMBER)

def parse_multi_wl_list(input_str):
    """ This function is used in multi plane operation to parse a string as list of wl list. 
    [0-6:2] -> (0,2,4)
    """
    # check if special wordline list has been enabled
    if len(test_library_settings.internal_list_block_list) > 0:
        ws.info("MP Internal Wordline list available, test will be executed here: {0}".format(test_library_settings.internal_list_block_list))
        return test_library_settings.internal_list_block_list

    return parse_multi_gap_list("WL", input_str, device.WL_NUMBER)

def parse_multi_str_list(input_str):
    """ This function is used to parse a string as list of page indexes. See: `TestLibrary.parse_list` """
    return parse_multi_string_list("STRING", input_str)

def parse_str_list(input_str):
    """ This function is used to parse a string as list of page indexes. See: `TestLibrary.parse_list` """
    return parse_string_list("STRING", input_str)

def parse_page_list(input_str):
    """ This function is used to parse a string as list of page indexes. See: `TestLibrary.parse_list` """
    return parse_list("PAGE", input_str, device.PAGE_NUMBER)

def parse_wl_list(input_str):
    """ This function is used to parse a string as list of page indexes. See: `TestLibrary.parse_list` """
    return parse_list("PAGE", input_str, device.WL_NUMBER)


# #################################################################################################

__pdoc__["make_unique_plot_name"] = False
def make_unique_plot_name(prefix, base_name):
    test_library_settings.plot_id += 1
    pid = "PID{:02d}".format(test_library_settings.plot_id)
    plot_name = "[{0} - {1}] {2}".format(pid, prefix, base_name)
    ws.info("New Plot: {0}".format(plot_name))
    return plot_name


__pdoc__["pmu_setup"] = False
def pmu_setup(pmu_algo, expected_max_time = 10000):
    pmu_ch = enumPmuChannel.Icc

    if PMU_ALGO.Icc1 == pmu_algo or PMU_ALGO.Icc2 == pmu_algo or PMU_ALGO.Icc3 == pmu_algo or PMU_ALGO.Icc4r == pmu_algo or PMU_ALGO.Icc4w == pmu_algo:
        pmu_ch = enumPmuChannel.Icc

    if PMU_ALGO.IccQ1 == pmu_algo or PMU_ALGO.IccQ2 == pmu_algo or PMU_ALGO.IccQ3 == pmu_algo or PMU_ALGO.IccQ4r == pmu_algo or PMU_ALGO.IccQ4w == pmu_algo:
        pmu_ch = enumPmuChannel.Iccq

    if PMU_ALGO.Ipp1 == pmu_algo or PMU_ALGO.Ipp2 == pmu_algo or PMU_ALGO.Ipp3 == pmu_algo or PMU_ALGO.Ipp4r == pmu_algo or PMU_ALGO.Ipp4w == pmu_algo:
        pmu_ch = enumPmuChannel.Ipp

    device.pmu_algo = pmu_algo
    pmu.PMU_SETUP(pmu_ch, expected_max_time)


def init_library(recipe_name):
    """ This is the first method invoked when a recipe is started. It contains the hardware initialization.
    """
    test_library_settings.recipe_name = recipe_name
    ws.info("Starting Test Program: {0}".format(test_library_settings.recipe_name))

    hw.init()
    hw.set_datarate(hw.default_datarate_MTs())
    hw.sequence_recording_enable(False, "")


def terminate_library():
    """ This is the last method invoked when a recipe ends. It contains the hardware termination and the datalog closing.\n
    To ensure a correct termination, this is the right place to close the datalog, turn off the device and temperature regulation as well.
    """

    for uid in device.uid_list:
        data_mgr.store_to_central(uid)

    # to be sure everything is closed (temperature regulation, power supply, datalog ...)
    device.turn_off()
    hw.board_set_temp_led(eBlinkStatusType.BlinkOff)
    hw.temperature_enable(False)
    datalog.close()

    hw.terminate()

    ws.info("Test program terminated")


__pdoc__['count_pages'] = False
def count_pages(ce_list: [], lun_list: [], block_list: [], page_list: []):
    tested_pages = len(device.pages_to_tested_pages_list(page_list))
    return len(ce_list) * len(lun_list) * len(block_list) * tested_pages


__pdoc__['mp_count_pages'] = False
def mp_count_pages(ce_list: [], lun_list: [], list_block_list: [[]], page_list: []):
    tested_pages = len(device.pages_to_tested_pages_list(page_list))
    tested_pages = len(ce_list) * len(lun_list) * tested_pages
    page_count = 0
    for i in range(len(list_block_list)):
        page_count += len(list_block_list[i]) * tested_pages
    return page_count


__pdoc__['print_ber'] = False
def print_ber(ch, ce_list: [] = None, lun_list: [] = None, block_list: [] = None, list_block_list: [[]] = None,
              page_list: [] = None, plot=None, x=0, series=None):

    page_count = 0
    if block_list is not None:
        page_count = count_pages(ce_list, lun_list, block_list, page_list)
    elif list_block_list is not None:
        page_count = mp_count_pages(ce_list, lun_list, list_block_list, page_list)
    else:
        # skip operation if no block are selected
        return 0

    if page_count == 0:
        return 0

    # fails_4_ch = device.get_fails_count(ch)
    # error_rate = 100 * fails_4_ch / (page_count * device.PAGE_LENGTH * 8)
    # ws.info("CH: {0}  - Fails: {1} - Error Rate: {2:.5f}".format(ch, fails_4_ch, error_rate))

    max_fails_4_ch_4_chunk = max(device.get_fails_count_4_chunk(ch, 0), device.get_fails_count_4_chunk(ch, 1), device.get_fails_count_4_chunk(ch, 2), device.get_fails_count_4_chunk(ch, 3))
    error_rate = 100 * max_fails_4_ch_4_chunk / (page_count * device.PAGE_LENGTH * 8)
    ws.info("CH: {0}  - Max Chunk Fails : {1} - Error Rate: {2:.5f}".format(ch, max_fails_4_ch_4_chunk, error_rate))

    if plot is not None:
        plot.add(x, error_rate, series)
        plot.flush()


__pdoc__['print_ber'] = False
def mp_print_ber(ch, ce_list: [] = None, lun_list: [] = None, block_list: [] = None, list_block_list: [[]] = None,
              list_page_list: [[]] = None, plot=None, x=0, series=None):

    page_count = 0
    if block_list is not None:
        for page_list in list_page_list:
            page_count = page_count + count_pages(ce_list, lun_list, block_list, page_list)
    elif list_block_list is not None:
        for page_list in list_page_list:
            page_count = page_count + mp_count_pages(ce_list, lun_list, list_block_list, page_list)
    else:
        # skip operation if no block are selected
        return 0

    if page_count == 0:
        return 0

    # fails_4_ch = device.get_fails_count(ch)
    # error_rate = 100 * fails_4_ch / (page_count * device.PAGE_LENGTH * 8)
    # ws.info("CH: {0}  - Fails: {1} - Error Rate: {2:.5f}".format(ch, fails_4_ch, error_rate))

    max_fails_4_ch_4_chunk = max(device.get_fails_count_4_chunk(ch, 0), device.get_fails_count_4_chunk(ch, 1), device.get_fails_count_4_chunk(ch, 2), device.get_fails_count_4_chunk(ch, 3))
    error_rate = 100 * max_fails_4_ch_4_chunk / (page_count * device.PAGE_LENGTH * 8)
    ws.info("CH: {0}  - Max Chunk Fails : {1} - Error Rate: {2:.5f}".format(ch, max_fails_4_ch_4_chunk, error_rate))

    if plot is not None:
        plot.add(x, error_rate, series)
        plot.flush()


__pdoc__["do_erase"]=False
def do_erase(ch_list: [], ce_list: [], lun_list: [], block_list: [],
             cycle=0, log_enable=True, log_set=None, time_plot=None, current_plot=None, pmu_algo=PMU_ALGO.Icc3):
    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     time_plot=time_plot if log_enable else None,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_ERASE_TIME)

    startT = hw.get_nsec_time()

    for block in block_list:
        device.select_pattern(ch_list, ce_list, lun_list, [block], PATTERN_ALGO.ALL1)
        device.erase_block(ch_list, ce_list, lun_list, block)

    endT = hw.get_nsec_time()
    ws.info("Erase - Elapsed Time {0:.3f} msec - Block List {1}".format((endT - startT) / 1000000, block_list))

    if time_plot is not None:
        time_plot.flush()

__pdoc__["do_multi_plane_erase"]=False
def do_multi_plane_erase(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]],
                         cycle=0, log_enable=True, log_set=None, time_plot=None, current_plot=None,
                         pmu_algo=PMU_ALGO.Icc3):
    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     time_plot=time_plot if log_enable else None,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_ERASE_TIME)

    startT = hw.get_nsec_time()

    for block_list in list_block_list:
        device.select_pattern(ch_list, ce_list, lun_list, block_list, PATTERN_ALGO.ALL1)
        device.multi_plane_erase_block(ch_list, ce_list, lun_list, block_list)

    endT = hw.get_nsec_time()
    ws.info("MP Erase - Elapsed Time {0:.3f} msec - Block List {1}".format((endT - startT) / 1000000, list_block_list[0]))

    if time_plot is not None:
        time_plot.flush()

__pdoc__["do_erase_suspend"]=False
def do_erase_suspend(ch_list: [], ce_list: [], lun_list: [], block, cycle, log_enable, suspend_cmd_delay):
    erase_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=erase_suspend_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    # erase suspend
    device.select_pattern(ch_list, ce_list, lun_list, [block], PATTERN_ALGO.ALL1)
    device.erase_block_suspend(ch_list, ce_list, lun_list, block, suspend_cmd_delay)

__pdoc__["do_erase_resume"]=False
def do_erase_resume(ch_list: [], ce_list: [], lun_list: [], block, cycle, log_enable, suspend_cmd_delay):
    erase_resume_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=erase_resume_log_set, cycle=cycle)
    pmu_setup(PMU_ALGO.NoPmu)

    # erase resume
    device.select_pattern(ch_list, ce_list, lun_list, [block], PATTERN_ALGO.ALL1)
    device.erase_block_resume(ch_list, ce_list, lun_list, block, suspend_cmd_delay)


__pdoc__["do_multi_plane_erase_suspend"]=False
def do_multi_plane_erase_suspend(ch_list: [], ce_list: [], lun_list: [], block_list: [], cycle, log_enable,
                                 suspend_cmd_delay):
    erase_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=erase_suspend_log_set, cycle=cycle)
    pmu_setup(PMU_ALGO.NoPmu)

    # erase suspend
    device.select_pattern(ch_list, ce_list, lun_list, block_list, PATTERN_ALGO.ALL1)
    device.multi_plane_erase_block_suspend(ch_list, ce_list, lun_list, block_list, suspend_cmd_delay)

__pdoc__["do_multi_plane_erase_resume"]=False
def do_multi_plane_erase_resume(ch_list: [], ce_list: [], lun_list: [], block_list: [], cycle, log_enable,
                                suspend_cmd_delay):
    erase_resume_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=erase_resume_log_set, cycle=cycle)
    pmu_setup(PMU_ALGO.NoPmu)

    # erase resume
    device.select_pattern(ch_list, ce_list, lun_list, block_list, PATTERN_ALGO.ALL1)
    device.multi_plane_erase_block_resume(ch_list, ce_list, lun_list, block_list, suspend_cmd_delay)


__pdoc__["do_program_page_suspend"]=False
def do_program_page_suspend(ch_list: [], ce_list: [], lun_list: [], block, page, cycle, log_enable, suspend_cmd_delay):
    prog_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=prog_suspend_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    # program suspend
    page_suspended = device.program_page_suspend(ch_list, ce_list, lun_list, block, page, suspend_cmd_delay)

    return page_suspended


__pdoc__["do_program_resume"]=False
def do_program_resume(ch_list: [], ce_list: [], lun_list: [], block, page, cycle, log_enable, suspend_cmd_delay):
    prog_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=prog_suspend_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    # program resume
    device.program_page_resume(ch_list, ce_list, lun_list, block, page, suspend_cmd_delay)


__pdoc__["do_multi_plane_program_page_suspend"]=False
def do_multi_plane_program_page_suspend(ch_list: [], ce_list: [], lun_list: [], block_list: [], page, cycle, log_enable,
                                        suspend_cmd_delay):
    prog_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=prog_suspend_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    # program suspend
    page_suspended = device.multi_plane_program_page_suspend(ch_list, ce_list, lun_list, block_list, page,
                                                             suspend_cmd_delay)

    return page_suspended


__pdoc__["do_multi_plane_program_resume"]=False
def do_multi_plane_program_resume(ch_list: [], ce_list: [], lun_list: [], block_list: [], page, cycle, log_enable,
                                  suspend_cmd_delay):
    prog_suspend_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}
    device.init_test(log_enable=log_enable, log_set=prog_suspend_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    # program resume
    device.multi_plane_program_page_resume(ch_list, ce_list, lun_list, block_list, page, suspend_cmd_delay)


__pdoc__["do_program"]=False
def do_program(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [],
               cycle=0, log_enable=True, log_set=None,
               pattern_algo=test_library_settings.pattern_algo, current_plot=None, pmu_algo=PMU_ALGO.Icc2):
    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_PROG_TIME)

    startT = hw.get_nsec_time()

    for block in block_list:
        device.select_pattern(ch_list, ce_list, lun_list, [block], pattern_algo)
        device.program_block(ch_list, ce_list, lun_list, block, page_list)

    endT = hw.get_nsec_time()
    ws.info("Program - Elapsed Time {0:.3f} msec".format((endT - startT) / 1000000))


__pdoc__["do_multi_plane_program"]=False
def do_multi_plane_program(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], page_list: [],
                           cycle=0, log_enable=True, log_set=None,
                           pattern_algo=test_library_settings.pattern_algo, current_plot=None, pmu_algo=PMU_ALGO.Icc2, close_partial_block=False, qlc_program_order=None):

    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_PROG_TIME)

    startT = hw.get_nsec_time()

    for block_list in list_block_list:
        if close_partial_block==False:
            device.select_pattern(ch_list, ce_list, lun_list, block_list, pattern_algo)
        if qlc_program_order == "Sawtooth":
            device.multi_plane_sawtooth_program_block(ch_list, ce_list, lun_list, block_list, page_list)
        else:
            device.multi_plane_program_block(ch_list, ce_list, lun_list, block_list, page_list)

    endT = hw.get_nsec_time()
    ws.info("MP Program - Elapsed Time {0:.3f} msec - Block List {1}".format((endT - startT) / 1000000, list_block_list[0]))


__pdoc__["do_read_out"]=False
def do_read_out(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [],
                cycle=0, log_enable=True, log_set=None, current_plot=None, ber_plot=None, pmu_algo=PMU_ALGO.Icc1):
    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_READ_TIME)

    startT = hw.get_nsec_time()

    for ce_index in ce_list:
        hw.select_ce(ce_index)
        for lun in lun_list:
            for block in block_list:
                for page in page_list:
                    device.page_compare(ch_list, ce_index, lun, block, page, 0, device.PAGE_LENGTH)

    endT = hw.get_nsec_time()

    if log_enable:
        ws.info("Read Out - Elapsed Time {0:.3f} msec".format((endT - startT) / 1000000))

        for ch in ch_list:
            series = "CH{0}-BER".format(ch)
            print_ber(ch, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                      plot=ber_plot, x=cycle, series=series)


__pdoc__["do_read_out"]=False
def do_page_read_out(ch_list: [], ce_list: [], lun_list: [], block_list: [], page=0,
                cycle=0, log_enable=True, log_set=None, current_plot=None, ber_plot=None, pmu_algo=PMU_ALGO.Icc1):
    device.init_test(log_enable=log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_READ_TIME)

    startT = hw.get_nsec_time()

    for block in range(550):
        block = block * 4
        for ce_index in ce_list:
            hw.select_ce(ce_index)
            for lun in lun_list:
                for num in range(4):
                    if (not data_mgr.is_bad_block(0, ce_index, lun, (block+num))) and (not data_mgr.is_bad_block(1, ce_index, lun, (block+num))): 
                        device.page_compare(ch_list, ce_index, lun, block+num, page, 0, device.PAGE_LENGTH)
                        device.page_compare(ch_list, ce_index, lun, block+num, (page+2), 0, device.PAGE_LENGTH)
                    elif (data_mgr.is_bad_block(0, ce_index, lun, (block+num))) and (not data_mgr.is_bad_block(1, ce_index, lun, (block+num))): 
                        device.page_compare([1], ce_index, lun, block+num, page, 0, device.PAGE_LENGTH)
                        device.page_compare([1], ce_index, lun, block+num, (page+2), 0, device.PAGE_LENGTH)
                    elif (not data_mgr.is_bad_block(0, ce_index, lun, (block+num))) and (data_mgr.is_bad_block(1, ce_index, lun, (block+num))): 
                        device.page_compare([0], ce_index, lun, block+num, page, 0, device.PAGE_LENGTH)
                        device.page_compare([0], ce_index, lun, block+num, (page+2), 0, device.PAGE_LENGTH)

        time.sleep(1)

    endT = hw.get_nsec_time()

    if log_enable:
        ws.info("Page {0} and {1} Read Out - Elapsed Time {2:.3f} msec".format(page, page+2, (endT - startT) / 1000000))


__pdoc__["do_fast_read"]=False
def do_fast_read(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [],
                 cycle=0, log_enable=True, log_set=None, pmu_algo=PMU_ALGO.Icc1):

    device.init_test(log_enable, log_set=log_set, cycle=cycle)

    pmu_setup(pmu_algo, device.MAX_READ_TIME)

    startT = hw.get_nsec_time()

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for page in page_list:
                    device.page_fast_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)

    endT = hw.get_nsec_time()

    if log_enable:

        ws.info("Fast Read Out - Elapsed Time {0:.3f} msec".format((endT - startT) / 1000000))

        for ch in ch_list:
            print_ber(ch, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list)


__pdoc__["do_multi_plane_read_disturb"]=False
def do_multi_plane_read_disturb(segment, rd_type, ch_list: [], ce_list: [], lun_list: [[]], list_block_list: [], o_str_retry, retry_blocks_list:[], rd_page_list: [], read_page_list: [],
                            pp_list, cycles=0, log_cycle=0, base_cycle=0, log_enable=True, log_set=None, file_options=None, options=None, current_plot=None, ber_plot=None,
                            pmu_algo=PMU_ALGO.Icc1):
    device.init_test(log_enable, log_set=log_set, cycle=cycles,
                     current_plot=current_plot if log_enable else None)

    for ce in ce_list:
        for lun in lun_list:
            ### ******************************************************************* Block Read Disturb *******************************************************************
            if 'BLKRD' in rd_type:
                ws.info("***  Block-RD Info: Block {0} - Total RD Count {1} - Log_Cycle {2}  ***".format(list_block_list, cycles, log_cycle))

                startT = hw.get_nsec_time()
                for cycle in range(base_cycle, cycles):
                    for block_list in list_block_list:
                        for page in rd_page_list:
                            device.multi_plane_block_read_disturb(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)

                    # rd_log_enable =  True if (log_cycle > 0 and ((cycle+1) % log_cycle == 0 or (cycle+1) == cycles)) else False

                    if (log_cycle > 0 and ((cycle+1) % log_cycle == 0 or (cycle+1) == cycles)):
                        rd_log_enable = True 
                    else:
                        rd_log_enable = False

                    ws.info('read disturb_cycle: {}'.format((cycle+1)))
                                        
                    if rd_log_enable:
                        endT = hw.get_nsec_time()
                        elapsed = (endT - startT) / 1000000000
                        ws.info("Done: {0:,} Block RD - on Block {1} - Elapsed Time {2:.3f} sec".format((cycle + 1), list_block_list, elapsed))

                        ws.info("***  Default Read for {0:,} BlkRD - on Block {1}  ***".format((cycle + 1), list_block_list))
                        datalog.set_segment(0, str(segment.name()) + '_Default_BlkRD_' + str(cycle + 1))
                        datalog.set_label("Default Read")

                        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                            page_list=read_page_list, cycle=(cycle+1), log_enable=True, log_set=read_log_set, pmu_algo=read_pmu_algo)
                                               
                        ws.info("***  Read Retry for {0:,} BlkRD - on Block {1}  ***".format((cycle + 1), list_block_list))
                        datalog.set_segment(0, str(segment.name()) + '_Retry_BlkRD_' + str(cycle + 1))
                        datalog.set_label("Read Retry")
                        if device.DEVICE_NAME == "X29060" and 'Partial' in test_library_settings.recipe_name:
                            read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list[:-18], o_str_retry, retry_blocks_list = retry_blocks_list, file = 'YMTC_9060_InnerWL_RR_Table.csv', options = '0-28')
                            read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list[-18:], o_str_retry, retry_blocks_list = retry_blocks_list, file = 'YMTC_9060_EdgeWL_RR_Table.csv', options = '0-27')
                        elif (device.DEVICE_NAME == "X39070" or device.DEVICE_NAME == "X49060") and ('Partial' in test_library_settings.recipe_name or 'OpenBlk' in test_library_settings.recipe_name):
                            open_read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, pp_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)
                        elif (device.DEVICE_NAME == "X36070"):
                            read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)
                        else:
                            read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)
                        startT = hw.get_nsec_time()

            ### ******************************************************************* Single Page Read Disturb *******************************************************************

            elif 'SPRD' in rd_type:
                ws.info("***  SPRD Info: Block {0} - Total RD Count {1} - Log_Cycle {2}  ***".format(list_block_list, cycles, log_cycle))
                
                cyc_loop = 0
                for cycle in range(0, int((cycles-base_cycle)/log_cycle)):
                    for page in rd_page_list:
                        ws.info("SPRD on Page: {0}".format(page))
                        device.multi_plane_single_page_read_disturb(ch_list, ce, lun, list_block_list, page, 0, device.PAGE_LENGTH, log_cycle, cyc_loop)
                    cyc_loop = cyc_loop + 1

                    ws.info("***  Default Read for {0:,} SPRD - Block {1}  ***".format((base_cycle+log_cycle*cyc_loop), list_block_list))
                    datalog.set_segment(0, str(segment.name()) + '_Default_SPRD_' + str((base_cycle+log_cycle*cyc_loop)))
                    datalog.set_label("Default Read")

                    do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                        page_list=read_page_list, cycle=(log_cycle*cyc_loop), log_enable=True, log_set=read_log_set, pmu_algo=read_pmu_algo)
                    
                    ws.info("***  Read Retry for {0:,} SPRD - Block {1}  ***".format((base_cycle+log_cycle*cyc_loop), list_block_list))
                    datalog.set_segment(0, str(segment.name()) + '_Retry_SPRD_' + str((base_cycle+log_cycle*cyc_loop)))
                    datalog.set_label("Read Retry")
                    if device.DEVICE_NAME == "X29060" and 'Partial' in test_library_settings.recipe_name:
                        read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list[:-18], o_str_retry, retry_blocks_list = retry_blocks_list, file = 'YMTC_9060_InnerWL_RR_Table.csv', options = '0-28')
                        read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list[-18:], o_str_retry, retry_blocks_list = retry_blocks_list, file = 'YMTC_9060_EdgeWL_RR_Table.csv', options = '0-27')
                    elif device.DEVICE_NAME == "X39070" and ('Partial' in test_library_settings.recipe_name or 'OpenBlk' in test_library_settings.recipe_name):
                        open_read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)
                    else:
                        read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)
                    # read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list = retry_blocks_list, file = file_options, options = options)


__pdoc__["do_multi_plane_read_out"]=False
def do_multi_plane_read_out(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], page_list: [],
                            cycle=0, log_enable=True, log_set=None, current_plot=None, ber_plot=None,
                            pmu_algo=PMU_ALGO.Icc1):
    device.init_test(log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_READ_TIME)

    startT = hw.get_nsec_time()

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block_list in list_block_list:
                for page in page_list:
                    device.multi_plane_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)

    endT = hw.get_nsec_time()

    if log_enable:
        ws.info("MP Read Out - Elapsed Time {0:.3f} msec - Block List {1}".format((endT - startT) / 1000000, list_block_list[0]))

        for ch in ch_list:
            series = "CH{0}-BER".format(ch)
            print_ber(ch, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list, page_list=page_list,
                      plot=ber_plot, x=cycle, series=series)


__pdoc__["do_multi_plane_openblk_read_out"]=False
def do_multi_plane_openblk_read_out(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], list_page_list: [[]],
                            cycle=0, log_enable=True, log_set=None, current_plot=None, ber_plot=None,
                            pmu_algo=PMU_ALGO.Icc1):
    device.init_test(log_enable, log_set=log_set, cycle=cycle,
                     current_plot=current_plot if log_enable else None)

    pmu_setup(pmu_algo, device.MAX_READ_TIME)

    startT = hw.get_nsec_time()

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block_index in range(len(list_block_list)):  #hang, 两个channel的block是一样的，所以要用index来区分
                original_block_list = list_block_list[block_index]
                if device.DEVICE_NAME == "X36070":  #hang, add for X36070
                        
                    if len(list_page_list) == 1:
                        page_list = list_page_list[0]
                    else:
                        page_list = list_page_list[block_index]

                    ch_used = []
                    if len(list_block_list) <5:
                        ch_used = ch_list
                    else:   #默认block_list>5时， 不同channel的block不一样
                        ch_used.append(ch_list[block_index//4]) # 获取block_list所属的ch，并存在ch_used[0]

                    block_wl_max = device.page_info(page_list[-1])[-1]
                    
                    # hang, 对于X36070，需要将block_list进行处理，以前的list_block_list对应的是一个partial block的不同PEC block，
                    # 现在的list_block_list对应的是不同partial block的PEC=1 block，因此需要将PEC=1/2/3/4/5K的block添加进来
                    # 后续若有其他PEC block，需要修改这里                    
                    for i in range(6): #6是PEC cycle的数量
                        block_list = []
                        if original_block_list[0] == 4:
                            for block in original_block_list:
                                if i < 3:
                                    block_list.append(block + i*8)
                                elif i < 5:
                                    block_list.append(block + i*8 + 16)
                                else:
                                    block_list.append(block + i*8 + 20)
                        else:
                            for block in original_block_list:
                                if i < 5:
                                    block_list.append(block + i*8)
                                else:
                                    block_list.append(block + i*8 + 4)
                            # block_list.append(block + i*8)
                        
                        ws.info("MP Read Out start on CH: {0}, Block List: {1}, Page List: {2}~{3}".format(ch_used, block_list, page_list[0], page_list[-1]))

                        for page in page_list:
                            _, _, cur_levels, cur_wl = device.page_info(page)
                            if cur_levels == 4 and cur_wl > block_wl_max-device.STRING_NUMBER:
                                continue
                            device.multi_plane_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                else:
                    for page in list_page_list[block_index]:
                        device.multi_plane_page_compare(ch_list, ce, lun, original_block_list, page, 0, device.PAGE_LENGTH)

    endT = hw.get_nsec_time()

    if log_enable:
        ws.info("MP Read Out - Elapsed Time {0:.3f} msec - Block List {1}".format((endT - startT) / 1000000, list_block_list[0]))

        for ch in ch_list:
            series = "CH{0}-BER".format(ch)
            mp_print_ber(ch, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list, list_page_list=list_page_list,
                      plot=ber_plot, x=cycle, series=series)


# use this code for result per pages
__pdoc__["do_read_offset_ber"]=False
def do_read_offset_ber(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0,
                       chart_enable):

    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK} # Eric

    device.init_test(log_enable=False, log_set=read_log_set)

    plots = [None, None]
    device.force_all1 = force_all1
    device.force_all0 = force_all0

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for page in page_list:

                    if chart_enable:
                        for ch in ch_list:
                            title = make_unique_plot_name("READ OFFSET BER", "Ce{0} L{1} B{2} P{3}".format(ce, lun, block, page))
                            vt_plot = nanocycler.plot()
                            vt_plot.openext(title, "Offset", enumScaleType.Numerical, "0.00", False,
                                            "BER", enumScaleType.Numerical, "0.00", False, "Level")
                            plots[ch] = vt_plot

                    for level in device.get_valid_level_for_page(page):
                        series = device.level_name(level)

                        fail_list_4_ch = [[], []]

                        condition = "L{:02d}".format(level)
                        device.set_condition(condition)

                        for vt in range(start_dac, end_dac+1, step_dac):

                            # reset fail counter and disable log
                            device.reset_fails_count()

                            res, x_pos = device.set_read_offset_code(ch_list, ce, lun, level, vt)
                            if not res:
                                continue

                            device.read_offset_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)

                            for ch in ch_list:
                                fails_bit = device.get_fails_count(ch)
                                fail_list_4_ch[ch].append(fails_bit)

                                error_rate = 100 * fails_bit / (device.PAGE_LENGTH * 8)
                                if plots[ch] is not None:
                                    plots[ch].add(x_pos, error_rate, series)


                            device.reset_read_offset_code(ch_list, ce, lun, level)

                            # condition = "L{:02d}_VT{:02x}".format(level, vt)
                            # device.logger.log_vt_fails(condition, ce, lun, block, page, fails_bit)

                        # log as array, one fails bit for each vt value
                        condition = "L{:02d}".format(level)
                        for ch in ch_list:
                            logger.log_fails(condition, ch, ce, [lun], [block], [page], fail_list_4_ch[ch])

                    for ch in ch_list:
                        if plots[ch] is not None:
                            plots[ch].flush()
                            plots[ch].close()
                            plots[ch].cleanup()

    device.force_all1 = False
    device.force_all0 = False



# use this code for result per pages
__pdoc__["do_read_offset_in_command_ber"]=False
def do_read_offset_in_command_ber(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0,
                       chart_enable):

    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK} # Eric

    device.init_test(log_enable=False, log_set=read_log_set)

    plots = [None, None]
    device.force_all1 = force_all1
    device.force_all0 = force_all0

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for page in page_list:

                    if chart_enable:
                        for ch in ch_list:
                            title = make_unique_plot_name("READ OFFSET ROIC", "Ce{0} L{1} B{2} P{3}".format(ce, lun, block, page))
                            vt_plot = nanocycler.plot()
                            vt_plot.openext(title, "Offset", enumScaleType.Numerical, "0.00", False,
                                            "BER", enumScaleType.Numerical, "0.00", False, "Level")
                            plots[ch] = vt_plot

                    for level in device.get_valid_level_for_page(page):
                        series = device.level_name(level)

                        fail_list_4_ch = [[], []]

                        condition = "L{:02d}".format(level)
                        device.set_condition(condition)

                        for vt in range(start_dac, end_dac+1, step_dac):

                            # reset fail counter and disable log
                            device.reset_fails_count()

                            offset = device.offset_to_code(vt)
                            device.read_offset_roic_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH, level, offset)

                            for ch in ch_list:
                                fails_bit = device.get_fails_count(ch)
                                fail_list_4_ch[ch].append(fails_bit)

                                error_rate = 100 * fails_bit / (device.PAGE_LENGTH * 8)
                                if plots[ch] is not None:
                                    plots[ch].add(vt, error_rate, series)

                            # condition = "L{:02d}_VT{:02x}".format(level, vt)
                            # device.logger.log_vt_fails(condition, ce, lun, block, page, fails_bit)

                        # log as array, one fails bit for each vt value
                        condition = "L{:02d}".format(level)
                        for ch in ch_list:
                            logger.log_fails(condition, ch, ce, [lun], [block], [page], fail_list_4_ch[ch])

                    for ch in ch_list:
                        if plots[ch] is not None:
                            plots[ch].flush()
                            plots[ch].close()
                            plots[ch].cleanup()

    device.force_all1 = False
    device.force_all0 = False

__pdoc__["do_read_offset_best"]=False
def do_read_offset_best(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac):

    read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}
    # read_log_set = {LOG_SET_ITEM.FAILS}

    device.init_test(log_enable=False, log_set=read_log_set)

    device.force_all1 = force_all1
    device.force_all0 = force_all0

    max_fails = device.PAGE_LENGTH * 8

    ber_fails_4_ch = [0, 0]
    pages = 0

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for page in page_list:

                    best_offset4level_dict = {0: {}, 1: {}}
                    
                    if device.DEVICE_NAME == "X36070":  #hang, add for X36070
                        _,_,_,block_wl_max = device.page_info(page_list[-1])
                        _, _, cur_levels, cur_wl = device.page_info(page)
                        if cur_levels == 4 and cur_wl > block_wl_max-device.STRING_NUMBER:
                            continue
                        for ch in ch_list:
                            for level in range(7 if device.page_info(page)[2] == 3 else (15 if device.page_info(page)[2] == 4 else 1)): #hang, modify for X36070
                                best_offset4level_dict[ch].update({level: 0})
                    else:
                        for ch in ch_list:
                            for level in range(7):
                                best_offset4level_dict[ch].update({level: 0})

                    fail_list_4_ch_best = [[max_fails for i in range(device.CHUNK_NUMBER)],
                                           [max_fails for i in range(device.CHUNK_NUMBER)]]

                    for level in device.get_valid_level_for_page(page):

                        best_dac_4_ch = [0, 0]
                        fails_4_ch = [max_fails, max_fails]
                        fail_list_4_ch = [[],[]]
                        fail_list_4_ch_best = [[max_fails for i in range(device.CHUNK_NUMBER)],
                                               [max_fails for i in range(device.CHUNK_NUMBER)]]

                        for vt in range(start_dac, end_dac + 1, step_dac):

                            for ch in ch_list:
                                best_offset4level_dict[ch].update({level: vt})
                                # device.set_read_offset_code_multi_level(ch, ce, lun, page%3, best_offset4level_dict[ch])
                                device.set_read_offset_code_multi_level(ch, ce, lun, page, best_offset4level_dict[ch])    #hang, add for X36070

                            # reset fail counter
                            device.reset_fails_count()
                            device.read_offset_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)

                            for ch in ch_list:
                                best = False
                                fails_bit = device.get_fails_count(ch)  #16KB
                                if fails_bit <= fails_4_ch[ch]:
                                    fails_4_ch[ch] = fails_bit
                                    best_dac_4_ch[ch] = vt
                                    best = True

                                for chunk in range(device.CHUNK_NUMBER):
                                    fails = device.get_fails_count_4_chunk(ch, chunk)   #4KB
                                    fail_list_4_ch[ch].append(fails)
                                    if best:
                                        fail_list_4_ch_best[ch][chunk] = fails  #每个state的best dac对应的fail count

                        # store best offset
                        for ch in ch_list:
                            # ws.info("CH: {0} - Page: {1} - Level: {2} - Best DAC: {3}".format(ch, page, level, best_dac_4_ch[ch]))
                            best_offset4level_dict[ch].update({level: best_dac_4_ch[ch]})

                        if log_fails:
                            condition = "L{:02d}".format(level)
                            for ch in ch_list:
                                logger.log_fails(condition, ch, ce, [lun], [block], [page], fail_list_4_ch[ch])
                                #logger.log_fails("BEST_FBC_L{:02d}".format(level), ch, ce, [lun], [block], [page], fail_list_4_ch_best[ch])

                    pages += 1
                    for ch in ch_list:
                        ber_fails_4_ch[ch] += sum(fail_list_4_ch_best[ch])
                        logger.log_fails("BEST_FBC", ch, ce, [lun], [block], [page], fail_list_4_ch_best[ch])   #4 chunk FBC，其实只打印了最后一个state的fail count

                    for level in device.get_valid_level_for_page(page):
                        device.reset_read_offset_code(ch_list, ce, lun, level)

                    if log_dac:
                        for ch in ch_list:
                            dacs = []
                            for level in device.get_valid_level_for_page(page):
                                dacs.append(best_offset4level_dict[ch][level])
                            logger.log_fails("BEST_DAC", ch, ce, [lun], [block], [page], dacs)

    for ch in ch_list:
        error_rate = 100 * ber_fails_4_ch[ch] / (pages * device.PAGE_LENGTH * 8)
        ws.info("CH: {0}  - Fails: {1} - Best Error Rate: {2:.5f}".format(ch, ber_fails_4_ch[ch], error_rate))

    device.force_all1 = False
    device.force_all0 = False


#######do_read_offset_fitting_best ----- add C/C++ for high speed
__pdoc__["do_read_offset_fitting_best"]=False
def do_read_offset_fitting_best(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac):
    import gc
    import array

    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}
    # read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}
    device.init_test(log_enable=True, log_set=read_log_set)

    device.force_all1 = force_all1
    device.force_all0 = force_all0

    ber_fails_4_ch = [0, 0]
    pages = 0

    class page_type:
        LP = 0
        MP = 1
        UP = 2
        XP = 3

    operation_array = {
        "3444":{0:{1:["","","~"], 7:["","~","~"], 13:["","~",""]},
                1:{2:["~","","~"], 6:["~","~","~"],  8:["","~","~"], 12:["","~",""]}, 
                2:{4:["~","~",""], 9:["","~","~"], 11:["","~",""], 14:["~","",""]}, 
                3:{0:["","",""], 3:["~","~",""],  5:["~","~","~"], 10:["","~",""]}},

        "133": {0:{3:["","~",""]},
                1:{0:["","",""], 2:["","~",""],  5:["~","",""]}, 
                2:{1:["","~",""], 4:["~","",""], 6:["~","~",""]}},

        "232": {0:{0:["","",""], 4:["","~",""]},
                1:{1:["~","",""], 3:["~","~",""],  5:["","~",""]}, 
                2:{2:["~","~",""], 6:["","~",""]}}        
    }

    pattern_array = {
        "3444":{0:[page_type.MP, page_type.UP, page_type.XP],
                1:[page_type.LP, page_type.UP, page_type.XP],
                2:[page_type.LP, page_type.MP, page_type.XP],
                3:[page_type.LP, page_type.MP, page_type.UP]}, 
        "133": {0:[page_type.MP, page_type.UP],
                1:[page_type.LP, page_type.UP],
                2:[page_type.LP, page_type.MP]}, 
        "232": {0:[page_type.MP, page_type.UP],
                1:[page_type.LP, page_type.UP],
                2:[page_type.LP, page_type.MP]}, 
    }

    # 计算需要的最大值
    max_ch = 2
    max_planes = device.PLANE_NUMBER

    def get_program_pattern(ch, ce, lun, block, page):
        buffer = bytearray(device.PAGE_LENGTH)
        program_pattern, program_seed = device.expected_pattern_fpga_codes(ch, ce, lun, block, page) #get the pattern for LP/MP/UP/XP 
        if program_pattern == ePatternType.Random:
            res, buffer = hw.random_fill_expected_buffer(program_seed, 0, device.PAGE_LENGTH)            
        return buffer

    ##################################################### Start ##########################################################
    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for page in page_list:
                ws.info("***********Start page: {0}**************".format(page))
                ############################### 获取相关当前page的相关信息 ###############################
                ### 获取当前page的相关信息
                if device.DEVICE_NAME == "X36070":
                    _,_,_,block_wl_max = device.page_info(page_list[-1])
                    _, cur_page_level, cur_level_number, cur_wl = device.page_info(page)
                    max_state_level = 7 if cur_level_number == 3 else (15 if cur_level_number == 4 else 1)
                    if cur_level_number == 4 and cur_wl > block_wl_max-device.STRING_NUMBER:
                        continue
                else:
                    cur_page_level = page % device.LEVEL_NUMBER
                    cur_level_number = device.LEVEL_NUMBER
                    max_state_level = 7

                ### 获取encoding map
                if device.DEVICE_NAME == "X36070" and cur_level_number == 4:  
                    encoding_map = "3444"
                elif device.DEVICE_NAME == "BiCS5" and cur_level_number == 3:
                    encoding_map = "133"
                elif cur_level_number == 3:
                    encoding_map = "232"
                else:
                    encoding_map = "SLC"

                ### 获取当前page中的state
                states_in_cur_page = device.get_valid_level_for_page(page)

                ############################### 相关字典或列表的创建 ###############################
                ### 创建每个state offset列表
                state_offset_dict = [0] * max_state_level    # 直接预分配所需大小

                ### 创建fail count字典
                fail_list_4_ch = {}
                for ch in ch_list:
                    fail_list_4_ch[ch] = {}
                    for block in block_list:
                        fail_list_4_ch[ch][block] = {}
                        for state_level in range(max_state_level):
                            fail_list_4_ch[ch][block][state_level] = {}

                ### 创建best offset字典
                if cur_page_level == 0: #only init state offset when read LP page
                    best_state_offset_dict = {}
                    for ch in ch_list:
                        best_state_offset_dict[ch] = {}
                        for block in block_list:
                            best_state_offset_dict[ch][block] = [0] * max_state_level

                ### 使用列表存储program pattern，用于计算state FBC
                if cur_page_level == 0:
                    program_pattern_size = max_ch * max_planes * cur_level_number
                    program_pattern_list = [None] * program_pattern_size
                    for ch in ch_list:
                        for block in block_list: 
                            plane = block % device.PLANE_NUMBER
                            for page_level in range(cur_level_number):
                                program_pattern_index = ch * max_planes * cur_level_number + plane * cur_level_number + page_level
                                program_pattern_list[program_pattern_index] = get_program_pattern(ch, ce, lun, block, page+page_level)

                ### 计算state cell pattern, 一个WL只需计算一次
                if cur_page_level == 0:
                    if cur_level_number != 1:
                        ### 创建state_cell_pattern列表, state
                        pre_cal_pattern_size = max_ch * max_planes
                        state_cell_pattern = [[None] * pre_cal_pattern_size for _ in range(max_state_level)]   

                        ### 根据不同的编码方式，计算state cell pattern
                        for ch in ch_list:
                            for block in block_list:
                                plane = block % device.PLANE_NUMBER
                                state_cell_pattern_index = ch * max_planes + plane

                                ### 保存当前WL的各个page的program pattern
                                cur_wl_program_pattern_list = [None] * cur_level_number
                                for i in range(cur_level_number):
                                    cur_wl_program_pattern_list[i] = program_pattern_list[ch * max_planes * cur_level_number + plane * cur_level_number + i]
                                    
                                ### 获取不同page下的各个state对应的pattern和operation,用来计算state_cell_pattern
                                for level in range(cur_level_number):
                                    pattern1 = cur_wl_program_pattern_list[pattern_array[encoding_map][level][0]]
                                    pattern2 = cur_wl_program_pattern_list[pattern_array[encoding_map][level][1]]
                                    if cur_level_number == 4:
                                        pattern3 = cur_wl_program_pattern_list[pattern_array[encoding_map][level][2]]
                                    else:
                                        pattern3 = None
                                    
                                    for state in device.get_valid_level_for_page(page+level):                                    
                                        operation1 = operation_array[encoding_map][level][state][0]
                                        operation2 = operation_array[encoding_map][level][state][1]
                                        operation3 = operation_array[encoding_map][level][state][2]
                                        temp_pattern = bytearray(device.PAGE_LENGTH)

                                        cal_pattern.and_cal_state_cell_pattern(pattern1,operation1, pattern2,operation2,pattern3,operation3,temp_pattern,device.PAGE_LENGTH)
                                        
                                        state_cell_pattern[state][state_cell_pattern_index] = temp_pattern
                
                ############################### 对当前page进行扫描 ###############################
                for vt in range(start_dac, end_dac + 1, step_dac):

                    ##### 获取offset=vt时的read_pattern
                    start_time = hw.get_nsec_time()

                    for ch in ch_list:
                        for i in range(max_state_level):
                            state_offset_dict[i] = vt
                        device.set_read_offset_code_multi_level(ch, ce, lun, page, state_offset_dict)   #这里的offset是实际使用的十进制，如-20~20 DAC
                    # ws.info("update state offset: {}".format(state_offset_dict))  

                    device.reset_fails_count()
                    read_pattern_dict = device.multi_plane_page_read_pattern(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)     #read without log FBC, return read pattern

                    end_time = hw.get_nsec_time()
                    ws.info("set feature and read pattern elapsed time: {0:.2f}ms".format((end_time - start_time)/1000000))

                    ##### 对每个block计算state fbc
                    for ch in ch_list:
                        for block in block_list:
                            ### 获取porgram pattern 和 read pattern
                            plane = block % device.PLANE_NUMBER
                            program_pattern_index = ch * max_planes * cur_level_number + plane * cur_level_number + cur_page_level
                            program_pattern = program_pattern_list[program_pattern_index]                            
                            read_pattern = read_pattern_dict[ch][block]

                            ### 计算FBC pattern 和 FBC
                            start_time = hw.get_nsec_time()

                            # Python 的 int 和 list 不是 C 的可写 buffer！
                            # Python 的 int 是不可变对象，不能直接传递其地址给 C 让其写入。
                            # Python 的 list 不是连续的内存块，不能直接传递给 C 让其当作 int* 或 size_t* 写入。
                            # 用 array.array('Q', [0])（Q=8字节无符号整数，适合C端的size_t），使用page_fbc_16KB[0]，list(page_fbc_4KB_list)获取
                            # 或 bytearray(8)，但是C端写入 size_t*，Python 端看到的是原始字节，如b'B\x00\x00\x00\x00\x00\x00\x00'，还需转换为整数66，用 struct.unpack('<Q', page_fbc_16KB)[0] 得到整数。
                            page_FBC_pattern = bytearray(device.PAGE_LENGTH)
                            page_FBC_16KB = array.array('Q', [0])    
                            page_FBC_4KB_list = array.array('Q', [0, 0, 0, 0])  # 'Q' = unsigned long long (8字节), 输出结果如：array('Q', [21, 14, 19, 12]）, 

                            cal_pattern.xor_cal_page_fbc_pattern_and_fbc(program_pattern, read_pattern, page_FBC_pattern, device.PAGE_LENGTH, page_FBC_16KB, page_FBC_4KB_list)

                            end_time = hw.get_nsec_time() 

                            ws.info("fail counts for: ch {0}, ce {1}, lun {2}, block {3}, page {4}, shift_dac {5}".format(ch, ce, lun, block, page, vt))
                            ws.info("page_fbc_16KB: {0}, page_FBC_4KB_list: {1}".format(page_FBC_16KB[0], list(page_FBC_4KB_list)))
                            ws.info("cal page FBC elapsed time: {0:.2f}ms".format((end_time - start_time)/1000000))
                           
                            ### 计算当前page的state FBC                          
                            if cur_level_number != 1:
                                state_cell_pattern_index = ch * max_planes + plane   
                                for state_level in states_in_cur_page:
                                    start_time = hw.get_nsec_time()

                                    state_fail_count = cal_pattern.and_cal_state_fbc(page_FBC_pattern, state_cell_pattern[state][state_cell_pattern_index], device.PAGE_LENGTH)
                                     
                                    fail_list_4_ch[ch][block][state_level][vt] = state_fail_count

                                    end_time = hw.get_nsec_time()

                                    ws.info("state {0} fail count: {1}".format(state_level, state_fail_count))
                                    ws.info("calculate state FBC elapsed time: {0:.2f}ms".format((end_time - start_time)/1000000))
                            else:
                                fail_list_4_ch[ch][block][0][vt] = page_FBC_16KB[0] #SLC不需要计算

                device.reset_read_offset_code(ch_list, ce, lun, 0)

                ####################### 对上面的扫描结果进行拟合，并计算出每个DAC的FBC  #######################
                for ch in ch_list:
                    for block in block_list:
                        for state_level in states_in_cur_page:
                            start_time = hw.get_nsec_time()
                            ### 获取扫描结果
                            vt_list = list(fail_list_4_ch[ch][block][state_level].keys())
                            state_fail_list = list(fail_list_4_ch[ch][block][state_level].values())
                            # ws.info("ch{0}, ce{1}, lun{2}, block{3}, page{4}, state {5}, vt list: {6}".format(ch, ce, lun, block, page, state_level, vt_list))
                            # ws.info("ch{0}, ce{1}, lun{2}, block{3}, page{4}, state {5}, fail list: {6}".format(ch, ce, lun, block, page, state_level, state_fail_list))
                            
                            #### 使用三次样条插值拟合一条平滑的曲线
                            # vt_min, state_fail_min, coeffs = fitting.fit_curve_and_find_minimum_poly(vt_list, state_fail_list, poly_degree = 2)
                            vt_min, x_dense, y_dense = fitting.fit_curve_and_find_minimum_spline(vt_list, state_fail_list)
                            end_time = hw.get_nsec_time()
                            ws.info("fitting elapsed time: {0:.2f}ms".format((end_time - start_time)/1000000))
                            
                            ## 保存best offset
                            best_state_offset_dict[ch][block][state_level] = vt_min

                            ### 输出拟合的结果到csv
                            if log_fails:
                                condition = "State{:02d}".format(state_level) 
                                logger.log_result_user(condition, ch, ce, [lun], [block], [page], "vt_list", x_dense, "")
                                logger.log_result_user(condition, ch, ce, [lun], [block], [page], "fail_list", y_dense, "")
                            
                        # ws.info("best offset update: ch: {0}, ce: {1}, lun: {2}, block: {3}, page: {4}, best offset: {5}".format(ch, ce, lun, block, page, best_state_offset_dict[ch][block]))

                        ### 用best offset再读一遍，并输出到csv，用于分析reliability
                        start_time = hw.get_nsec_time()
                        device.set_read_offset_code_multi_level(ch, ce, lun, page, best_state_offset_dict[ch][block])
                        device.reset_fails_count()
                        device.multi_plane_page_compare([ch], ce, lun, [block], page, 0, device.PAGE_LENGTH)
                        hw.select_channel(ch)
                        _, fails = hw.custom_sequence_get_fail_count()
                        ber_fails_4_ch[ch] += fails
                        end_time = hw.get_nsec_time()
                        ws.info("read with best offset elapsed time: {0:.2f}ms for ch: {1}, ce: {2}, lun: {3}, block: {4}, page: {5}".format((end_time - start_time)/1000000, ch, ce, lun, block, page))

                        if log_dac and cur_page_level == cur_level_number-1:
                            logger.log_result_user("", ch, ce, [lun], [block], [page], "BestDAC", best_state_offset_dict[ch][block], "")

                pages += 1
                # gc.collect()
        
    for ch in ch_list:
        error_rate = 100 * ber_fails_4_ch[ch] / (pages * device.PAGE_LENGTH * 8)
        ws.info("CH: {0}  - Fails: {1} - Best Error Rate: {2:.5f}".format(ch, ber_fails_4_ch[ch], error_rate))

    device.force_all1 = False
    device.force_all0 = False

####### output program pattern and read pattern to .bin file
__pdoc__["do_read_offset_write_binary_file"]=False
def do_read_offset_write_binary_file(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac, log_program_pattern):
    import gc

    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}
    # read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}
    device.init_test(log_enable=True, log_set=read_log_set)

    device.force_all1 = force_all1
    device.force_all0 = force_all0

    class page_type:
        LP = 0
        MP = 1
        UP = 2
        XP = 3

    # 计算需要的最大值
    max_ch = 2
    max_planes = device.PLANE_NUMBER
    max_levels = device.LEVEL_NUMBER

    BIT_COUNT_TABLE = [0] * 256
    for i in range(256):
        BIT_COUNT_TABLE[i] = (i & 1) + BIT_COUNT_TABLE[i >> 1]

    def get_program_pattern(ch, ce, lun, block, page):
        buffer = bytearray(device.PAGE_LENGTH)
        program_pattern, program_seed = device.expected_pattern_fpga_codes(ch, ce, lun, block, page) #get the pattern for LP/MP/UP/XP 
        if program_pattern == ePatternType.All1:
            for i in range(device.PAGE_LENGTH):
                buffer[i] = 0xFF
        elif program_pattern == ePatternType.All0:
            for i in range(device.PAGE_LENGTH):
                buffer[i] = 0x00
        elif program_pattern == ePatternType.Counter:
            for i in range(device.PAGE_LENGTH):
                buffer[i] = i & 0xFF
        elif program_pattern == ePatternType.Alt_FF_00:
            for i in range(0,device.PAGE_LENGTH,2):
                buffer[i] = 0xFF
                buffer[i+1] = 0x00
        elif program_pattern == ePatternType.Alt_55_AA:
            for i in range(0,device.PAGE_LENGTH,2):
                buffer[i] = 0x55
                buffer[i+1] = 0xAA
        elif program_pattern == ePatternType.Random:
            res, buffer = hw.random_fill_expected_buffer(program_seed, 0, device.PAGE_LENGTH)
        elif program_pattern == ePatternType.User:
            start_pos = page * device.PAGE_LENGTH
            buffer = device.pattern_buffer[start_pos:start_pos + device.PAGE_LENGTH]
            
        return buffer
    
    ################ get program pattern for ch/block
    ##每个ch/block存一个pattern
    if log_program_pattern == 1:

        file_wr = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
        file_name = "PorgPattern.bin"
        file_wr.open(file_name)

        for ce in ce_list:
            hw.select_ce(ce)
            for lun in lun_list:
                for ch in ch_list:
                    for block in block_list:
                        for page in page_list:                           
                            program_pattern = get_program_pattern(ch, ce, lun, block, page)
                            file_wr.write(program_pattern, device.PAGE_LENGTH)

        file_wr.close()
        file_wr.cleanup()

    ############### get read pattern for ch/block

    file_wr = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
    file_name = "ReadPattern.bin"
    file_wr.open(file_name)
    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for page in page_list:
                ws.info("***********Start page: {0}**************".format(page))
                if device.DEVICE_NAME == "X36070":
                    _,_,_,block_wl_max = device.page_info(page_list[-1])
                    _, _, cur_level_number, cur_wl = device.page_info(page)
                    max_state_level = 7 if cur_level_number == 3 else (15 if cur_level_number == 4 else 1)
                    if cur_level_number == 4 and cur_wl > block_wl_max-device.STRING_NUMBER:
                        continue
                else:
                    cur_level_number = device.LEVEL_NUMBER
                    max_state_level = 7

                state_offset_dict = [0] * max_state_level

                #####不同state在RD/DR中的偏移量不一样，可以添加一个offset_list对每个state作调整

                for vt in range(start_dac, end_dac + 1, step_dac):
                    for ch in ch_list:
                        for i in range(max_state_level):
                            state_offset_dict[i] = vt 
                        device.set_read_offset_code_multi_level(ch, ce, lun, page, state_offset_dict)   #这里的offset是实际使用的十进制，如-20~20 DAC

                    device.reset_fails_count()
                    read_pattern_dict = device.multi_plane_page_read_pattern(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)     #read without log FBC, return read pattern

                    for ch in ch_list:
                        for block in block_list:
                            read_pattern = read_pattern_dict[ch][block]
                            file_wr.write(read_pattern, device.PAGE_LENGTH)
        file_wr.close()
        file_wr.cleanup()

    device.force_all1 = False
    device.force_all0 = False


# # use this code for test time reduction and result for all pages together
# __pdoc__["do_read_offset_ber"]=False
# def do_read_offset_ber(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac, force_all1, force_all0,
#                        chart_enable):
#     device.force_all1 = force_all1
#     device.force_all0 = force_all0
#
#     # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
#     read_log_set = {LOG_SET_ITEM.FAILS}
#
#     device.init_test(log_enable=False, log_set=read_log_set)
#
#     plots = [None, None]
#
#     for ce in ce_list:
#         hw.select_ce(ce)
#         for lun in lun_list:
#
#             if chart_enable:
#                 for ch in ch_list:
#                     title = make_unique_plot_name("READ OFFSET BER", "CH{0} Ce{1} L{2}".format(ch, ce, lun))
#                     vt_plot = nanocycler.plot()
#                     vt_plot.openext(title, "Offset", enumScaleType.Numerical, "0.00", False,
#                                     "BER", enumScaleType.Numerical, "0.00", False, "Level")
#                     plots[ch] = vt_plot
#
#             for level in device.get_valid_level_for_page(page):
#
#                 series = device.level_name(level)
#
#                 fail_list_4_ch = [[], []]
#
#                 for vt in range(start_dac, end_dac+1, step_dac):
#
#                     # reset fail counter and disable log
#                     condition = "L{:02d}_VT{:02x}".format(level, vt)
#                     device.reset_fails_count()
#                     device.set_condition(condition)
#
#                     res, x_pos = device.set_read_offset_code(ch_list, ce, lun, level, vt)
#                     if not res:
#                         continue
#
#                     page_count = 0
#                     for block in block_list:
#                         for page in page_list:
#                             device.read_offset_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)
#                             page_count += 1
#
#                     device.reset_read_offset_code(ch_list, ce, lun, level)
#
#                     for ch in ch_list:
#                         fails_bit = device.get_fails_count(ch)
#                         fail_list_4_ch[ch].append(fails_bit)
#
#                         error_rate = 100 * fails_bit / (device.PAGE_LENGTH * 8 * page_count)
#                         if plots[ch] is not None:
#                             plots[ch].add(x_pos, error_rate, series)
#
#
#                 # log as array, one fails bit for each vt value
#                 condition = "L{:02d}".format(level)
#                 for ch in ch_list:
#                     logger.log_fails(condition, ch, ce, [lun], block_list, page_list, fail_list_4_ch[ch])
#
#             for ch in ch_list:
#                 if plots[ch] is not None:
#                     plots[ch].flush()
#                     plots[ch].close()
#                     plots[ch].cleanup()
#
#     device.force_all1 = False
#     device.force_all0 = False



__pdoc__["do_read_offset_vth"]=False
def do_read_offset_vth(ch_list: [], ce_list: [], lun_list: [], block_list: [], pages, start_dac, end_dac, step_dac, chart_enable):
    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK} # Eric
    device.init_test(log_enable=True, log_set=read_log_set)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for page in pages:

                    vt_plot = None
                    if chart_enable:
                        vt_plot = nanocycler.plot()

                        title = make_unique_plot_name("READ OFFSET VTH",
                                                      "Ce{0} L{1} B{2} P{3}".format(ce, lun, block, page))
                        vt_plot.openext(title, "VT", enumScaleType.Numerical,
                                        "0.00", False, "Bits", enumScaleType.Numerical, "0.00", True, "Level(mV)")

                    for level in device.get_valid_level_for_page(page):
                        series = device.level_name(level)

                        last_pass = 0
                        for vt in range(start_dac, end_dac+1, step_dac):

                            # reset fail counter and disable log
                            condition = "L{:02d}_VT{:02x}".format(level, vt)
                            device.reset_fails_count()
                            device.set_condition(condition)

                            res, x_pos = device.set_read_offset_code(ch_list, ce, lun, level, vt)
                            if not res:
                                continue

                            device.read_offset_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)

                            device.reset_read_offset_code(ch_list, ce, lun, level)

                            fails_bit = 0
                            for ch in ch_list:
                                fails_bit += device.get_fails_count(ch)

                            pass_now = len(ch_list) * device.PAGE_LENGTH * 8 - fails_bit  # bit good

                            if vt_plot is not None:
                                if vt > 0:  # plot show the differential curve, start from second point
                                    x = x_pos
                                    y = abs(pass_now - last_pass)
                                    vt_plot.add(x, y, series)

                            last_pass = pass_now

                        # is it necessary ??? maybe no
                        device.reset_read_offset_code(ch_list, ce, lun, level)

                    if vt_plot is not None:
                        vt_plot.close()
                        vt_plot.cleanup()


__pdoc__["do_vth_read"]=False
def do_vth_read(ch_list: [], ce_list: [], lun_list: [], block_list: [], wls, start_dac, end_dac, step_dac):
    read_log_set = {LOG_SET_ITEM.FAILS} 
    device.init_test(log_enable=False, log_set=read_log_set)

    title = make_unique_plot_name("", "VTH READ")
    vt_plot = nanocycler.plot()
    vt_plot.openext(title, "VT", enumScaleType.Numerical,
                    "0.00", False, "Bits", enumScaleType.Numerical, "0.00", True, "Level(mV)")

    summary_fw = nanocycler.filewriter()
    # res, time = hw.get_time()
    # if (res):
    #     file_name = "Vt" + "-" + time
    # file_name = file_name.replace(".","-")
    summary_fw.open('Vt.csv')
    
    sline = "CH, Ce, Lun, Block, WordLine, Page, Vt, Count"
    summary_fw.write_line(sline)
        
    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for wl in wls:
                    # wl = (page // device.LEVEL_NUMBER)
                    # page = (page // device.LEVEL_NUMBER)*device.LEVEL_NUMBER
                    page = wl*device.LEVEL_NUMBER
                    if device.DEVICE_NAME == "X36070":  #hang, add for X36070
                        if wl < 6:
                            bits_per_cell = 3   
                            page = wl*bits_per_cell
                        elif wl < 678:
                            bits_per_cell = 4
                            page = (wl-6)*bits_per_cell + 18
                        elif wl < 702:
                            bits_per_cell = 3
                            page = (wl-678)*bits_per_cell + 2706
                        elif wl < 1374:
                            bits_per_cell = 4
                            page = (wl-702)*bits_per_cell + 2778
                        elif wl < 1392:
                            bits_per_cell = 3
                            page = (wl-1374)*bits_per_cell + 5466
                        else:
                            bits_per_cell = 1
                            page = (wl-1392)*bits_per_cell + 5520
                    else:
                        # wl = (page // device.LEVEL_NUMBER)
                        # page = (page // device.LEVEL_NUMBER)*device.LEVEL_NUMBER    
                        page = wl*device.LEVEL_NUMBER

                    if bits_per_cell == 4:  #hang, add for X36070
                        for level in range(device.VT_LEVEL):    #from 0 to 1/7/15(not include), 0 is RL1, and so on
                            if level not in device.VT_SCAN:
                                continue

                            current_read_page = device.page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)
                    elif bits_per_cell == 3:
                        for level in range(device.TLC_VT_LEVEL):
                            if level not in device.TLC_VT_SCAN:
                                continue

                            current_read_page = device.tlc_page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.tlc_vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.tlc_set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)
                    else:
                        for level in range(device.SLC_VT_LEVEL):
                            if level not in device.SLC_VT_SCAN:
                                continue

                            current_read_page = device.slc_page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.slc_vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.slc_set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)
                    
                    vt_plot.flush()

    vt_plot.close()
    vt_plot.cleanup()

    summary_fw.close()
    summary_fw.cleanup()

__pdoc__["do_vth_read_randWL"]=False
def do_vth_read_randWL(ch_list: [], ce_list: [], lun_list: [], block_list: [], wl_dlist:[], start_dac, end_dac, step_dac):
    read_log_set = {LOG_SET_ITEM.FAILS} 
    device.init_test(log_enable=False, log_set=read_log_set)

    title = make_unique_plot_name("", "VTH READ")
    vt_plot = nanocycler.plot()
    vt_plot.openext(title, "VT", enumScaleType.Numerical,
                    "0.00", False, "Bits", enumScaleType.Numerical, "0.00", True, "Level(mV)")

    summary_fw = nanocycler.filewriter()
    # res, time = hw.get_time()
    # if (res):
    #     file_name = "Vt" + "-" + time
    # file_name = file_name.replace(".","-")
    summary_fw.open('Vt.csv')
    
    sline = "CH, Ce, Lun, Block, WordLine, Page, Vt, Count"
    summary_fw.write_line(sline)
        
    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                for wl in wl_dlist:              
                    if device.DEVICE_NAME == "X36070":  #hang, add for X36070
                        if wl < 6:
                            bits_per_cell = 3   
                            page = wl*bits_per_cell
                        elif wl < 678:
                            bits_per_cell = 4
                            page = (wl-6)*bits_per_cell + 18
                        elif wl < 702:
                            bits_per_cell = 3
                            page = (wl-678)*bits_per_cell + 2706
                        elif wl < 1374:
                            bits_per_cell = 4
                            page = (wl-702)*bits_per_cell + 2778
                        elif wl < 1392:
                            bits_per_cell = 3
                            page = (wl-1374)*bits_per_cell + 5466
                        else:
                            bits_per_cell = 1
                            page = (wl-1392)*bits_per_cell + 5520
                    else:
                        # wl = (page // device.LEVEL_NUMBER)
                        # page = (page // device.LEVEL_NUMBER)*device.LEVEL_NUMBER    
                        page = wl*device.LEVEL_NUMBER
                        bits_per_cell == 3
                    
                    if bits_per_cell == 4:  #hang, add for X36070
                        for level in range(device.VT_LEVEL):    #from 0 to 1/7/15(not include), 0 is RL1, and so on
                            if level not in device.VT_SCAN:
                                continue

                            current_read_page = device.page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)
                    elif bits_per_cell == 3:
                        for level in range(device.TLC_VT_LEVEL):
                            if level not in device.TLC_VT_SCAN:
                                continue

                            current_read_page = device.tlc_page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.tlc_vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.tlc_set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)
                    elif bits_per_cell == 1:
                        for level in range(device.SLC_VT_LEVEL):
                            if level not in device.SLC_VT_SCAN:
                                continue

                            current_read_page = device.slc_page_convert(level, page) # LSB page num or CSB/MSB....

                            start_dac, end_dac, voltage_offset = device.slc_vt_init(ch_list, ce, lun, level)

                            last_pass = [0, 0]
                            for vt in range(start_dac, end_dac, step_dac): # start_dac to end_dac = [0, 256]

                                # reset fail counter and disable log
                                condition = "L{:02d}_VT{:02x}".format(level, vt)
                                device.reset_fails_count()
                                device.set_condition(condition)

                                res, x_pos = device.slc_set_vt_code(ch_list, ce, lun, level, vt) # x_pos is voltage shift [mV]
                                if not res:
                                    continue

                                device.vt_page_compare(ch_list, ce, lun, block, current_read_page, 0, device.PAGE_LENGTH)

                                for ch in ch_list:
                                    series = "CH{0} Ce{1} L{2} B{3} P{4} {5}".format(ch, ce, lun, block, current_read_page,
                                                                                    device.vt_level_name(level))
                                    fails_bits = device.get_fails_count(ch)

                                    pass_now = len([ch]) * device.PAGE_LENGTH * 8 - fails_bits  # bit good

                                    if vt != start_dac:  # plot show the differential curve, start from second point
                                    # if vt > 0:  # plot show the differential curve, start from second point
                                        x = x_pos + voltage_offset
                                        y = abs(pass_now - last_pass[ch])
                                        vt_plot.add(x, y, series)
                                        sline = "{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7} ".format(ch, ce, lun, block, wl, current_read_page, x, y)
                                        summary_fw.write_line(sline)

                                    last_pass[ch] = pass_now

                                device.reset_vt_code(ch_list, ce, lun)
                                device.vt_terminate(ch_list, ce, lun, level)

                    vt_plot.flush()

    vt_plot.close()
    vt_plot.cleanup()

    summary_fw.close()
    summary_fw.cleanup()

__pdoc__["do_read_offset_vth_dump"]=False
def do_read_offset_vth_dump(vt_file_name, ch, ce, lun, block, page_list: [], start_dac, end_dac, step_dac):
    device.init_test(log_enable=False)

    ws.info("Pattern Seed is: {0}".format(data_mgr.get_seed(ch, ce, lun, block)))

    for level in range(0, device.VT_LEVEL):
        idx = 0
        for code in range(start_dac, end_dac+1, step_dac):

            res, x_pos = device.set_read_offset_code([ch], ce, lun, level, code)
            if not res:
                break

            file_name = "{0}-{1}-{2}".format(level, hex(idx), vt_file_name)
            file_wr = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
            file_wr.open(file_name)

            for page in page_list:
                device.read_offset_page_read([ch], ce, lun, block, page, 0, device.PAGE_LENGTH)
                buffer = device.get_read_buffer(ch, device.PAGE_LENGTH)
                file_wr.write(buffer, device.PAGE_LENGTH)

            device.reset_read_offset_code([ch], ce, lun, level)

            file_wr.close()
            file_wr.cleanup()

            idx += step_dac

    return True

__pdoc__["do_read_offset_dump"]=False
def do_read_offset_dump(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], start_dac, end_dac, step_dac):
    device.init_test(log_enable=False)

    for ce in ce_list:
        hw.select_ce(ce)
        for ch in ch_list:
            for lun in lun_list:
                for block in block_list:
                    file_wr = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
                    file_name = "CH{0}-CE{1}-L{2}-B{3}.bin".format(ch, ce, lun, block)
                    ws.info(file_name)
                    file_wr.open(file_name)
                    for page in page_list:
                        for level in device.get_valid_level_for_page(page):
                            for vt in range(start_dac, end_dac+1, step_dac):

                                res, x_pos = device.set_read_offset_code([ch], ce, lun, level, vt)
                                if not res:
                                    break
                                device.read_offset_page_read([ch], ce, lun, block, page, 0, device.PAGE_LENGTH)
                                buffer = device.get_read_buffer(ch, device.PAGE_LENGTH)
                                file_wr.write(buffer, device.PAGE_LENGTH)

                            device.reset_read_offset_code([ch], ce, lun, level)

                    file_wr.close()
                    file_wr.cleanup()

    return True


__pdoc__["do_read_retry"]=False
def do_multi_plane_read_retry(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], page_list: [], read_retry_plot,
                  option_list: []):
    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    # read_log_set = {LOG_SET_ITEM.FAILS4CHUNK} # Eric
    read_log_set = { LOG_SET_ITEM.FAILS}
    device.init_test(log_enable=True, log_set=read_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:

            for read_retry in option_list:
                res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                if not res:
                    continue

                # reset fail count
                device.reset_fails_count()
                device.set_condition(condition)

                ws.info("{0}".format(condition))

                for block_list in list_block_list:
                    if device.DEVICE_NAME == "X36070":
                        #new script for YMTC X36070 QLC NAND, hang
                        #only for close block, open block use another function: open_read_retry_brick_eric
                        if read_retry == 0:
                            for page in range(0,page_list[-1]+1):
                                device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                        elif read_retry < 10:  #SLC RRT
                            SLC_page_list = range(5520,page_list[-1]+1)
                            for page in SLC_page_list:
                                device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                        elif read_retry in range(10,62):
                            TLC_page_list = list(range(0,18)) + list(range(2706,2778)) + list(range(5466,5520))
                            for page in TLC_page_list:
                                device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                        else:
                            QLC_page_list = list(range(18,2706)) + list(range(2778,5466))
                            for page in QLC_page_list:
                                device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                    else:
                        # old script for other NANDs
                        for page in page_list:
                            device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)

                for ch in ch_list:
                    series = "CH{0}_CE{1}_L{2}".format(ch, ce, lun)
                    print_ber(ch, ce_list=[ce], lun_list=[lun], block_list=block_list, page_list=page_list,
                              plot=read_retry_plot, x=read_retry, series=series)

                device.reset_read_retry_option(ch_list, ce, lun)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break
            
            if read_retry_plot is not None:
                read_retry_plot.flush()


def wordline_group_check(ch_used: [], ce, lun, page, read_retry, block_page_list, inner_wl_set_flag:[], edge_wl_set_flag:[], close_wl_set_flag:[]):
        
    for ch in ch_used:

        end_page = block_page_list[-1]

        if read_retry == 0:
            continue
        else:
            inner_rr_range, inner_wl_group = [-100,-100], [-100,-100]
            edge_rr_range, edge_wl_group = [-100,-100], [-100,-100]
            
            if device.DEVICE_NAME == "X36070":
                # 改为通过wordline来判断inner_wl和edge_wl,hang
                cur_layer, _, _, cur_wl = device.page_info(page)
                inner_wl_range, edge_wl_range = device.qlc_inner_edge_wl_assign(end_page)
                close_RR_count = 53
                
                # Check if Inner WL of Open BLock 
                if int(end_page) != int(device.PAGE_NUMBER-1) and cur_wl in inner_wl_range:
                    inner_rr_range[ch], inner_wl_group[ch] = device.inner_wl_group(cur_layer)
                    if inner_wl_group[ch] != inner_wl_set_flag[ch]: # inner_wl_group == inner_wl_set_flag说明该范围内的wl都已经set_feature了该used_rr
                        ws.info("CH-{0} InnerWL group: {1}, start_page:{2}".format(ch, inner_wl_group[ch], page))
                        inner_wl_set_flag[ch] = inner_wl_group[ch]
                        used_rr = read_retry + inner_wl_group[ch]*close_RR_count
                        if used_rr in inner_rr_range[ch]: # inner_rr_range是默认设置，即便used_rr在inner_rr_range里，但是实际测试的总rr数可能小于used_rr, 所以需要下面res判断
                            res, condition = device.set_read_retry_option([ch], ce, lun, used_rr)
                            if not res: # set rr failed: used_rr exceed the total rr count or other conditions
                                inner_wl_set_flag[ch] = -1
                                continue
                            device.set_condition(condition)
                            ws.info("CH-{0} InnerWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                        else:
                            continue  
                    else:
                        continue
                
                # Check if Edge WL of Open BLock 
                elif int(end_page) != int(device.PAGE_NUMBER-1) and cur_wl in edge_wl_range:
                    ws.info("CH-{0} EdgeWL page: {1}".format(ch, page))
                    edge_rr_range[ch], edge_wl_group[ch] = device.edge_wl_group(cur_layer)
                    if edge_wl_group[ch] != edge_wl_set_flag[ch]: # edge_wl_group == edge_wl_set_flag[ch] means already did set_rr for the wl range
                        ws.info("CH-{0} EdgeWL group: {1}, start_page:{2}".format(ch, edge_wl_group[ch], page))
                        edge_wl_set_flag[ch] = edge_wl_group[ch]
                        edge_used_rr = read_retry + edge_wl_group[ch]*close_RR_count
                        if edge_used_rr in edge_rr_range[ch]:
                            res, condition = device.set_read_retry_option([ch], ce, lun, edge_used_rr)
                            if not res:
                                edge_wl_set_flag[ch] = -1
                                continue
                            device.set_condition(condition)
                            ws.info("CH-{0} EdgeWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                        else:
                            continue
                    else:
                        continue
                # Close Block
                elif int(end_page) == int(device.PAGE_NUMBER-1):
                    if read_retry != close_wl_set_flag[ch]: # edge_wl_group == edge_wl_set_flag[ch] means already did set_rr for the wl range
                        close_wl_set_flag[ch] = read_retry
                        res, condition = device.set_read_retry_option([ch], ce, lun, read_retry)
                        if not res:
                            edge_wl_set_flag[ch] = -1
                            continue
                        device.set_condition(condition)
                        ws.info("CH-{0} CloseWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                    else:
                        continue
            else:
                # Check if Inner WL of Open BLock 
                if int(end_page) != int(device.PAGE_NUMBER-1) and int(page/3) <= (int(end_page/3) - 6):
                    inner_rr_range[ch], inner_wl_group[ch] = device.inner_wl_group(int(int(page/3)/6)) # here wl means layer
                    if inner_wl_group[ch] != inner_wl_set_flag[ch]: # inner_wl_group == inner_wl_set_flag说明该范围内的wl都已经set_feature了该used_rr
                        inner_wl_set_flag[ch] = inner_wl_group[ch]
                        used_rr = read_retry + (inner_wl_group[ch]-1)*25
                        if used_rr in inner_rr_range[ch]: # inner_rr_range是默认设置，即便used_rr在inner_rr_range里，但是实际测试的总rr数可能小于used_rr, 所以需要下面res判断
                            res, condition = device.set_read_retry_option([ch], ce, lun, used_rr)
                            if not res: # set rr failed: used_rr exceed the total rr count or other conditions
                                inner_wl_set_flag[ch] = -1
                                continue
                            device.set_condition(condition)
                            ws.info("CH-{0} InnerWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                        else:
                            continue  
                    else:
                        continue
                
                # Check if Edge WL of Open BLock 
                elif int(end_page) != int(device.PAGE_NUMBER-1) and (int(page/3) > (int(end_page/3) - 6) and int(page/3) <= (int(end_page/3))):
                    edge_rr_range[ch], edge_wl_group[ch] = device.edge_wl_group(int(int(page/3)/6)) # here wl means layer
                    if edge_wl_group[ch] != edge_wl_set_flag[ch]: # edge_wl_group == edge_wl_set_flag[ch] means already did set_rr for the wl range
                        edge_wl_set_flag[ch] = edge_wl_group[ch]
                        edge_used_rr = read_retry + (edge_wl_group[ch]-1)*25
                        if edge_used_rr in edge_rr_range[ch]:
                            res, condition = device.set_read_retry_option([ch], ce, lun, edge_used_rr)
                            if not res:
                                edge_wl_set_flag[ch] = -1
                                continue
                            device.set_condition(condition)
                            ws.info("CH-{0} EdgeWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                        else:
                            continue
                    else:
                        continue
                # Close Block
                elif int(end_page) == int(device.PAGE_NUMBER-1):
                    if read_retry != close_wl_set_flag[ch]: # edge_wl_group == edge_wl_set_flag[ch] means already did set_rr for the wl range
                        close_wl_set_flag[ch] = read_retry
                        res, condition = device.set_read_retry_option([ch], ce, lun, read_retry)
                        if not res:
                            edge_wl_set_flag[ch] = -1
                            continue
                        device.set_condition(condition)
                        ws.info("CH-{0} CloseWL RR: Orig-{1}, {2}".format(ch, read_retry, int(str(condition), 16)))
                    else:
                        continue

    return inner_wl_set_flag, edge_wl_set_flag, close_wl_set_flag

__pdoc__["do_multi_plane_openblk_read_retry"]=False
def do_multi_plane_openblk_read_retry(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], list_page_list: [[]], read_retry_plot, option_list: []):

    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK} # Eric
    device.init_test(log_enable=True, log_set=read_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for read_retry in option_list:
                if device.DEVICE_NAME == "X36070":
                    SLC_page_list = range(5520,5544)
                    TLC_page_list = list(range(0,18)) + list(range(2706,2778)) + list(range(5466,5520))
                    QLC_page_list = list(range(18,2706)) + list(range(2778,5466))

                if (device.DEVICE_NAME != "X39070" and device.DEVICE_NAME != "X36070") or read_retry == 0:
                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                    if not res:
                        continue

                    # reset fail count
                    device.reset_fails_count()
                    device.set_condition(condition)
                    ws.info("{0}".format(condition))

                elif device.DEVICE_NAME == "X39070" and read_retry > 25: # (read_retry-25+175) Inner wordline provides 10 in-house RR:RR176~185
                    read_retry = read_retry - 25 + 175
                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                    if not res:
                        continue

                    device.reset_fails_count()
                    device.set_condition(condition)
                    ws.info("{0}".format(condition))
                
                else:
                    # reset fail count
                    device.reset_fails_count()
                    ws.info("Priliminary RR: {0}".format(read_retry))

                for block_index in range(len(list_block_list)):  #hang, 两个channel的block是一样的，所以要用index来区分

                    original_block_list = list_block_list[block_index]
                    
                    inner_wl_set_flag = [-1, -1]  # 预设的错误值
                    edge_wl_set_flag  = [-1, -1]  # 预设的错误值
                    close_wl_set_flag = [-1, -1]  # 预设的错误值

                    ch_used = []
                    if len(list_block_list) < 5:
                        ch_used = ch_list
                    else:
                        ch_used.append(ch_list[block_index//4]) # 获取block_list所属的ch，并存在ch_used[0]

                    if ch_used:
                        # hang, 若是list_page_list只有一个，则直接使用list_page_list[0]，否则使用list_page_list[block_index]
                        if len(list_page_list) == 1:
                            original_block_page_list = list_page_list[0]
                        else:
                            original_block_page_list = list_page_list[block_index]

                        block_page_list = []
                        get_error_rate_flag = False

                        if device.DEVICE_NAME == "X36070":

                            _,_,_,block_wl_max = device.page_info(original_block_page_list[-1])
                                                        
                            # 去除block_page_list中大于block_wl_max-6的 QLC page
                            for page in original_block_page_list:
                                _, _, cur_levels, cur_wl = device.page_info(page)
                                if cur_levels == 4 and cur_wl > block_wl_max-device.STRING_NUMBER:
                                    continue
                                else:
                                    block_page_list.append(page)

                            # hang, 对于X36070，需要将block_list进行处理，以前的list_block_list对应的是一个partial block的不同PEC block，
                            # 现在的list_block_list对应的是不同partial block的PEC=1 block，因此需要将PEC=1/2/3/4/5K的block添加进来
                            # 后续若有其他PEC block，需要修改这里                            
                            for i in range(6): #6是PEC的数量
                                block_list = []
                                if original_block_list[0] == 4:     #hang, 为【4-7】这组block特殊处理
                                    for block in original_block_list:
                                        if i < 3:
                                            block_list.append(block + i*8)
                                        elif i < 5: 
                                            block_list.append(block + i*8 + 16)
                                        else:
                                            block_list.append(block + i*8 + 20)
                                else:
                                    for block in original_block_list:
                                        if i < 5:
                                            block_list.append(block + i*8)
                                        else:
                                            block_list.append(block + i*8 + 4)

                                if read_retry == 0: #RR0 is general case, offset is 0                                
                                    ws.info("Read retry on ch{1}, Block list{0}, all page list: {2}".format(block_list, ch_used, block_page_list))
                                    for page in block_page_list:                           
                                        device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                    get_error_rate_flag = True

                                elif read_retry < 10: #SLC RRT, 不区分open block和close block
                                    if not set(SLC_page_list) & set(block_page_list):
                                        continue
                                    ws.info("Read retry on ch{1}, Block list{0}, SLC page list: {2}".format(block_list, ch_used, sorted(set(SLC_page_list) & set(block_page_list))))
                                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                                    if not res:
                                        continue
                                    device.reset_fails_count()
                                    device.set_condition(condition)
                                    # ws.info("{0}".format(condition))
                                    for page in sorted(set(SLC_page_list) & set(block_page_list)):  # 获取block_page_list中在SLC_page_list中的page
                                        device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                    get_error_rate_flag = True

                                elif read_retry in range(10,62): #TLC RRT, open block和close block分别使用不同的RR, open block不区分inner_wl和edge_wl
                                    if not set(TLC_page_list) & set(block_page_list):
                                        continue
                                    if (read_retry < 49 and int(block_page_list[-1]) == int(device.PAGE_NUMBER-1)) or (read_retry >= 49 and int(block_page_list[-1]) < int(device.PAGE_NUMBER-1)):
                                        ws.info("Read retry on ch{1}, Block list{0}, TLC page list: {2}".format(block_list, ch_used, sorted(set(TLC_page_list) & set(block_page_list))))
                                        res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                                        if not res:
                                            continue
                                        device.reset_fails_count()
                                        device.set_condition(condition)
                                        # ws.info("{0}".format(condition))
                                        for page in sorted(set(TLC_page_list) & set(block_page_list)):  # 获取block_page_list中在TLC_page_list中的page  
                                            device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                        get_error_rate_flag = True

                                elif read_retry in range(62,70): #QLC RRT for close/open block
                                    if not set(QLC_page_list) & set(block_page_list):
                                        continue
                                    ws.info("Read retry on ch{1}, Block list{0}, QLC page list: {2}".format(block_list, ch_used, sorted(set(QLC_page_list) & set(block_page_list))))
                                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                                    if not res:
                                        continue
                                    device.reset_fails_count()
                                    device.set_condition(condition)
                                    # ws.info("{0}".format(condition))
                                    for page in sorted(set(QLC_page_list) & set(block_page_list)):  # 获取block_page_list中在QLC_page_list中的page
                                        device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                    get_error_rate_flag = True

                                elif read_retry in range(70,123): #QLC RRT
                                    if not set(QLC_page_list) & set(block_page_list):
                                        continue
                                    ws.info("Read retry on ch{1}, Block list{0}, QLC page list to wordline group check: {2} ".format(block_list, ch_used, sorted(set(QLC_page_list) & set(block_page_list))))
                                    for page in sorted(set(QLC_page_list) & set(block_page_list)):  # 获取block_page_list中在QLC_page_list中的page
                                        inner_wl_set_flag, edge_wl_set_flag, close_wl_set_flag = wordline_group_check( ch_used, ce, lun, page, read_retry, original_block_page_list, inner_wl_set_flag, edge_wl_set_flag, close_wl_set_flag)
                                        device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                    get_error_rate_flag = True
                        else:
                            if len(list_page_list) == 1:
                                block_page_list = list_page_list[0]
                            else:
                                block_page_list = list_page_list[block_index]
                            for page in block_page_list:                                
                                if device.DEVICE_NAME == "X39070": #RR0 (offset is 0) is general case, will go to 'else loop'
                                    if read_retry <= 25:
                                        inner_wl_set_flag, edge_wl_set_flag = wordline_group_check( ch_used, ce, lun, page, read_retry, block_page_list, inner_wl_set_flag, edge_wl_set_flag, close_wl_set_flag)
                                        if ch_used:
                                            device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_page_list, page, 0, device.PAGE_LENGTH)
                                    else:
                                        device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_page_list, page, 0, device.PAGE_LENGTH)
                                else:
                                    device.multi_plane_read_retry_page_compare(ch_used, ce, lun, block_page_list, page, 0, device.PAGE_LENGTH)
                            get_error_rate_flag = True
                
                    if get_error_rate_flag:
                        for ch in ch_used:
                            series = "CH{0}_CE{1}_L{2}".format(ch, ce, lun)
                            print_ber(ch, ce_list=[ce], lun_list=[lun], block_list=block_list, page_list=block_page_list,
                                    plot=read_retry_plot, x=read_retry, series=series)
                        get_error_rate_flag = False

                device.reset_read_retry_option(ch_list, ce, lun)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break
            
            if read_retry_plot is not None:
                read_retry_plot.flush()

__pdoc__["do_multi_plane_openblk_read_retry"]=False
def do_multi_plane_openblk_read_retry_backup_oldscript(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], page_list: [], read_retry_plot,
                  option_list: []):
    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK} # Eric
    device.init_test(log_enable=True, log_set=read_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:

            for read_retry in option_list:
                if device.DEVICE_NAME != "X39070" or read_retry == 0 or int(page_list[-1]) == int(device.PAGE_NUMBER-1):
                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                    if not res:
                        continue

                    # reset fail count
                    device.reset_fails_count()
                    device.set_condition(condition)
                    ws.info("{0}".format(condition))

                elif device.DEVICE_NAME == "X39070" and read_retry > 25: # (read_retry-26+176)Inner wordline provides 10 in-house RR:RR176~185
                    read_retry = read_retry - 25 + 175
                    res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                    if not res:
                        continue

                    # reset fail count
                    device.reset_fails_count()
                    device.set_condition(condition)

                    ws.info("{0}".format(condition))

                else:
                    # reset fail count
                    device.reset_fails_count()
                    ws.info("Priliminary RR: {0}".format(read_retry))

                for block_list in list_block_list:
                    inner_wl_set_group = -1 # 预设的错误值
                    edge_wl_set_group = -1  # 预设的错误值
                    
                    # page_list = list_page_list[list_block_list.index(block_list)]
                    for page in page_list:

                        if device.DEVICE_NAME == "X39070" and read_retry != 0: #RR0 (offset is 0) is general case, will go to 'else loop'
                            
                            if read_retry <= 25:
                                # Open BLock with Inner WL
                                if int(page_list[-1]) != int(device.PAGE_NUMBER-1) and int(page/3) <= (int(page_list[-1]/3) - 6):
                                    rr_range, rr_group = device.inner_wl_group(int(int(page/3)/6))
                                    if rr_group != inner_wl_set_group: # 如果rr_group == set_group说明该范围内的wl都已经设置过了该used_rr
                                        inner_wl_set_group = rr_group
                                        used_rr = read_retry + (rr_group-1)*25
                                        if used_rr in rr_range: # inner_rr_range是默认设置，即便used_rr在inner_rr_range里，但是实际测试的总rr数可能小于used_rr, 所以需要下面res判断
                                            res, condition = device.set_read_retry_option(ch_list, ce, lun, used_rr)
                                            if not res: # set rr failed: used_rr exceed the total rr count or other conditions
                                                inner_wl_set_group = -1
                                                continue
                                            device.set_condition(condition)
                                            ws.info("Inner RR: Orig-{0}, {1}".format(read_retry, condition))
                                            device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                        else:
                                            continue

                                    elif inner_wl_set_group != -1:
                                        device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                
                                # Open BLock with Edge WL
                                elif int(page_list[-1]) != int(device.PAGE_NUMBER-1) and int(page/3) > (int(page_list[-1]/3) - 6):
                                    edge_rr_range, edge_wl_group = device.edge_wl_group(int(int(page/3)/6))
                                    if edge_wl_group != edge_wl_set_group: # edge_wl_group == edge_wl_set_group means already did set_rr for the wl range
                                        edge_wl_set_group = edge_wl_group
                                        edge_used_rr = read_retry + (edge_wl_group-1)*25
                                        if edge_used_rr in edge_rr_range:
                                            res, condition = device.set_read_retry_option(ch_list, ce, lun, edge_used_rr)
                                            if not res:
                                                edge_wl_set_group = -1
                                                continue
                                            device.set_condition(condition)
                                            ws.info("Edge RR: Orig-{0}, {1}".format(read_retry, condition))
                                            device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                        else:
                                            continue

                                    elif edge_wl_set_group != -1:
                                        device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)

                                # Close Block
                                elif int(page_list[-1]) == int(device.PAGE_NUMBER-1):
                                    # res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                                    # if not res:
                                    #     continue
                                    
                                    # device.set_condition(condition)
                                    # ws.info("{0}".format(condition))

                                    device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                            else:
                                device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)
                                
                        else:
                            device.multi_plane_read_retry_page_compare(ch_list, ce, lun, block_list, page, 0, device.PAGE_LENGTH)

                for ch in ch_list:
                    series = "CH{0}_CE{1}_L{2}".format(ch, ce, lun)
                    print_ber(ch, ce_list=[ce], lun_list=[lun], block_list=block_list, page_list=page_list,
                              plot=read_retry_plot, x=read_retry, series=series)
                    # mp_print_ber(ch, ce_list=[ce], lun_list=[lun], block_list=block_list, list_page_list=list_page_list,
                    #           plot=read_retry_plot, x=read_retry, series=series)
                    
                device.reset_read_retry_option(ch_list, ce, lun)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break
            
            if read_retry_plot is not None:
                read_retry_plot.flush()

__pdoc__["do_read_retry"]=False
def do_read_retry(ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: [], read_retry_plot,
                  option_list: []):
    # read_log_set = { eLogSetItem.FAILS, eLogSetItem.FAILS4CHUNK }
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK} # Eric
    device.init_test(log_enable=True, log_set=read_log_set)
    pmu_setup(PMU_ALGO.NoPmu)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:

            for read_retry in option_list:

                res, condition = device.set_read_retry_option(ch_list, ce, lun, read_retry)
                if not res:
                    continue

                # reset fail count
                device.reset_fails_count()
                device.set_condition(condition)

                ws.info("{0}".format(condition))

                for block in block_list:
                    for page in page_list:
                        device.read_retry_page_compare(ch_list, ce, lun, block, page, 0, device.PAGE_LENGTH)

                for ch in ch_list:
                    series = "CH{0}_CE{1}_L{2}".format(ch, ce, lun)
                    print_ber(ch, ce_list=[ce], lun_list=[lun], block_list=block_list, page_list=page_list,
                              plot=read_retry_plot, x=read_retry, series=series)

                device.reset_read_retry_option(ch_list, ce, lun)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break
            
            if read_retry_plot is not None:
                read_retry_plot.flush()


__pdoc__["do_temperature_on"]=False
def do_temperature_on(fSet, lSettleSec=0, plot_refresh_time = 5):
    hw.temperature_set(fSet)
    hw.temperature_enable(True)
    if plot_refresh_time != 0:
        hw.temperature_enable_plot(True, plot_refresh_time)

    hw.board_set_temp_led(eBlinkStatusType.BlinkSlow)

    fTemp = hw.sensor_temperature_get()

    while abs(fTemp - fSet) > TEMP_ACCURACY:
        time.sleep(1)
        fTemp = hw.sensor_temperature_get()

    hw.board_set_temp_led(eBlinkStatusType.BlinkOn)

    datalog.set_temperature(fSet)

    # Wait if required
    if lSettleSec > 0:
        ws.info("Wait for settle time: {0} sec".format(lSettleSec))
        time.sleep(lSettleSec)

    return True


__pdoc__["do_temperature_off"]=False
def do_temperature_off(fRoomTemp = ROOM_TEMPERATURE, lSettleSec = 0):

    hw.temperature_set(fRoomTemp)
    hw.board_set_temp_led(eBlinkStatusType.BlinkSlow)

    fTemp = hw.sensor_temperature_get()
    while abs(fTemp - fRoomTemp) > TEMP_ACCURACY:
        time.sleep(1)
        fTemp = hw.sensor_temperature_get()

    hw.board_set_temp_led(eBlinkStatusType.BlinkOff)

    datalog.set_temperature(fTemp)

    hw.temperature_enable(False)
    hw.temperature_enable_plot(False, 0)

    if lSettleSec > 0:
        time.sleep(lSettleSec)


__pdoc__["do_calib"]=False
def do_calib(ch_list, ce_list, data_rate_mhz):
    res = True

    device.init_test()

    hw.set_datarate(data_rate_mhz)

    for ch in ch_list:
        hw.select_channel(ch)
        for ce in ce_list:
            lun = 0
            device.calib_setup(data_rate_mhz, 256, ce, lun)
            ws.info("DQ Calib Ch: {0} CE: {1} In progress ....".format(ch, ce))
            device.select_channel(ch)
            res &= hw.calibrate(ce, device.PAGE_LENGTH, False)
            logger.log_calib_param(ch, ce, data_rate_mhz)

    return res


###################################################
### Basic Functions
###################################################

def dq_calib_brick(segment):
    """
    This function is mapped to the `DqCalib` recipe brick of the `Basic Functions` category.\n
    It is used to align the DQ pin in order to operate at high frequency. Writing and reading page buffer of the NAND chip,
    the function looks for best DQ pin alignment.\n
    The calibration parameters can be different for each CE, so it is mandatory to calibrate all CE we are going to test. \n
    At the end of the test, the function logs the calibrate windows in FPGA taps units. Increasing the data rate the window decreases.
    In Commands panel, it is possible to show a graphical view of the windows using the command `cat CalibReport.txt`. \n
    For HS system, a windows of 300 taps are expected around 800 MTs .\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the channels to calibrate
    - Ces (recipe param): the chip enables to calibrate
    - DataRateMHz (recipe param): the data rate in MHz (max value is 2000 for HS20, 1600MHz for HS16, 800MHz for STD Rev 2, 600 MHz for STD Rev1).\n

    """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    data_rate_mhz = segment.variables("DataRateMHz").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    if do_calib(ch_list, ce_list, data_rate_mhz):
        ws.info("Calibration done")
    else:
        raise Exception("Calibration fails")


def set_power_brick(segment):
    """
    This function is mapped to the `SetPower` recipe brick of the `Basic Functions` category.\n
    It is used to change the level of VCC and VCCQ and to control VPP.\n
    In case of VPP is greater or equal 10V, the VPP relay is closed and device.enable_vpp method is called
    in order to apply the specific device vpp configuration.\n

    The recipe parameters (from the segment input object) are:\n
    - VCC (recipe param): VCC voltage level.
    - VCCQ (recipe param): VCCQ voltage level.
    - VPP (recipe param): VPP voltage level.

    """

    vcc = segment.variables("VCC").value()
    vccq = segment.variables("VCCQ").value()
    vpp = segment.variables("VPP").value()

    device.set_power_supply(vcc, vccq, vpp)


def turn_on_brick(segment):
    """
    This function is mapped to the `TurnOn` recipe brick of the `Basic Functions` category.\n
    It is used to turn the device on after a power off. It can be called only after a power off\n
    in order to apply a specific device vpp configuration.\n

    The recipe parameters (from the segment input object) are:\n
    - VCC (recipe param): VCC voltage level.
    - VCCQ (recipe param): VCCQ voltage level.

    """

    vcc = segment.variables("VCC").value()
    vccq = segment.variables("VCCQ").value()

    device.init_test()
    device.turn_on(vcc, vccq)


def turn_off_brick(segment):
    """
    This function is mapped to the `TurnOff` recipe brick of the `Basic Functions` category.\n
    It is used to turn off the device. Sometimes it is used to switch the device off and bake it at a specific temperature for some time.\n
    """
    device.init_test()
    device.turn_off()


def device_identify_brick(segment):
    """
    This function is mapped to the `DeviceIdentify` recipe brick of the `Basic Functions` category.\n
    It is used to configure the device after turn on and read the device identifier used for logging and database linking.\n
    Compared with `device_config_brick`, it just read the device identification, it doesn't turn on the device and it doesn't change
    the device options.
    """
    device.init_test(log_set={LOG_SET_ITEM.tRnB})
    device.identification()


def reset_brick(segment):
    """
    This function is mapped to the `Reset` recipe brick of the `Basic Functions` category.\n
    It is used to apply the reset command to selected channels and chips.\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to reset.
    - Ces (recipe param): the list of chip enables to reset.
    - reset_cmd (recipe param): the reset command to apply, usually 0xFF.

    """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    reset_cmd = segment.variables("ResetCmd").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    device.device_reset(ch_list, ce_list, reset_cmd)

def zq_calib_long_brick(segment):
    """
        This function is mapped to the `ZQCalibLong` recipe brick of the `Basic Functions` category.\n
        It is used to apply ZQ Calib Long ONFI sequence (F9h) to the selected channels, chips and luns.\n
        The operation is performed in parallel between channels and chips and sequentially for each lun.\n
        At the end of the test, the function logs the tZQCL (busy time after calibration command).\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for lun in lun_list:
        device.zq_calib_long(ch_list, ce_list, lun)


def zq_calib_short_brick(segment):
    """
        This function is mapped to the `ZQCalibShort` recipe brick of the `Basic Functions` category.\n
        It is used to apply ZQ Calib Short ONFI sequence (D9h) to the selected channels, chips and luns.\n
        The operation is performed in parallel between channels and chips and sequentially for each lun.\n
        At the end of the test, the function logs the tZQCS (busy time after calibration command).\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.

    """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for lun in lun_list:
        device.zq_calib_short(ch_list, ce_list, lun)


def set_feature_brick(segment):
    """
        This function is mapped to the `SetFeature` recipe brick of the `Basic Functions` category.\n
        It is used to apply Set Feature ONFI sequence (EFh) to the selected channels and chips.\n
        The operation is performed in parallel between channels and chips.\n
        Each parameter is applied with a DQS pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the applied values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()

    addr = segment.variables("address").value()
    p1 = segment.variables("p1").value()
    p2 = segment.variables("p2").value()
    p3 = segment.variables("p3").value()
    p4 = segment.variables("p4").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    device.set_feature(ch_list, ce_list, addr, p1, p2, p3, p4)


def set_feature_by_lun_brick(segment):
    """
        This function is mapped to the `SetFeatureByLun` recipe brick of the `Basic Functions` category.\n
        It is used to apply Set Feature by Lun ONFI sequence (D5h) to the selected channels, chips and luns.\n
        The operation is performed in parallel between channels and chips and sequentially by luns.\n
        Each parameter is applied with a DQS pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the applied values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()

    addr = segment.variables("address").value()
    p1 = segment.variables("p1").value()
    p2 = segment.variables("p2").value()
    p3 = segment.variables("p3").value()
    p4 = segment.variables("p4").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for lun in lun_list:
        device.set_feature_by_lun(ch_list, ce_list, lun, addr, p1, p2, p3, p4)


def set_feature_async_brick(segment):
    """
        This function is mapped to the `SetFeatureAsync` recipe brick of the `Basic Functions` category.\n
        It is used to apply Set Feature Async ONFI sequence (EFh) to the selected channels and chips.\n
        The operation is performed in parallel between channels and chips.\n
        Each parameter is applied with a WE pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the applied values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()

    addr = segment.variables("address").value()
    p1 = segment.variables("p1").value()
    p2 = segment.variables("p2").value()
    p3 = segment.variables("p3").value()
    p4 = segment.variables("p4").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    device.set_feature_async(ch_list, ce_list, addr, p1, p2, p3, p4)


def set_feature_by_lun_async_brick(segment):
    """
        This function is mapped to the `SetFeatureByLunAsync` recipe brick of the `Basic Functions` category.\n
        It is used to apply Set Feature by Lun ONFI sequence (D5h) to the selected channels, chips and luns.\n
        The operation is performed in parallel between channels and chips and sequentially by luns.\n
        Each parameter is applied with a WE pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the applied values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()

    addr = segment.variables("address").value()
    p1 = segment.variables("p1").value()
    p2 = segment.variables("p2").value()
    p3 = segment.variables("p3").value()
    p4 = segment.variables("p4").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for lun in lun_list:
        device.set_feature_by_lun_async(ch_list, ce_list, lun, addr, p1, p2, p3, p4)

    return


def get_feature_brick(segment):
    """
        This function is mapped to the `GetFeature` recipe brick of the `Basic Functions` category.\n
        It is used to apply Get Feature ONFI sequence (EEh) to the selected channels and chips.\n
        The operation is performed in parallel between channels and sequentially for each chips.\n
        Each parameter is read with an RE pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the read values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()

    addr = segment.variables("address").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for ce in ce_list:
        hw.select_ce(ce)
        device.get_feature(ch_list, ce, addr)

    return


def get_feature_by_lun_brick(segment):
    """
        This function is mapped to the `GetFeatureByLun` recipe brick of the `Basic Functions` category.\n
        It is used to apply Get Feature by Lun ONFI sequence (D4h) to the selected channels, chips and luns.\n
        The operation is performed in parallel between channels and sequentially for each chip and luns.\n
        Each parameter is read with an RE pulse.\n
        At the end of the test, the function logs the tFEAT (ready busy time after command) and the applied values.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.
        - address (recipe param): the feature address to set.
        - p1 (recipe param): first byte parameter.
        - p2 (recipe param): second byte parameter.
        - p3 (recipe param): third byte parameter.
        - p4 (recipe param): fourth byte parameter.

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()

    addr = segment.variables("address").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            device.get_feature_by_lun(ch_list, ce, lun, addr)


def delay_brick(segment):
    """
    This function is mapped to the `Delay` recipe brick of the `Basic Functions` category.\n
    It is used to pause the test in a specific condition for a certain time.\n

    The recipe parameters (from the segment input object) are:\n
    - DD (recipe param): the number of days to wait.
    - HH (recipe param): the number of hours to wait.
    - MM (recipe param): the number of minutes to wait.
    - SS (recipe param): the number of seconds to wait.
    The function will wait for DD days plus HH hours plus MM minutes plus SS seconds.\n
    If you want to wait for 2 minutes you can use DD=0, HH=0, MM=2, SS=0, but also DD=0, HH=0, MM=0, SS=120 is a valid setting.\n
    The brick is used if you want to wait after a test brick, for example if you want to wait after the program before applying the read,
    or if you want to wait a certain time at a specific temperature set point.
    Instead, if you want to add a delay inside the cycling algorithm, in this case you need to add a specific call to 'time.sleep' in the cycling_brick Python method.

    """

    dd = segment.variables("DD").value()
    hh = segment.variables("HH").value()
    mm = segment.variables("MM").value()
    ss = segment.variables("SS").value()

    try:

        sec_delay = 5

        for d in range(dd):
            for h in range(24):
                for m in range(60):
                    for s in range(0, 60, sec_delay):
                        time.sleep(sec_delay)
                        if ws.is_stop_request():
                            Exception("Delay Stopped")

        for h in range(hh):
            for m in range(60):
                for s in range(0, 60, sec_delay):
                    time.sleep(sec_delay)
                    if ws.is_stop_request():
                        Exception("Delay Stopped")

        for m in range(mm):
            for s in range(0, 60, sec_delay):
                time.sleep(sec_delay)
                if ws.is_stop_request():
                    Exception("Delay Stopped")

        for s in range(ss):
            time.sleep(1)
            if ws.is_stop_request():
                Exception("Delay Stopped")

    except Exception:
        ws.info("Delay has been stopped")


def delay_with_read_brick(segment):
    """
    This function is mapped to the `Delay_withRead` recipe brick of the `Special Functions` category.\n
    It is used to pause the test in a specific condition for a certain time, and do some pages read without output.\n

    The recipe parameters (from the segment input object) are:\n
    - DD (recipe param): the number of days to wait.
    - HH (recipe param): the number of hours to wait.
    - MM (recipe param): the number of minutes to wait.
    - SS (recipe param): the number of seconds to wait.
    - timeToRead: the Periodic time(seconds) to read all the pages
    - CHs: The read channels
    - CEs: The read CEs
    - LUNs: The Read LUNs
    - Blocks: The Read Blocks
    - Pages: The Read pages

    The function will wait for DD days plus HH hours plus MM minutes plus SS seconds.\n
    If you want to wait for 2 minutes you can use DD=0, HH=0, MM=2, SS=0, but also DD=0, HH=0, MM=0, SS=120 is a valid setting.\n
    The brick is used if you want to wait after a test brick, for example if you want to wait after the program before applying the read,
    or if you want to wait a certain time at a specific temperature set point.
    Instead, if you want to add a delay inside the cycling algorithm, in this case you need to add a specific call to 'time.sleep' in the cycling_brick Python method.

    """

    # Get flow parameters
    dd = segment.variables("DD").value()
    hh = segment.variables("HH").value()
    mm = segment.variables("MM").value()
    ss = segment.variables("SS").value()
    timeToRead = segment.variables("timeToRead").value()

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}

    try:
        for timeTmp in range(0,(((dd * 24 + hh) * 60) + mm) * 60 + ss,timeToRead):
            delayStartT = hw.get_nsec_time()
            do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list,page_list=page_list,
                        log_enable=False, log_set=read_log_set)

            delayEndT = hw.get_nsec_time()
            readTimeSec = (delayEndT - delayStartT) / 1000000000

            if readTimeSec > timeToRead:
                ws.error("Read Time " + str(readTimeSec) + " is larger than timeToRead " + str(timeToRead))
                return
            else:
                sec_delay = timeToRead - readTimeSec
                time.sleep(sec_delay)
                if ws.is_stop_request():
                    Exception("Delay Stopped")

    except Exception:
        ws.info("Delay has been stopped")



def page_buffer_test_brick(segment):
    """
    This function is mapped to the `PageBufferTest` recipe brick of the `Special Functions` category.\n
    It is used to test the interface speed, (0 fail each read cycle are expected).\n
    The test writes the page buffer and it compares the content N times. The second write operation can be disabled and the test just write one time and read N times.\n
    The recipe parameters (from the segment input object) are:\n
        - CHs: The channels to test
        - CEs: The CEs to test
        - Cycles: Number or Write/Read operation
        - ForceReWrite: if not zero enable write at each cycle

    """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    cycles = segment.variables("Cycles").value()
    force_rewrite = segment.variables("ForceReWrite").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}
    device.init_test(True, read_log_set)

    for ce in ce_list:
        hw.select_ce(ce)

        startT = hw.get_nsec_time()

        for cycle in range(cycles):
            if force_rewrite or cycle == 0:
                for ch in ch_list:
                    hw.select_channel(ch)
                    hw.set_pattern(ePatternType.Random, 0, cycle)  # random pattern
                device.page_buffer_write(ch_list, [ce], [0], 0, 0, 0)

            device.page_buffer_compare(ch_list, ce, 0, 0, 0, 0, device.PAGE_LENGTH)

        endT = hw.get_nsec_time()

        ws.info("Page Buffer Test - Elapsed Time {0:.3f} msec".format((endT - startT) / 1000000))

        for ch in ch_list:
            hw.select_channel(ch)
            fails = device.get_fails_count(ch)
            if fails > 0:
                ws.warning("Page Buffer Compare CH: {0} - CE: {1} - Fails: {2}".format(ch, ce, fails))
            else:
                ws.info("Page Buffer Compare CH: {0} - CE: {1} - Fails: {2}".format(ch, ce, fails))


###################################################
### SequenceViewer
###################################################

def enable_sequence_recording_brick(segment):
    """
        This function is mapped to the `EnableSequenceRecording` recipe brick of the `Sequence Viewer` category.\n
        It is used to start recording of applied nand sequence.\n
        From that moment each sequence started will create a seq file with the name of the section plus the channel and a sequential number.
        For Example MySection-0-0.seq. Be careful if you repeat the enable_sequence_recording_brickwith the same section name the seq file will be overwritten. \n
        This enabling is gated by test library setting disable_sequence_recording. See: `TestLibrary.initialize_brick`. \n
        The sequence file can be exported in the the PC and it can be converted in a VCD file for visualization.\n

        The recipe parameters (from the segment input object) are:\n
        - Section Name (recipe param): This parameter is used to mark with specific prefix the next generated sequence file.

    """
    section = segment.variables("Section Name").value()

    if not test_library_settings.disable_sequence_recording:
        hw.sequence_recording_enable(True, section)

def disable_sequence_recording_brick(segment):
    """
        This function is mapped to the `DisableSequenceRecording` recipe brick of the `Sequence Viewer` category.\n
        It is used to stop recording of applied nand sequence.\n

    """

    hw.sequence_recording_enable(False, "")


###################################################
### Temperature
###################################################

def temperature_on_brick(segment):
    """
        This function is mapped to the `TemperatureOn` recipe brick of the `Temperature` category.\n
        It is used to start the temperature regulation at a specific set point. After the set point is reached, the test waits for the settle time.\n
        Using Plot Refresh Time, it is possible to start temperature plotting; if the parameter is 0, the charting will be skipped.

        The recipe parameters (from the segment input object) are:\n
        - Set Point (recipe param): Temperature Set point.
        - Settle Time [sec] (recipe param): Stabilization time, waiting time when the set point is reached.
        - Plot Refresh Time [sec] (recipe param): Temperature plot refresh time, if zero the plot is disabled.

    """

    set_point = segment.variables("Set Point").value()
    settle_time_sec = segment.variables("Settle Time [sec]").value()
    plot_refresh_time_sec = segment.variables("Plot Refresh Time [sec]").value()

    do_temperature_on(set_point, settle_time_sec, plot_refresh_time_sec)


def temperature_off_brick(segment):
    """
        This function is mapped to the `TemperatureOff` recipe brick of the `Temperature` category.\n
        It is used to return to room temperature (30°C) and to stop the temperature regulation.

        The recipe parameters (from the segment input object) are:\n
        - Settle Time [sec] (recipe param): Stabilization time, waiting time when the Off temperature is reached.

    """
    settle_time_sec = segment.variables("Settle Time [sec]").value()
    
    fRoomTemperature = ROOM_TEMPERATURE
    do_temperature_off(fRoomTemperature, settle_time_sec)

def data_retention_brick(segment):
    """
        This function is mapped to the `DataRetention` recipe brick of the `Temperature` category.\n
        It is used to turn off the device and wait certain time at specific temperature. \n
        At the end of the stress time the temperature is restored and the device is initialized with default condition.\n

        The recipe parameters (from the segment input object) are:\n
        - Temp On Set Point (recipe param): On Temperature Set point for stress.
        - Temp On Settle Time [sec] (recipe param): Stabilization time, waiting time when the On set point is reached.
        - Temp Off Set Point (recipe param): Off Temperature Set point after the stress.
        - Temp Off Settle Time [sec] (recipe param): Stabilization time, waiting time when the Off set point is reached.
        - Plot Refresh Time [sec] (recipe param): Temperature plot refresh time, if zero the plot is disabled.

        See also:  `TestLibrary.temperature_on_brick` `TestLibrary.temperature_off_brick`

    """
    temp_on_set_point = segment.variables("Temp On Set Point").value()
    temp_on_settle_time_sec = segment.variables("Temp On Settle Time [sec]").value()
    temp_off_set_point = segment.variables("Temp Off Set Point").value()
    temp_off_settle_time_sec = segment.variables("Temp Off Settle Time [sec]").value()
    plot_refresh_time_sec = segment.variables("Plot Refresh Time [sec]").value()

    device.init_test()
    device.turn_off()

    do_temperature_on(temp_on_set_point, temp_on_settle_time_sec, plot_refresh_time_sec)
    if temp_off_set_point <= 30:
        fRoomTemperature = ROOM_TEMPERATURE
        do_temperature_off(fRoomTemperature, temp_off_settle_time_sec)
    else:
        do_temperature_on(temp_off_set_point, temp_off_settle_time_sec, plot_refresh_time_sec)

    device.init_test(log_set={LOG_SET_ITEM.tRnB})
    device.turn_on(device.VCC, device.VCCQ)
    device.identification()


__pdoc__["temp_measure"]=False
def temp_measure(ch_list: [], ce_list: [], plot, fSetPoint, fX):
    pSerieS = "SetPoint"
    pSerieSensor = "Sensor"

    fSensorTemp = hw.sensor_temperature_get()

    plot.add(fX, fSetPoint, pSerieS)
    plot.add(fX, fSensorTemp, pSerieSensor)

    datalog.set_counter(fX)
    for ce in ce_list:
        device.read_internal_temperature(ch_list, ce)
        for ch in ch_list:
            dev_serie = "Ch{0}_Ce{1}".format(ch, ce)
            dev_temp = device.get_last_device_temperature(ch)
            plot.add(fX, dev_temp, dev_serie)

    # flush plot and datalog
    plot.flush()

    return fSensorTemp


def temp_profiler_brick(segment):
    """
        This function is mapped to the `TempProfiler` recipe brick of the `Composite Functions` category.\n
        It is used to apply a thermal stress to the device. In particular, a list of temperatures will be applied for a
        specified time.\n
        During the test, a chart is shown with 2 series: one for the set point temperature and one for the measured temperature on the bridge sensor.\n
        If the device implements the read_internal_temperature method, the datalog file will contain the temperature read for each selected channel and chip.\n
        After the last set point, the temperature is restored to the room temperature (30.0 °C).

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test.
        - Ces (recipe param): the list of chips to test.
        - Settle Time [sec] (recipe param): Stabilization time, waiting time when the set point is reached.
        - Temps (recipe param): the list of temperature to apply, See: `TestLibrary.parse_list` for list syntax

    """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    lSettle = segment.variables("Settle Time [sec]").value()
    temps = segment.variables("Temps").value()
    temp_list = parse_list("TEMP", temps, 130)

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    fRoomTemp = ROOM_TEMPERATURE
    segmentName = str(segment.name())
    plotName = "TempProfiler " + segmentName

    thePlot = nanocycler.plot()
    thePlot.open(plotName, "Time", "Temp", "Sensor")
    ws.info("TempProfiler")
    hw.temperature_enable(True)
    hw.board_set_temp_led(eBlinkStatusType.BlinkSlow)

    step = 0
    stoprequest = False
    for iCurrentTemp in temp_list:
        ws.info("Set Temperature: " + str(iCurrentTemp))
        hw.temperature_set(float(iCurrentTemp))
        fTempBridge = hw.sensor_temperature_get()

        t1 = hw.get_nsec_time() / 1e9
        while (abs(fTempBridge - float(iCurrentTemp)) > TEMP_ACCURACY) and not stoprequest:
            if ws.is_stop_request():
                stoprequest = True
            fX = step * TEMP_PROFILER_INTERVAL
            fTempBridge = temp_measure(ch_list, ce_list, thePlot, float(iCurrentTemp), fX)
            step = step + 1
            time.sleep(TEMP_PROFILER_INTERVAL)

        t2 = hw.get_nsec_time() / 1e9
        elapsed = t2 - t1
        ws.info("Temperature {0} reached in {1:.2f} seconds".format(iCurrentTemp, elapsed))
        datalog.add_data("ElapsedTime", "{0:.2f}".format(elapsed))

        ws.info("Settle: " + str(lSettle) + " [sec]")
        if stoprequest:
            break

        for i in range(0, lSettle, TEMP_PROFILER_INTERVAL):
            if ws.is_stop_request():
                stoprequest = True

            if stoprequest:
                break

            fX = step * TEMP_PROFILER_INTERVAL
            temp_measure(ch_list, ce_list, thePlot, float(iCurrentTemp), fX)
            step = step + 1
            time.sleep(TEMP_PROFILER_INTERVAL)

    fCurrentTemp = fRoomTemp
    ws.info("Set Room Temperature: " + str(fCurrentTemp))
    hw.temperature_set(fCurrentTemp)
    fTempBridge = hw.sensor_temperature_get()

    t1 = hw.get_nsec_time() / 1e9
    while (abs(fTempBridge - fRoomTemp) > TEMP_ACCURACY) and not stoprequest:
        if ws.is_stop_request():
            stoprequest = True

        fX = step * TEMP_PROFILER_INTERVAL
        fTempBridge = temp_measure(ch_list, ce_list, thePlot, fCurrentTemp, fX)
        step = step + 1
        time.sleep(TEMP_PROFILER_INTERVAL)

    t2 = hw.get_nsec_time() / 1e9
    elapsed = t2 - t1
    ws.info("Temperature {0} reached in {1:.2f} seconds".format(fRoomTemp, elapsed))
    datalog.add_data("ElapsedTime", "{0:.2f}".format(elapsed))

    hw.board_set_temp_led(eBlinkStatusType.BlinkOff)
    hw.temperature_enable(False)

    # close plot
    thePlot.close()

def good_block_cycling_brick(segment):
    """
    This function is mapped to the `GoodBlockCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Program Erase (PE) Cycling to the selected pages of a good block. Optionally it is possible to perform read out as well.\n
    For each selected channel, chip and lun, the library identifies N good blocks to test. The test is not executed in parallel because different chips can have different good blocks.
    For testing good blocks in parallel please see: `TestLibrary.select_good_block_list_brick`\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips and luns and sequentially by block and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference, this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - Cycles (recipe param): the number of PE cycles for each selected page.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - FirstGoodBlockIndex (recipe param): this is the index of the first block to start searching.
    - GoodBlockNumber (recipe param): number of good blocks to test for each channel, chip and lun.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles".
    - readout_enable (recipe param): If different from 0, it enables readout with log period.

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    first_block = segment.variables("FirstGoodBlockIndex").value()
    block_number = segment.variables("GoodBlockNumber").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    erase_pmu_algo = PMU_ALGO.Icc3
    erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    prog_pmu_algo = PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    read_pmu_algo = PMU_ALGO.IccQ4r
    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
                    LOG_SET_ITEM.FAILS4CHUNK}

    for cycle in range(0, cycles):
        log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))
        for ch in ch_list:
            for ce in ce_list:
                hw.select_ce(ce)
                for lun in lun_list:
                    block_tested = 0
                    for block in range(first_block, device.BLOCK_NUMBER):
                        if data_mgr.is_bad_block(ch, ce, lun, block):
                            continue

                        ws.info("Cycle: {0} - Test Block CH{1}-CE{2}-L{3}-B{4}".format(cycle, ch, ce, lun, block))

                        # Erase
                        do_erase(ch_list=[ch], ce_list=[ce], lun_list=[lun], block_list=[block], cycle=cycle,
                                log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo)

                        # Program
                        do_program(ch_list=[ch], ce_list=[ce], lun_list=[lun], block_list=[block], page_list=page_list,
                                   cycle=cycle,
                                   log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo)

                        if dwell_time:
                            time.sleep(dwell_time)

                        # Readout during PE
                        if log_enable and readout_enable:
                            do_read_out(ch_list=[ch], ce_list=[ce], lun_list=[lun], block_list=[block],
                                        page_list=page_list, cycle=cycle,
                                        log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo)

                        block_tested += 1

                        if block_tested == block_number:
                            break


def good_block_multi_plane_cycling_brick(segment):
    """
    This function is mapped to the `MultiPlaneGoodBlockCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply multi plane Program Erase (PE) Cycling to the selected pages of a good multi block range.
    Optionally, it is possible to perform multi plane read out as well.\n
    For each selected channel, chip and lun, the library identifies N good blocks for multi plane test.
    The test is not executed in parallel because different chip can have different good blocks.
    For testing good blocks in parallel, please see: `TestLibrary.select_good_block_list_brick`\n
    Datalog can be enabled periodically; datalog takes time, so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips and luns and sequentially by block and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time, we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - Cycles (recipe param): the number of PE cycles for each selected page.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - FirstGoodBlockIndex (recipe param): this is the index of the first block to start searching.
    - GoodBlockNumber (recipe param): number of good blocks to test for each channel, chip and lun.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles".
    - readout_enable (recipe param): If different from 0, it enables readout with log period.

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    first_block = segment.variables("FirstGoodBlockIndex").value()
    block_number = segment.variables("GoodBlockNumber").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    erase_pmu_algo = PMU_ALGO.Icc3
    erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    prog_pmu_algo = PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    read_pmu_algo = PMU_ALGO.IccQ4r
    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
                    LOG_SET_ITEM.FAILS4CHUNK}

    for cycle in range(0, cycles):
        log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))
        for ch in ch_list:
            for ce in ce_list:
                hw.select_ce(ce)
                for lun in lun_list:
                    block_tested = 0
                    for block in range(first_block, device.BLOCK_NUMBER, device.PLANE_NUMBER):

                        block_list = []
                        for b in range(block, block + device.PLANE_NUMBER):
                            if not data_mgr.is_bad_block(ch, ce, lun, b) and len(
                                    block_list) + block_tested < block_number:
                                block_list.append(b)

                        if len(block_list) == 0:
                            continue

                        ws.info("Cycle: {0} - Test Blocks CH{1}-CE{2}-L{3}-B{4}".format(cycle, ch, ce, lun, block_list))

                        if close_partial_block == False:
                            # Erase
                            do_multi_plane_erase(ch_list=[ch], ce_list=[ce], lun_list=[lun], list_block_list=[block_list],
                                                cycle=cycle,
                                                log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo)

                        # Program
                        do_multi_plane_program(ch_list=[ch], ce_list=[ce], lun_list=[lun], list_block_list=[block_list],
                                               page_list=page_list, cycle=cycle,
                                               log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo, close_partial_block=close_partial_block)

                        if dwell_time:
                            time.sleep(dwell_time)

                        # Readout during PE
                        if log_enable and readout_enable:
                            do_multi_plane_read_out(ch_list=[ch], ce_list=[ce], lun_list=[lun],
                                                    list_block_list=[block_list], page_list=page_list, cycle=cycle,
                                                    log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo)

                        block_tested += len(block_list)

                        if block_tested == block_number:
                            break


def cycling_brick(segment):
    """
    This function is mapped to the `Cycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips and luns and sequentially by block and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference, this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - Cycles (recipe param): the number of PE cycles for each selected page.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

     """
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    read_ber_plot = None
    if readout_enable:
        read_ber_plot = nanocycler.plot()
        read_ber_plot.openext(make_unique_plot_name(segment.name(), "Read BER"), "Cycle", enumScaleType.Numerical, "0.",
                              False, "%", enumScaleType.Numerical, "0.000", False, "")

    erase_current_plot = None
    if hw.is_nanocycler_hs() and readout_enable:
        erase_current_plot = nanocycler.plot()
        erase_current_plot.open(make_unique_plot_name(segment.name(), "Block Erase (Icc)"), "usec", "mA", "")

    erase_pmu_algo = PMU_ALGO.Icc3
    erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
                     LOG_SET_ITEM.PmuSamples}

    prog_pmu_algo = PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    read_pmu_algo = PMU_ALGO.IccQ4r
    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
                    LOG_SET_ITEM.FAILS4CHUNK}

    for cycle in range(0, cycles):
        startT = hw.get_nsec_time()

        log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))

        # Erase
        do_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, cycle=cycle,
                log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                current_plot=erase_current_plot)

        # Program
        do_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                   cycle=cycle, log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo)

        if dwell_time:
            time.sleep(dwell_time)

        # Readout during PE
        if log_enable and readout_enable:
            #series = "BER"  not used
            do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                        cycle=cycle, log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo, ber_plot=read_ber_plot)

        bStop = ws.is_stop_request()
        if bStop:
            ws.warning("Stop Requested")
            break

        endT = hw.get_nsec_time()
        ws.info("PE-Cycling {0} - Elapsed Time {1:.3f} msec".format(cycle, (endT - startT) / 1000000))

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()

    if erase_current_plot is not None:
        erase_current_plot.close()
        erase_current_plot.cleanup()

    return


def read_out_brick(segment):
    """
    This function is mapped to the `ReadOut` recipe brick of the `Basic Function` category.\n
    It is used to read and compare the selected pages with an expected pattern. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tR (ready busy time after each read page command), the fails and fails for chunk, the I-Avg and I-Max.
    ICCQ4 read has been selected as default.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..
    - File (recipe param): It is used to select the pattern file to compare with. It is used only if stored pattern is User Buffer.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()

    if file_name:
        device.fill_pattern_buffer(file_name)

    datalog.set_label("Default Read")

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    # Option A) 0,1,6-8 ==> 0,1,6,7,8
    # Option B) ALL ==> 0,1,2,...,PART_NUMBER-1
    page_list = parse_page_list(pages)

    read_pmu_algo = None
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}  # , LOG_SET_ITEM.FAILS
    
    # read_pmu_algo = PMU_ALGO.IccQ4r # ICCQ4 read as default
    # read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
    #                 LOG_SET_ITEM.FAILS4CHUNK}  # , LOG_SET_ITEM.FAILS

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                page_list=page_list, log_set=read_log_set, pmu_algo=read_pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                    log_set=read_log_set, pmu_algo=read_pmu_algo)

def open_block_read_out_brick(segment):
    """
    Eric: pages is a read list, like [0-10][20-600]

    This function is mapped to the `ReadOut` recipe brick of the `Basic Function` category.\n
    It is used to read and compare the selected pages with an expected pattern. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tR (ready busy time after each read page command), the fails and fails for chunk, the I-Avg and I-Max.
    ICCQ4 read has been selected as default.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..
    - File (recipe param): It is used to select the pattern file to compare with. It is used only if stored pattern is User Buffer.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()

    if file_name:
        device.fill_pattern_buffer(file_name)

    datalog.set_label("Default Read")

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    list_page_list = parse_multi_page_list(pages)

    read_pmu_algo = None
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}  # , LOG_SET_ITEM.FAILS
    
    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_openblk_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                list_page_list=list_page_list, log_set=read_log_set, pmu_algo=read_pmu_algo)
    # else:
    #     block_list = parse_block_list(blocks)
    #     do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
    #                 log_set=read_log_set, pmu_algo=read_pmu_algo)

def stdr_read_out_brick(segment):
    """
    This function is mapped to the `ReadOut` recipe brick of the `Basic Function` category.\n
    It is used to read and compare the selected pages with an expected pattern. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tR (ready busy time after each read page command), the fails and fails for chunk, the I-Avg and I-Max.
    ICCQ4 read has been selected as default.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..
    - File (recipe param): It is used to select the pattern file to compare with. It is used only if stored pattern is User Buffer.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()

    if file_name:
        device.fill_pattern_buffer(file_name)

    datalog.set_label("Default Read")

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    read_pmu_algo = None #PMU_ALGO.IccQ4r # ICCQ4 read as default
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}  # , LOG_SET_ITEM.FAILS

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)

        pre_time = 0
        for dwell_time in [2,4,6,8,10,12,18,24,30,36,42,48,54,60,66,72,84,96]:
            ws.info('{}hrs DR start...'.format(dwell_time))
            time.sleep((dwell_time-pre_time) * 3600)
            pre_time = dwell_time
            for i in range(1,3):
                datalog.set_segment(0, str(i) + 'th_' + str(segment.name()) + '_' + str(dwell_time) + 'hrs')
                do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                        page_list=page_list, log_set=read_log_set, pmu_algo=read_pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        # page = []
        # for pg in range(4,28):
        #     page.append(pg)

        # for pg in range(1568,1604):
        #     page.append(pg)

        ws.info("Scan page: {}".format(page_list))
        i,n = 0,0
        startT_dr = hw.get_nsec_time()
        endT_dr = hw.get_nsec_time()
        while (endT_dr - startT_dr)/1000000000 <= 96*3600:
            datalog.set_segment(0, str(i) + 'th_' + 'loop')
            ws.info('{}th Loop'.format(i))
            
            if n < len(page_list):
                page = page_list[n]
            else:
                n = 0
                page = page_list[n]

            do_page_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page=page, cycle=i,
                        log_set=read_log_set, pmu_algo=read_pmu_algo)

            i = i + 1

            endT_dr = hw.get_nsec_time()



def multi_read_out_brick(segment):
    """
    This function is mapped to the `ReadOut` recipe brick of the `Basic Function` category.\n
    It is used to read and compare the selected pages with an expected pattern. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tR (ready busy time after each read page command), the fails and fails for chunk, the I-Avg and I-Max.
    ICCQ4 read has been selected as default.\n
    The recipe parameters are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..
    - File (recipe param): It is used to select the pattern file to compare with. It is used only if stored pattern is User Buffer.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

                 Parameters:
                         segment: The recipe segment object to access the test parameters
     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    file_name = segment.variables("File").value()

    if file_name:
        device.fill_pattern_buffer(file_name)

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    # read_pmu_algo = PMU_ALGO.IccQ4r # ICCQ4 read as default
    # read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
    #                 LOG_SET_ITEM.FAILS4CHUNK}
    
    read_pmu_algo = None # ICCQ4 read as default
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}

    for cycle in range(0, cycles):
        datalog.set_segment(0, str(segment.name()) + '_' + str(cycle + 1))
        datalog.set_label("Default Read")

        if o_str == "MP":
            list_block_list = parse_multi_block_list(blocks)
            do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                page_list=page_list, log_set=read_log_set, pmu_algo=read_pmu_algo)
        else:
            block_list = parse_block_list(blocks)
            do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                        log_set=read_log_set, pmu_algo=read_pmu_algo)


def erase_brick(segment):
    """
    This function is mapped to the `Erase` recipe brick of the `Basic Function` category.\n
    It is used to erase the selected blocks.
    During the test, for each block library logs the tERS (ready busy time after each erase command), the Icc3Avg, Icc3Max and Icc3Samples.
    It also logs the Status Register after each block erase.\n ICC3 read has been selected as default.
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`


     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    pmu_algo = PMU_ALGO.Icc3 # ICC read as default
    erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
                     LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                             log_set=erase_log_set, pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, log_set=erase_log_set, pmu_algo=pmu_algo)

__pdoc__["do_multi_plane_erase_suspend_test"]=False
def do_multi_plane_erase_suspend_test(ch_list: [], ce_list: [], lun_list: [], list_block_list: [[]], suspend_times,
                                      suspend_cmd_delay,
                                      ch_list_read: [], ce_list_read: [], lun_list_read: [], block_list_read: [],
                                      page_list_read: []):
    
    fast_read_pmu_algo = PMU_ALGO.NoPmu
    fast_read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}

    for block_list in list_block_list:

        # random time from nano second timer
        delay = hw.get_nsec_time() % suspend_cmd_delay

        do_multi_plane_erase_suspend(ch_list, ce_list, lun_list, block_list, 0, True, delay)

        for cycle in range(suspend_times):
            do_fast_read(ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read, cycle=cycle,
                         log_enable=True, log_set=fast_read_log_set, pmu_algo=fast_read_pmu_algo)

            do_multi_plane_erase_resume(ch_list, ce_list, lun_list, block_list, cycle, True,
                                        delay if cycle < suspend_times - 1 else -1)



__pdoc__["do_erase_suspend_test"]=False
def do_erase_suspend_test(ch_list: [], ce_list: [], lun_list: [], block_list: [], suspend_times, suspend_cmd_delay,
                          ch_list_read: [], ce_list_read: [], lun_list_read: [], block_list_read: [],
                          page_list_read: []):

    fast_read_pmu_algo = PMU_ALGO.NoPmu
    fast_read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}

    for block in block_list:

        # random time from nano second timer
        delay = hw.get_nsec_time() % suspend_cmd_delay

        do_erase_suspend(ch_list, ce_list, lun_list, block, 0, True, delay)

        for cycle in range(suspend_times):
            do_fast_read(ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read,
                         cycle=cycle, log_enable=True, log_set=fast_read_log_set, pmu_algo=fast_read_pmu_algo)

            do_erase_resume(ch_list, ce_list, lun_list, block, cycle, True,
                            delay if cycle < suspend_times - 1 else -1)


def erase_suspend_brick(segment):
    """
    This function is mapped to the `EraseSuspend` recipe brick of the `Special` category.\n
    For each selected block, the library applies the erase command and, after a random delay, an erase suspend command.
    After the suspend, a fast read (or standard read if fast is not implemented) is performed on selected pages.
    After the read the erase resume command is applied.
    The sequence erase suspend, fast read, resume is repeated N times.
    After the last resume the erase is completed.
    The algorithm can be executed in single or multi plane mode.

    By default, the library logs:\n
    - For Erase Suspend and Resume: For each block: tERS, SR
    - Read: For each page FAILS, FAILS4CHUNK

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels for erase suspend and resume.
    - Ces (recipe param): the list of chips for erase suspend and resume.
    - Luns (recipe param): the list of luns for erase suspend and resume.
    - Blocks (recipe param): the list of blocks for erase suspend and resume.

    - CHsRead (recipe param): the list of channels for reading during erase suspend.
    - CesRead (recipe param): the list of chips for reading during erase suspend.
    - LunsRead (recipe param): the list of luns for reading during erase suspend.
    - BlocksRead (recipe param): the list of blocks for reading during erase suspend.
    - PagesRead (recipe param): the list of pages for reading during erase suspend.

    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation..
    - SuspendCmdDelay (recipe param): delay parameter to control the time between erase and suspend (as default is a random number between 0 and this value).
    - SuspendTimes (recipe param): number of suspends (consequently of read operation) to perform.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    suspend_cmd_delay = segment.variables("SuspendCmdDelay").value()
    suspend_times = segment.variables("SuspendTimes").value()
    chs_read = segment.variables("CHsRead").value()
    ces_read = segment.variables("CesRead").value()
    luns_read = segment.variables("LunsRead").value()
    blocks_read = segment.variables("BlocksRead").value()
    pages_read = segment.variables("PagesRead").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    ch_list_read = parse_ch_list(chs_read)
    ce_list_read = parse_ce_list(ces_read)
    lun_list_read = parse_lun_list(luns_read)
    block_list_read = parse_block_list(blocks_read)
    page_list_read = parse_page_list(pages_read)

    if suspend_times <= 0:
        raise Exception("Suspend Times must be greater than 0")  # exception

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_erase_suspend_test(ch_list, ce_list, lun_list, list_block_list, suspend_times, suspend_cmd_delay,
                                          ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read)
    else:
        block_list = parse_block_list(blocks)
        do_erase_suspend_test(ch_list, ce_list, lun_list, block_list, suspend_times, suspend_cmd_delay,
                              ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read)


__pdoc__["do_program_suspend_test"]=False
def do_program_suspend_test(ch_list, ce_list, lun_list, block_list, suspend_page_list, suspend_times, suspend_cmd_delay,
                            ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read):

    fast_read_pmu_algo = PMU_ALGO.NoPmu
    fast_read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}

    for block in block_list:

        device.select_pattern(ch_list, ce_list, lun_list, [block], test_library_settings.pattern_algo)

        for page in suspend_page_list:

            # random time from nano second timer
            delay = hw.get_nsec_time() % suspend_cmd_delay

            if not do_program_page_suspend(ch_list, ce_list, lun_list, block, page, 0, True, delay):
                continue

            for cycle in range(suspend_times):
                do_fast_read(ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read,
                             cycle=cycle, log_enable=True, log_set=fast_read_log_set, pmu_algo=fast_read_pmu_algo)

                do_program_resume(ch_list, ce_list, lun_list, block, page, cycle, True,
                                  delay if cycle < suspend_times - 1 else -1)
                ws.info("Program Resumed on Page: {0} - Times: {1}".format(page, cycle))


__pdoc__["do_multi_plane_program_suspend_test"]=False
def do_multi_plane_program_suspend_test(ch_list, ce_list, lun_list, list_block_list, suspend_page_list, suspend_times,
                                        suspend_cmd_delay,
                                        ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read):

    fast_read_pmu_algo = PMU_ALGO.NoPmu
    fast_read_log_set = {LOG_SET_ITEM.FAILS, LOG_SET_ITEM.FAILS4CHUNK}

    for block_list in list_block_list:

        device.select_pattern(ch_list, ce_list, lun_list, block_list, test_library_settings.pattern_algo)

        for page in suspend_page_list:

            # random time from nano second timer
            delay = hw.get_nsec_time() % suspend_cmd_delay

            if not do_multi_plane_program_page_suspend(ch_list, ce_list, lun_list, block_list, page, 0, True, delay):
                continue

            for cycle in range(suspend_times):
                do_fast_read(ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read,
                             cycle=cycle, log_enable=True, log_set=fast_read_log_set, pmu_algo=fast_read_pmu_algo)

                do_multi_plane_program_resume(ch_list, ce_list, lun_list, block_list, page, cycle, True,
                                              delay if cycle < suspend_times - 1 else -1)
                ws.info("Program Resumed on Page: {0} - Times: {1}".format(page, cycle))


def program_suspend_brick(segment):
    """
    This function is mapped to the `ProgramSuspend` recipe brick of the `Special` category.\n
    For each selected pages (for some device not all the page can be suspended) the library applies the program command and, after a random delay, a program suspend command.
    After the suspend, a fast read (or standard read if fast is not implemented) is performed on selected pages.
    After the read, the program resume command is applied.
    The sequence program suspend, fast read, program resume is repeated N times.
    After the last resume the program is completed.
    The algorithm can be executed in single or multi plane mode.

    By default, the library logs:\n
    - For Program Suspend and Resume: For each page: tERS, SR
    - Read: For each page FAILS, FAILS4CHUNK

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels for program suspend and resume.
    - Ces (recipe param): the list of chips for program suspend and resume.
    - Luns (recipe param): the list of luns for program suspend and resume.
    - Blocks (recipe param): the list of blocks for program suspend and resume.
    - SuspendPages (recipe param): the list of pages for program suspend and resume.

    - CHsRead (recipe param): the list of channels for reading during program suspend.
    - CesRead (recipe param): the list of chips for reading during program suspend.
    - LunsRead (recipe param): the list of luns for reading during program suspend.
    - BlocksRead (recipe param): the list of blocks for reading during program suspend.
    - PagesRead (recipe param): the list of pages for reading during program suspend.

    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - SuspendCmdDelay (recipe param): delay parameter to control the time between program and suspend (as default is a random number between 0 and this value).
    - SuspendTimes (recipe param): number of suspends (consequently of read operation) to perform.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    suspend_pages = segment.variables("SuspendPages").value()
    suspend_cmd_delay = segment.variables("SuspendCmdDelay").value()
    suspend_times = segment.variables("SuspendTimes").value()
    chs_read = segment.variables("CHsRead").value()
    ces_read = segment.variables("CesRead").value()
    luns_read = segment.variables("LunsRead").value()
    blocks_read = segment.variables("BlocksRead").value()
    pages_read = segment.variables("PagesRead").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    suspend_page_list = parse_page_list(suspend_pages)

    ch_list_read = parse_ch_list(chs_read)
    ce_list_read = parse_ce_list(ces_read)
    lun_list_read = parse_lun_list(luns_read)
    block_list_read = parse_block_list(blocks_read)
    page_list_read = parse_page_list(pages_read)

    if suspend_times <= 0:
        raise Exception("Suspend Times must be greater than 0")  # exception

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_program_suspend_test(ch_list, ce_list, lun_list, list_block_list, suspend_page_list,
                                            suspend_times, suspend_cmd_delay,
                                            ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read)
    else:
        block_list = parse_block_list(blocks)
        do_program_suspend_test(ch_list, ce_list, lun_list, block_list, suspend_page_list, suspend_times,
                                suspend_cmd_delay,
                                ch_list_read, ce_list_read, lun_list_read, block_list_read, page_list_read)


def program_brick(segment):
    """
    This function is mapped to the `Program` recipe brick of the `Basic Function` category.\n
    It is used to program selected pages with a random or a user pattern. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tPRG (ready busy time after each program page command), the status register and the I-Avg and I-Max.
    ICCQ4 read has been selected as default.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - File (recipe param): if not empty, the content of the binary file will be used to program the block.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    pattern_algo = test_library_settings.pattern_algo
    if file_name:
        device.fill_pattern_buffer(file_name)
        pattern_algo = PATTERN_ALGO.USER

    pmu_algo= {PMU_ALGO.Icc2} # as default measure ICC2
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                               page_list=page_list,
                               log_set=prog_log_set, pattern_algo=pattern_algo, pmu_algo=pmu_algo, close_partial_block=close_partial_block)
    else:
        block_list = parse_block_list(blocks)
        do_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                   log_set=prog_log_set, pattern_algo=pattern_algo, pmu_algo=pmu_algo)

def multi_plane_EP_hang_brick(segment):
    """
    This function is mapped to the `MultiPlaneFastCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Multiplane Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform multiplane read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips, luns and one block group and sequentially for each block group and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - Cycles (recipe param): the number of PE cycle for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    qlc_program_order = segment.variables("ByWL/Sawtooth_QLCProg").value()
    cycles = segment.variables("Cycles").value()
    cyc_commonisor = segment.variables("Commonisor").value()
    num_block = segment.variables("NumBlocks").value()
    block_freq = segment.variables("BlockFreq").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    cycle_list = parse_list("CYCLE", cycles, 100000) # cycles = 3, 3 -> cycle_list = [3, 3]
    list_block_list = parse_multi_block_list(blocks) # blocks = [40-43][500-503] -> list_block_list = [[40,41,42,43], [500, 501,502,503]]
    # page_list = parse_page_list(pages)
    list_page_list = parse_multi_page_list(pages)

    pec_count = [int(x/cyc_commonisor) for x in cycle_list]
    cycle = [0] * len(cycle_list)

    read_ber_plot = None

    erase_current_plot = None

    erase_pmu_algo = None # PMU_ALGO.Icc3
    erase_log_set = {LOG_SET_ITEM.tRnB}

    prog_pmu_algo = None # PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB}

    for pec_step in range(0, cyc_commonisor):
        startT = hw.get_nsec_time()

        for pec_len in range(len(pec_count)):

            block_group = list_block_list[pec_len] # list_block_list = [[40,41,42,43], [500, 501,502,503]]

            ch_used = []
            if len(pec_count) <5:
                ch_used = ch_list
            else:
                ch_used.append(ch_list[pec_len//4])

            if len(list_page_list) == 1:
                page_list = list_page_list[0]
            else:
                page_list = list_page_list[pec_len]
            
            for pec in range(pec_count[pec_len]):

                cycle[pec_len] = pec_step * pec_count[pec_len] + pec + 1

                log_enable = (log_mode_cycle > 0) and (((cycle[pec_len]) == 1) or ((cycle[pec_len] % log_mode_cycle) == 0) or (cycle[pec_len] == cycle_list[pec_len]))

                ws.info(">>> Condiiton_Group = {0}, CH {1}, Total Cycle = {2}, Current_cycle = {3}".format(pec_len, ch_used, cycle_list[pec_len], cycle[pec_len]))

                for blk_num in range(num_block):

                    blk_list = []
                    list_block_list_new = []

                    if block_group[0] == 4:
                        for blk in block_group:
                            if blk_num < 3:
                                blk_list.append(blk + blk_num * block_freq)
                            elif blk_num < 5:
                                blk_list.append(blk + blk_num * block_freq + 16)
                            else:   
                                blk_list.append(blk + blk_num * block_freq + 20)
                    else:
                        for blk in block_group:                   
                            if blk_num < 5: # hang, 其他NAND需要根据endurance block进行修改
                                blk = blk_num * block_freq + blk
                            else:
                                blk = blk_num * block_freq + blk + 4
                            blk_list.append(blk)
                    
                    list_block_list_new.append(blk_list)

                    if close_partial_block==False:
                        do_multi_plane_erase(ch_list=ch_used, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                            cycle=cycle[pec_len], log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                                            current_plot=erase_current_plot)

                    do_multi_plane_program(ch_list=ch_used, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                        page_list=page_list, cycle=cycle[pec_len], log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo,
                                        close_partial_block=close_partial_block,qlc_program_order=qlc_program_order)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break

        endT = hw.get_nsec_time()
        ws.info("MP-Cycling {0} - Elapsed Time {1:.3f} sec\n".format(cycle, (endT - startT) / 1e9))
        ws.info("**********************************************")

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()

    if erase_current_plot is not None:
        erase_current_plot.close()
        erase_current_plot.cleanup()

    return

def read_disturb_brick(segment):
    """
    This function is mapped to the `ReadDisturb` recipe brick of the `Composite Functions` category.\n
    It is used to apply a read stress to the selected pages.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, and sequentially for chips, luns, block and pages.\n

    By default, when enabled the library logs:\n
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling.
    In order to reduce the test time we suggest to remove the plot and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - ReadCycles (recipe param): the number of read cycles for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("ReadCycles").value()
    log_mode_cycle = segment.variables("LogModCyc").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)


    read_ber_plot = nanocycler.plot()
    read_ber_plot.open(make_unique_plot_name(segment.name(), "Block Read BER (%)"), "Cycle", "%", "Page")

    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
                    LOG_SET_ITEM.FAILS4CHUNK}

    for cycle in range(0, cycles):
        startT = hw.get_nsec_time()

        log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))

        do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                    log_enable=log_enable, log_set=read_log_set, ber_plot=read_ber_plot, cycle=cycle)

        endT = hw.get_nsec_time()
        ws.info("ReadDisturb {0} - Elapsed Time {1:.3f} msec".format(cycle, (endT - startT) / 1000000))

    read_ber_plot.close()
    read_ber_plot.cleanup()

    return


def multi_plane_cycling_brick(segment):
    """
    This function is mapped to the `MultiPlaneCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Multiplane Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform multiplane read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips, luns and one block group and sequentially for each block group and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - Cycles (recipe param): the number of PE cycle for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    list_block_list = parse_multi_block_list(blocks)
    page_list = parse_page_list(pages)

    read_ber_plot = None
    if readout_enable:
        read_ber_plot = nanocycler.plot()
        read_ber_plot.openext(make_unique_plot_name(segment.name(), "Block MP-Read BER"), "Cycle",
                              enumScaleType.Numerical, "0.", False, "%", enumScaleType.Numerical, "0.000", False, "")

    erase_current_plot = None
    if hw.is_nanocycler_hs() and readout_enable:
        erase_current_plot = nanocycler.plot()
        erase_current_plot.open(make_unique_plot_name(segment.name(), "Block MP-Erase (Icc)"), "usec", "mA", "")


    # erase_pmu_algo = PMU_ALGO.Icc3
    # erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
    #                  LOG_SET_ITEM.PmuSamples}

    # prog_pmu_algo = PMU_ALGO.Icc2
    # prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    # read_pmu_algo = PMU_ALGO.IccQ4r
    # read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
    #                 LOG_SET_ITEM.FAILS4CHUNK}

    erase_pmu_algo = None
    erase_log_set = {LOG_SET_ITEM.tRnB}

    prog_pmu_algo = None
    prog_log_set = {LOG_SET_ITEM.tRnB}

    read_pmu_algo = None
    read_log_set = {LOG_SET_ITEM.tRnB}

    for cycle in range(0, cycles):
        startT = hw.get_nsec_time()

        log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))

        if close_partial_block == False:
            do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                cycle=cycle, log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                                current_plot=erase_current_plot)
            
        do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                               page_list=page_list, cycle=cycle, log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo, 
                               close_partial_block=close_partial_block, qlc_program_order='Sawtooth')   #hang, 20250326

        if dwell_time:
            time.sleep(dwell_time)

        # Readout during PE
        if log_enable and readout_enable:
            #series = "BER"  not used
            do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list,
                                    list_block_list=list_block_list, page_list=page_list,
                                    cycle=cycle, log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
                                    ber_plot=read_ber_plot)

        bStop = ws.is_stop_request()
        if bStop:
            ws.warning("Stop Requested")
            break

        endT = hw.get_nsec_time()
        ws.info("MP-Cycling {0} - Elapsed Time {1:.3f} msec\n".format(cycle, (endT - startT) / 1000000))
        ws.info("**********************************************")

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()

    if erase_current_plot is not None:
        erase_current_plot.close()
        erase_current_plot.cleanup()


def multi_plane_fast_cycling_brick(segment):
    """
    This function is mapped to the `MultiPlaneFastCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Multiplane Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform multiplane read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips, luns and one block group and sequentially for each block group and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - Cycles (recipe param): the number of PE cycle for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    qlc_program_order = segment.variables("ByWL/Sawtooth_QLCProg").value()
    cycles = segment.variables("Cycles").value()
    cyc_commonisor = segment.variables("Commonisor").value()
    num_block = segment.variables("NumBlocks").value()
    block_freq = segment.variables("BlockFreq").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    cycle_list = parse_list("CYCLE", cycles, 100000) # cycles = 3, 3 -> cycle_list = [3, 3]
    list_block_list = parse_multi_block_list(blocks) # blocks = [40-43][500-503] -> list_block_list = [[40,41,42,43], [500, 501,502,503]]
    page_list = parse_page_list(pages)

    pec_count = [int(x/cyc_commonisor) for x in cycle_list]
    cycle = [0] * len(cycle_list)

    read_ber_plot = None
    # if readout_enable:
    #     read_ber_plot = nanocycler.plot()
    #     read_ber_plot.openext(make_unique_plot_name(segment.name(), "Block MP-Read BER"), "Cycle",
    #                           enumScaleType.Numerical, "0.", False, "%", enumScaleType.Numerical, "0.000", False, "")

    erase_current_plot = None
    # if hw.is_nanocycler_hs() and readout_enable:
    #     erase_current_plot = nanocycler.plot()
    #     erase_current_plot.open(make_unique_plot_name(segment.name(), "Block MP-Erase (Icc)"), "usec", "mA", "")


    erase_pmu_algo = None # PMU_ALGO.Icc3
    erase_log_set = {LOG_SET_ITEM.tRnB}
    # erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
    #                  LOG_SET_ITEM.PmuSamples}

    prog_pmu_algo = None # PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB}
    # prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    read_pmu_algo = None # PMU_ALGO.IccQ4r
    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.FAILS4CHUNK}
    # read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
    #                 LOG_SET_ITEM.FAILS4CHUNK}

    for pec_step in range(0, cyc_commonisor):
        startT = hw.get_nsec_time()

        for pec_len in range(len(pec_count)):

            block_group = list_block_list[pec_len] # list_block_list = [[40,41,42,43], [500, 501,502,503]]
            
            for pec in range(pec_count[pec_len]):

                cycle[pec_len] = pec_step * pec_count[pec_len] + pec + 1

                log_enable = (log_mode_cycle > 0) and (((cycle[pec_len]) == 1) or ((cycle[pec_len] % log_mode_cycle) == 0) or (cycle[pec_len] == cycle_list[pec_len]))

                ws.info(">>> PEC_Group = {0} Total Cycle = {1}, Current_cycle = {2}".format(pec_len, cycle_list[pec_len], cycle[pec_len]))

                for blk_num in range(num_block):

                    blk_list = []
                    list_block_list_new = []

                    for blk in block_group: # block_group = [40,41,42,43]

                        blk = blk_num * block_freq + blk
                        blk_list.append(blk)

                    list_block_list_new.append(blk_list) # []

                    if close_partial_block==False:
                        do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                            cycle=cycle[pec_len], log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                                            current_plot=erase_current_plot)

                    do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                        page_list=page_list, cycle=cycle[pec_len], log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo,
                                          close_partial_block=close_partial_block,qlc_program_order=qlc_program_order)
                    # Readout during PE
                    if log_enable and readout_enable:
                        series = "BER"
                        datalog.set_label("Default Read")
                        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list,
                                                list_block_list=list_block_list_new, page_list=page_list,
                                                cycle=cycle[pec_len], log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
                                                ber_plot=read_ber_plot)

                    # do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                    #                     cycle=cycle[pec_len], log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                    #                     current_plot=erase_current_plot)

                    # do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                    #                     page_list=page_list, cycle=cycle[pec_len], log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo)

                    # # Readout during PE
                    # if log_enable and readout_enable:
                    #     series = "BER"
                    #     do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list,
                    #                             list_block_list=list_block_list_new, page_list=page_list,
                    #                             cycle=cycle[pec_len], log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
                    #                             ber_plot=read_ber_plot)

                if dwell_time:
                    time.sleep(dwell_time)

                bStop = ws.is_stop_request()
                if bStop:
                    ws.warning("Stop Requested")
                    break

        endT = hw.get_nsec_time()
        ws.info("MP-Cycling {0} - Elapsed Time {1:.3f} sec\n".format(cycle, (endT - startT) / 1e9))
        ws.info("**********************************************")

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()

    if erase_current_plot is not None:
        erase_current_plot.close()
        erase_current_plot.cleanup()

    return


def multi_plane_delay_cycling_brick(segment):
    """
    This function is mapped to the `MultiPlaneDelayCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Multiplane Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform multiplane read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips, luns and one block group and sequentially for each block group and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - Cycles (recipe param): the number of PE cycle for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

                 Parameters:
                         segment: The recipe segment object to access the test parameters
     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    cycles = segment.variables("Cycles").value()
    num_block = segment.variables("NumBlocks").value()
    block_freq = segment.variables("BlockFreq").value()
    endurance_test_time = segment.variables("Endurancetime [sec]").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    cycle_list = parse_list("CYCLE", cycles, 100000)
    list_block_list = parse_multi_block_list(blocks)
    page_list = parse_page_list(pages)

    cycle = [0] * len(cycle_list)
    delay_pec = [0] * len(cycle_list)
    delay_check = [0] * len(cycle_list)

    read_ber_plot = None
    # if readout_enable:
    #     read_ber_plot = nanocycler.plot()
    #     read_ber_plot.openext(make_unique_plot_name(segment.name(), "Block MP-Read BER"), "Cycle",
    #                           enumScaleType.Numerical, "0.", False, "%", enumScaleType.Numerical, "0.000", False, "")

    erase_current_plot = None
    # if hw.is_nanocycler_hs():
    #     erase_current_plot = nanocycler.plot()
    #     erase_current_plot.open(make_unique_plot_name(segment.name(), "Block MP-Erase (Icc)"), "usec", "mA", "")

    
    # erase_pmu_algo = PMU_ALGO.Icc3
    # erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax,
    #                  LOG_SET_ITEM.PmuSamples}

    # prog_pmu_algo = PMU_ALGO.Icc2
    # prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    # read_pmu_algo = PMU_ALGO.IccQ4r
    # read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
    #                 LOG_SET_ITEM.FAILS4CHUNK}
    
    erase_pmu_algo = None
    erase_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}

    prog_pmu_algo = None
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR}

    read_pmu_algo = None
    read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.FAILS4CHUNK}

    # 10% PEC is done in the endurance test time
    for i in range(len(cycle_list)):
        delay_pec[i] = endurance_test_time/cycle_list[i]

    startT = hw.get_nsec_time()/1e9
    time_now = 0
    while time_now < endurance_test_time:

        for pec_len in range(len(cycle_list)):

            if time_now >= delay_check[pec_len]:
                cycle[pec_len] = cycle[pec_len] + 1

                ws.info('PEC_Group = {} Total Cycle {}, Curent_Cycle = {}, Time_Now = {:.2f}s, Current_Time = {:.2f}'.format(pec_len, cycle_list[pec_len], cycle[pec_len], float(time_now), (hw.get_nsec_time()/1e9-startT)))

                blk_group = list_block_list[pec_len]
                log_enable = (log_mode_cycle > 0) and (((cycle[pec_len]) == 1) or ((cycle[pec_len] % log_mode_cycle) == 0) or (cycle[pec_len] == (cycle_list[pec_len])))

                for blk_num in range(num_block):

                    blk_lisk = []
                    list_block_list_new = []

                    for blk in blk_group:
                        blk = blk_num * block_freq + blk
                        blk_lisk.append(blk)

                    list_block_list_new.append(blk_lisk)

                    if close_partial_block == False:
                        do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                            cycle=cycle[pec_len], log_enable=log_enable, log_set=erase_log_set, pmu_algo=erase_pmu_algo,
                                            current_plot=erase_current_plot)

                    do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list_new,
                                        page_list=page_list, cycle=cycle[pec_len], log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo, close_partial_block=close_partial_block)

                    # Readout during PE
                    if log_enable and readout_enable:
                        series = "BER"
                        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list,
                                                list_block_list=list_block_list_new, page_list=page_list,
                                                cycle=cycle[pec_len], log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
                                                ber_plot=read_ber_plot)

                    bStop = ws.is_stop_request()
                    if bStop:
                        ws.warning("Stop Requested")
                        break
                
                delay_check[pec_len] += delay_pec[pec_len]

        endT = hw.get_nsec_time()/1e9
        time_now = endT - startT

    ws.info("MP-Cycling {0} - Elapsed Time {1:.3f} sec\n".format(cycle, int((endT - startT))))
    ws.info("**********************************************")

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()

    if erase_current_plot is not None:
        erase_current_plot.close()
        erase_current_plot.cleanup()

    return


def multi_plane_program_brick(segment):
    """
    This function is mapped to the `MultiPlaneCycling` recipe brick of the `Composite Functions` category.\n
    It is used to apply Multiplane Program Erase (PE) Cycling to the selected pages. Optionally, it is possible to perform multiplane read out as well.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, chips, luns and one block group and sequentially for each block group and pages.\n

    By default, when enabled the library logs:\n
    - Erase: For each block: tERS, SR, Icc3Avg, Icc3Max, Icc3Samples
    - Program: For each page  tPROG, SR, Icc2Avg, Icc2Max
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling and Icc3 current waveform.
    In order to reduce the test time we recommend to remove these plots and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - DwellTime(s) (recipe param): sleep time in second after erase operation.
    - Cycles (recipe param): the number of PE cycle for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"
    - readout_enable (recipe param): If different from 0, it enables readout with log period

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    log_mode_cycle = segment.variables("LogModCyc").value()
    readout_enable = segment.variables("EnableReadOut").value()
    dwell_time = segment.variables("DwellTime(s)").value()
    close_partial_block = segment.variables("ClosePartialBlock").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    list_block_list = parse_multi_block_list(blocks)
    page_list = parse_page_list(pages)

    read_ber_plot = None
    if readout_enable:
        read_ber_plot = nanocycler.plot()
        read_ber_plot.openext(make_unique_plot_name(segment.name(), "Block MP-Read BER"), "Cycle",
                              enumScaleType.Numerical, "0.", False, "%", enumScaleType.Numerical, "0.000", False, "")

    prog_pmu_algo = PMU_ALGO.Icc2
    prog_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.SR, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax}

    startT = hw.get_nsec_time()

    cycle = 0
    log_enable = log_mode_cycle

    do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                            page_list=page_list, cycle=cycle, log_enable=log_enable, log_set=prog_log_set, pmu_algo=prog_pmu_algo, close_partial_block=close_partial_block)

    if dwell_time:
        time.sleep(dwell_time)

    # Readout during PE
    if log_enable and readout_enable:
        #series = "BER"  not used
        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list,
                                list_block_list=list_block_list, page_list=page_list,
                                cycle=cycle, log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
                                ber_plot=read_ber_plot)

    endT = hw.get_nsec_time()
    ws.info("PE-Multiplane-Cycling {0} - Elapsed Time {1:.3f} msec".format(cycle, (endT - startT) / 1000000))

    if read_ber_plot is not None:
        read_ber_plot.close()
        read_ber_plot.cleanup()


def multi_plane_read_disturb_brick(segment):
    """
    This function is mapped to the `MultiPlaneReadDisturb` recipe brick of the `Composite Functions` category.\n
    It is used to apply a multi plane read stress to the selected pages.\n
    Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
    The test is executed in parallel between channels, multi plane read command is applied for each blocks group
    then the read is performed sequentially for chips, luns and pages.\n

    By default, when enabled the library logs:\n
    - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

    As reference this code contains plotting of Bit Error Rate (BER) during cycling.
    In order to reduce the test time we suggest to remove the plot and to reduce the logged information.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - ReadCycles (recipe param): the number of read cycles for each selected page.
    - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    # Get flow parameters
    ces = segment.variables("Ces").value()
    chs = segment.variables("CHs").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value() # Default Read and Read disturb use blocks format
    o_str_retry = segment.variables("SP/MP_Retry").value()
    retry_blocks = segment.variables("Retry_Blocks").value()
    disturb_pages = segment.variables("Disturb_Pages").value() # Read disturb on RD_Pages
    read_pages = segment.variables("Read_Pages").value() # Default and Read Retry use these Read_Pages
    # partial_ratio = segment.variables("Partial_Ratio").value()
    cycles = segment.variables("RDCycles").value() # Read disturb cycles
    log_cycle = segment.variables("LogModCyc").value()
    base_cycle = segment.variables("BaseCycle").value() # Read disturb base count value
    RD_Type = segment.variables("RDType").value() # SPRD or BlockRD
    file_options = segment.variables("FileOptions").value()
    options = segment.variables("Options").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    list_block_list = parse_multi_block_list(blocks)
    if o_str_retry == "MP":
        retry_blocks_list = parse_multi_block_list(retry_blocks)
    else:
        retry_blocks_list = parse_block_list(retry_blocks)

    pp_list = []

    # if (device.DEVICE_NAME == "X39070" or device.DEVICE_NAME == "X49060"):
    #     pp_list = parse_multi_str_list(partial_ratio)
    # else:
    #     pp_list = []

    rd_page_list = parse_page_list(disturb_pages)
    read_page_list = parse_page_list(read_pages)

    datalog.set_label("Default Read")

    global read_pmu_algo, read_log_set

    read_pmu_algo = None
    read_log_set = {LOG_SET_ITEM.FAILS4CHUNK}

    startT = hw.get_nsec_time()

    log_enable = False

    do_multi_plane_read_disturb(segment, rd_type=RD_Type, ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                            o_str_retry=o_str_retry, retry_blocks_list=retry_blocks_list, rd_page_list=rd_page_list, read_page_list=read_page_list, pp_list=pp_list, cycles=cycles, log_cycle=log_cycle, base_cycle=base_cycle, log_enable=log_enable, log_set=read_log_set, file_options=file_options, options=options)

    endT = hw.get_nsec_time()

    # if (cycle+1) % 10000 == 0:
        # ws.info("MP-ReadDisturb {0} - Block {1} - Page {2} - Elapsed Time Min {3:.3f} - Max {4:.3f} msec".format((cycle+1), block_list, page, elapsed))

    ws.info("MP-ReadDisturb {0} - Elapsed Time {1:.3f} sec".format(cycles, (endT - startT)/1e9))

    return


# def multi_plane_read_disturb_brick(segment):
#     """
#     This function is mapped to the `MultiPlaneReadDisturb` recipe brick of the `Composite Functions` category.\n
#     It is used to apply a multi plane read stress to the selected pages.\n
#     Datalog can be enabled periodically; datalog takes time so it is important to select an appropriate log period.\n
#     The test is executed in parallel between channels, multi plane read command is applied for each blocks group
#     then the read is performed sequentially for chips, luns and pages.\n

#     By default, when enabled the library logs:\n
#     - Read: For each page tR, IccQ4Avg, IccQ4Max, FAILS, FAILS4CHUNK

#     As reference this code contains plotting of Bit Error Rate (BER) during cycling.
#     In order to reduce the test time we suggest to remove the plot and to reduce the logged information.

#     The recipe parameters (from the segment input object) are:\n
#     - CHs (recipe param): the list of channels to test in parallel.
#     - Ces (recipe param): the list of chip enables to test.
#     - Luns (recipe param): the list of luns to test.
#     - Blocks (recipe param): the list of blocks to test.
#     - Pages (recipe param): the list of pages to test.
#     - ReadCycles (recipe param): the number of read cycles for each selected page.
#     - LogModCyc (recipe param): If different from 0, it controls the log period. Datalog will be enabled at first cycle, then at each "log mode cycles"

#     For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
#     `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

#      """

#     # Get flow parameters
#     ces = segment.variables("Ces").value()
#     chs = segment.variables("CHs").value()
#     luns = segment.variables("Luns").value()
#     blocks = segment.variables("Blocks").value()
#     pages = segment.variables("Pages").value()
#     cycles = segment.variables("ReadCycles").value()
#     log_mode_cycle = segment.variables("LogModCyc").value()

#     ch_list = parse_ch_list(chs)
#     ce_list = parse_ce_list(ces)
#     lun_list = parse_lun_list(luns)
#     list_block_list = parse_multi_block_list(blocks)
#     page_list = parse_page_list(pages)

#     read_ber_plot = nanocycler.plot()
#     read_ber_plot.open(make_unique_plot_name(segment.name(), "Block MP-Read BER (%)"), "Cycle", "%", "Page")

#     read_pmu_algo = PMU_ALGO.IccQ4r
#     read_log_set = {LOG_SET_ITEM.tRnB, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
#                     LOG_SET_ITEM.FAILS4CHUNK}

#     for cycle in range(0, cycles):
#         startT = hw.get_nsec_time()

#         log_enable = (log_mode_cycle > 0) and (((cycle % log_mode_cycle) == 0) or (cycle == (cycles - 1)))

#         do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
#                                 page_list=page_list, cycle=cycle, log_enable=log_enable, log_set=read_log_set, pmu_algo=read_pmu_algo,
#                                 ber_plot=read_ber_plot)

#         endT = hw.get_nsec_time()
#         ws.info("MP-ReadDisturb {0} - Elapsed Time {1:.3f} msec".format(cycle, (endT - startT) / 1000000))

#     read_ber_plot.close()
#     read_ber_plot.cleanup()

#     return


def read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, o_str_retry, retry_blocks_list, file, options): # Eric
    """
    This function is mapped to the `ReadRetry` recipe brick of the `VT` category.\n
    It is used to perform the read retry algorithm on selected pages.\n
    The options can be the default option of the device, like it usually happens for Micron devices, or the options can be taken from a csv file.
    The csv file must be organized with one line for each option on N columns with one value for each code to apply.\n
    The file is read from RR folder of the test program.\n
    A plot is generated with one series or each channel, chip and lun, the x axis contains the option the Y is the Bit Error Rate.\n
    By default, the library logs the read option as datalog condition, then for each page it logs the FAILS.\n

    The recipe parameters are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - FileOptions (recipe param): the name of csv file option.
    - Options (recipe param): the number of options to apply (or the number of line of line to use as options).

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

                 Parameters:
                         segment: The recipe segment object to access the test parameters
     """


    # chs = '0'
    # # chs = '0,1'
    # ces = '0'
    # luns = '0'
    # blocks = '24-25,28-29,32-33,36-37'

    file_options = file  # RRT table .csv
    options = options # RRT entries total number: 0-?

    ch_list = ch_list
    ce_list = ce_list
    lun_list = lun_list
    block_list = retry_blocks_list
    page_list = read_page_list
    option_list = parse_list("LINES", options)

    datalog.set_label("Read Retry")

    device.fill_option_buffer(file_options)
    
    read_retry_plot = None
    # read_retry_plot = nanocycler.plot()
    if read_retry_plot is not None: 
        read_retry_plot.openext(make_unique_plot_name("RD", "Read Retry"), "RR_OPTION", enumScaleType.Qualitative,
                                "", False, "BER (%)", enumScaleType.Numerical, "0.00", False, "")

    if o_str_retry == "MP":
        do_multi_plane_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)
    
    else:
        do_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)

    if read_retry_plot is not None:
        read_retry_plot.close()
        read_retry_plot.cleanup()

    return


def open_read_retry_brick_eric(ch_list, ce_list, lun_list, read_page_list, pp_list, o_str_retry, retry_blocks_list, file, options): # Eric
    """
    This function is mapped to the `ReadRetry` recipe brick of the `VT` category.\n
    It is used to perform the read retry algorithm on selected pages.\n
    The options can be the default option of the device, like it usually happens for Micron devices, or the options can be taken from a csv file.
    The csv file must be organized with one line for each option on N columns with one value for each code to apply.\n
    The file is read from RR folder of the test program.\n
    A plot is generated with one series or each channel, chip and lun, the x axis contains the option the Y is the Bit Error Rate.\n
    By default, the library logs the read option as datalog condition, then for each page it logs the FAILS.\n

    The recipe parameters are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - FileOptions (recipe param): the name of csv file option.
    - Options (recipe param): the number of options to apply (or the number of line of line to use as options).

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

                 Parameters:
                         segment: The recipe segment object to access the test parameters
     """


    # chs = '0'
    # # chs = '0,1'
    # ces = '0'
    # luns = '0'
    # blocks = '24-25,28-29,32-33,36-37'

    file_options = file  # RRT table .csv
    options = options # RRT entries total number: 0-?

    ch_list = ch_list
    ce_list = ce_list
    lun_list = lun_list
    block_list = retry_blocks_list
    page_list = read_page_list
    pp_list = pp_list
    option_list = parse_list("LINES", options)

    datalog.set_label("Read Retry")

    device.fill_option_buffer(file_options)
    
    read_retry_plot = None
    # read_retry_plot = nanocycler.plot()
    if read_retry_plot is not None: 
        read_retry_plot.openext(make_unique_plot_name("RD", "Read Retry"), "RR_OPTION", enumScaleType.Qualitative,
                                "", False, "BER (%)", enumScaleType.Numerical, "0.00", False, "")

    if o_str_retry == "MP":
        do_multi_plane_openblk_read_retry(ch_list, ce_list, lun_list, block_list, page_list, pp_list, read_retry_plot, option_list)
    
    else:
        do_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)

    if read_retry_plot is not None:
        read_retry_plot.close()
        read_retry_plot.cleanup()

    return


def read_retry_brick(segment):
    """
    This function is mapped to the `ReadRetry` recipe brick of the `VT` category.\n
    It is used to perform the read retry algorithm on selected pages.\n
    The options can be the default option of the device, like it usually happens for Micron devices, or the options can be taken from a csv file.
    The csv file must be organized with one line for each option on N columns with one value for each code to apply.\n
    The file is read from RR folder of the test program.\n
    A plot is generated with one series or each channel, chip and lun, the x axis contains the option the Y is the Bit Error Rate.\n
    By default, the library logs the read option as datalog condition, then for each page it logs the FAILS.\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - FileOptions (recipe param): the name of csv file option.
    - Options (recipe param): the number of options to apply (or the number of line of line to use as options).

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_options = segment.variables("FileOptions").value()
    options = segment.variables("Options").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)
    option_list = parse_list("LINES", options)

    datalog.set_label("Read Retry")

    device.fill_option_buffer(file_options)

    read_retry_plot = None
    # read_retry_plot = nanocycler.plot()
    if read_retry_plot is not None: 
        read_retry_plot.openext(make_unique_plot_name(segment.name(), "Read Retry"), "RR_OPTION", enumScaleType.Qualitative,
                                "", False, "BER (%)", enumScaleType.Numerical, "0.00", False, "")

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_read_retry(ch_list, ce_list, lun_list, list_block_list, page_list, read_retry_plot, option_list)
    
    else:
        block_list = parse_block_list(blocks)
        do_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)

    if read_retry_plot is not None:
        read_retry_plot.close()
        read_retry_plot.cleanup()

    return


def open_block_read_retry_brick(segment):
    """
    This function is mapped to the `ReadRetry` recipe brick of the `VT` category.\n
    It is used to perform the read retry algorithm on selected pages.\n
    The options can be the default option of the device, like it usually happens for Micron devices, or the options can be taken from a csv file.
    The csv file must be organized with one line for each option on N columns with one value for each code to apply.\n
    The file is read from RR folder of the test program.\n
    A plot is generated with one series or each channel, chip and lun, the x axis contains the option the Y is the Bit Error Rate.\n
    By default, the library logs the read option as datalog condition, then for each page it logs the FAILS.\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - FileOptions (recipe param): the name of csv file option.
    - Options (recipe param): the number of options to apply (or the number of line of line to use as options).

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_options = segment.variables("FileOptions").value()
    options = segment.variables("Options").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    # page_list = parse_page_list(pages)
    list_page_list = parse_multi_page_list(pages)
    option_list = parse_list("LINES", options)

    datalog.set_label("Read Retry")

    device.fill_option_buffer(file_options)

    read_retry_plot = None
    # read_retry_plot = nanocycler.plot()
    if read_retry_plot is not None: 
        read_retry_plot.openext(make_unique_plot_name(segment.name(), "Read Retry"), "RR_OPTION", enumScaleType.Qualitative,
                                "", False, "BER (%)", enumScaleType.Numerical, "0.00", False, "")

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        # do_multi_plane_openblk_read_retry(ch_list, ce_list, lun_list, list_block_list, page_list, pp_list, read_retry_plot, option_list)
        do_multi_plane_openblk_read_retry(ch_list, ce_list, lun_list, list_block_list, list_page_list, read_retry_plot, option_list)   #hang
    
    else:
        ws.error("So far can't use SP mode!")
    #     block_list = parse_block_list(blocks)
    #     do_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)

    if read_retry_plot is not None:
        read_retry_plot.close()
        read_retry_plot.cleanup()

    return


# def read_retry_brick(segment):
#     """
#     This function is mapped to the `ReadRetry` recipe brick of the `VT` category.\n
#     It is used to perform the read retry algorithm on selected pages.\n
#     The options can be the default option of the device, like it usually happens for Micron devices, or the options can be taken from a csv file.
#     The csv file must be organized with one line for each option on N columns with one value for each code to apply.\n
#     The file is read from RR folder of the test program.\n
#     A plot is generated with one series or each channel, chip and lun, the x axis contains the option the Y is the Bit Error Rate.\n
#     By default, the library logs the read option as datalog condition, then for each page it logs the FAILS.\n

#     The recipe parameters (from the segment input object) are:\n
#     - CHs (recipe param): the list of channels to test in parallel.
#     - Ces (recipe param): the list of chip enables to test.
#     - Luns (recipe param): the list of luns to test.
#     - Blocks (recipe param): the list of blocks to test.
#     - Pages (recipe param): the list of pages to test.
#     - FileOptions (recipe param): the name of csv file option.
#     - Options (recipe param): the number of options to apply (or the number of line of line to use as options).

#     For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
#     `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

#      """


#     chs = segment.variables("CHs").value()
#     ces = segment.variables("Ces").value()
#     luns = segment.variables("Luns").value()
#     blocks = segment.variables("Blocks").value()
#     pages = segment.variables("Pages").value()
#     file_options = segment.variables("FileOptions").value()
#     options = segment.variables("Options").value()

#     ch_list = parse_ch_list(chs)
#     ce_list = parse_ce_list(ces)
#     lun_list = parse_lun_list(luns)
#     block_list = parse_block_list(blocks)
#     page_list = parse_page_list(pages)
#     option_list = parse_list("LINES", options)

#     datalog.set_label("Read Retry")

#     device.fill_option_buffer(file_options)

#     read_retry_plot = None
#     # read_retry_plot = nanocycler.plot()
#     if read_retry_plot is not None: 
#         read_retry_plot.openext(make_unique_plot_name(segment.name(), "Read Retry"), "RR_OPTION", enumScaleType.Qualitative,
#                                 "", False, "BER (%)", enumScaleType.Numerical,
#                                 "0.00", False, "")
  
#     do_read_retry(ch_list, ce_list, lun_list, block_list, page_list, read_retry_plot, option_list)

#     if read_retry_plot is not None:
#         read_retry_plot.close()
#         read_retry_plot.cleanup()

#     return


def read_offset_ber_brick(segment):
    """
    This function is mapped to the `ReadOffset_BER` recipe brick of the `VT` category.\n
    It is used to measure Bit Error Rate vs Read Levels and Offset.\n
    There are two version of this algorithm, one is optimized for test time (the results of all pages are collected together),
    the other collect and logs results for each pages. (This second one is currently enabled, the other code is commented, see: do_read_offset_ber)\n
    For each chip and lun, block and page, a device.set_read_offset_code is called for each selected level and offset, then the pages are read and fails are collected for each offset.
    By default, fails for page log is disabled, but a single csv line with an array of the number of fails for each offset applied is logged.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - ChartEnable (recipe param): it enables/disables chart generations.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """


    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    pattern = segment.variables("Pattern").value()
    chart_enable = segment.variables("ChartEnable").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)
    force_all1 = True if pattern == "ALL1" else False
    force_all0 = True if pattern == "ALL0" else False

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    do_read_offset_ber(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, force_all1, force_all0, chart_enable)

    return

def read_offset_in_command_ber_brick(segment):
    """
    This function is mapped to the `ReadOffset_BER_ROIC` recipe brick of the `VT` category.\n
    It is used to measure Bit Error Rate vs Read Levels and Offset; if the device support it,
    the read offset is applied by Read Offset In Command algorithm. \n
    For each chip and lun, block and page, the read_offset_roic_page_compare method is invoked in order to collact the fails for each offset value.
    By default, fails for page log is disabled, but a single csv line with an array of the number of fails for each offset applied is logged.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - ChartEnable (recipe param): it enables/disables chart generations.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """


    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    pattern = segment.variables("Pattern").value()
    chart_enable = segment.variables("ChartEnable").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)
    force_all1 = True if pattern == "ALL1" else False
    force_all0 = True if pattern == "ALL0" else False

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    do_read_offset_in_command_ber(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, force_all1, force_all0, chart_enable)

    return

def read_offset_best_brick(segment):
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    pattern = segment.variables("Pattern").value()
    log_dac = segment.variables("LogDac").value()
    log_fails = segment.variables("LogFails").value()

    cycle = 0

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)
    force_all1 = True if pattern == "ALL1" else False
    force_all0 = True if pattern == "ALL0" else False

    datalog.set_segment(0, segment.name())
    datalog.set_label("READ_OFFSET_BEST")
    datalog.set_function("READ")
    datalog.set_counter(cycle)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    do_read_offset_best(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac)

    return

def read_offset_best_2_brick(segment):
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    pattern = segment.variables("Pattern").value()
    log_dac = segment.variables("LogDac").value()
    log_fails = segment.variables("LogFails").value()

    cycle = 0

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)
    force_all1 = True if pattern == "ALL1" else False
    force_all0 = True if pattern == "ALL0" else False

    datalog.set_segment(0, segment.name())
    datalog.set_label("READ_OFFSET_BEST")
    datalog.set_function("READ")
    datalog.set_counter(cycle)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    do_read_offset_best_2(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac)

    return

def read_offset_fitting_best_brick(segment):
    """
    This function is mapped to the `ReadOffset_Fitting_BEST` recipe brick of the `VT` category.\n
    It is used to measure best read offset by fitting several read point FBC (eg.: default read level ± 20 dacs with 2 dac step).\n

    Follow the steps below:
    1. For each chip, lun block and page a device.set_read_offset_code is called, then do page read,
    2. recall program pattern and read buffer to calculate the FBC pattern, 
    3. calculate the FBC of each state by the algorithm in the function do_read_offset_fitting_best, the FBC value will be stored in the list
    4. fitiing the FBC list to find the best FBC value for each state, and get the best offset value


    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - ChartEnable (recipe param): it enables/disables chart generations.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    num_block = segment.variables("NumBlocks").value()
    block_freq = segment.variables("BlockFreq").value()
    max_pp_per_ch = segment.variables("MaxPPPerCH").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    pattern = segment.variables("Pattern").value()
    log_dac = segment.variables("LogDac").value()
    log_fails = segment.variables("LogFails").value()
    

    cycle = 0

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    # block_list = parse_block_list(blocks)
    list_block_list = parse_multi_block_list(blocks)
    # page_list = parse_page_list(pages)
    list_page_list = parse_multi_page_list(pages)
    force_all1 = True if pattern == "ALL1" else False
    force_all0 = True if pattern == "ALL0" else False

    datalog.set_segment(0, segment.name())
    # datalog.set_label("Fitting_Best")
    datalog.set_function("READ_Offset")
    datalog.set_counter(cycle)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    list_ch_list = []
    if len(list_block_list) < max_pp_per_ch+1:
        for i in range(len(list_block_list)):
            list_ch_list.append(ch_list)
    else:
        for i in range(len(list_block_list)):
            list_ch_list.append(list(i // max_pp_per_ch))

    for i in range(len(list_block_list)):

        base_block_list = list_block_list[i]

        ch_used = list_ch_list[i]

        if len(list_page_list) == 1:
            page_list = list_page_list[0]
        else:
            page_list = list_page_list[i]

        for j in range(num_block):
            block_list = []
            for block in base_block_list:
                block_list.append(block + j * block_freq)
        
            do_read_offset_fitting_best(ch_used, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, force_all1, force_all0, log_fails, log_dac)
    
    return

def read_offset_vth_brick(segment):
    """
    This function is mapped to the `ReadOffset_Vth` recipe brick of the `VT` category.\n
    It is used to measure VTH vs Read Levels and Offset.\n

    For each chip, lun block and page a device.set_read_offset_code is called for each selected level and offset, then
    device.read_offset_page_compare is invoked to calculate the number of fails. This value is logged in the .csv file
    and it is used to generate the VTH chart.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - ChartEnable (recipe param): it enables/disables chart generations.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    chart_enable = segment.variables("ChartEnable").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))
    
    do_read_offset_vth(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac, chart_enable)


def vth_read_brick(segment):
    """
    This function is mapped to the `ReadOffset_VT` recipe brick of the `VT` category.\n
    This is a special test (usually not documented) implemented at the moment only for Hynix and Sandisk devices.\n
    Using device.vt_page_compare it is possible to generate a chart like this:
    .. image:: ./images/VTH-read.png

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.
    - Pattern (recipe param): fails will be calculated versus expected pattern (read from database if possible), ALL1 or AL00
    - ChartEnable (recipe param): it enables/disables chart generations.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """
    o_str = segment.variables("SP/MP").value()
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    wls = segment.variables("WLs").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    # wl_list = parse_page_list(wls)
    

    if o_str == "MP":
        wl_list = parse_multi_wl_list(wls)  #当只有一个[]时，返回一个列表，当有多个[]时，返回一个列表的列表
        ws.info("Vt WL: {}".format(wl_list))

        # Check if wl_list is a list of lists or a simple list
        if wl_list and isinstance(wl_list[0], list):
            # Handle case when wl_list contains multiple lists (multiple groups)
            for wl_index in range(len(wl_list)):
                block_list_new = []
                for i in range(6):
                    if block_list[wl_index] == 4:
                        if i < 3:
                            block_list_new.append(i*8 + block_list[wl_index])
                        elif i < 5:
                            block_list_new.append(i*8 + block_list[wl_index] + 16)
                        else:
                            block_list_new.append(i*8 + block_list[wl_index] + 20)
                    else:
                        if i < 5:
                            block_list_new.append(i*8 + block_list[wl_index])
                        else:
                            block_list_new.append(i*8 + block_list[wl_index] + 4)
                
                wl_group = wl_list[wl_index]

                ch_used = []
                if len(wl_list) < 4:    #this number is for X36070
                    ch_used = ch_list
                else:
                    ch_used.append(ch_list[wl_index // 4])

                ws.info("Vth read, CH: {0} Block: {1} WL: {2}".format(ch_used, block_list_new, wl_group))
                do_vth_read_randWL(ch_used, ce_list, lun_list, block_list_new, wl_group, start_dac, end_dac, step_dac)
        else:
            # Handle case when wl_list is a simple list (single group)
            do_vth_read_randWL(ch_list, ce_list, lun_list, block_list, wl_list, start_dac, end_dac, step_dac)

    else:
        wl_list = parse_wl_list(wls)
        do_vth_read(ch_list, ce_list, lun_list, block_list, wl_list, start_dac, end_dac, step_dac)


def read_offset_vth_dump_brick(segment):
    """
    This function is mapped to the `ReadOffset_Vth_Dump` recipe brick of the `VT` category.\n
    It is used to measure page content vs Read Levels and Offset. The file measured can be processed by NplusT VT Analysis Python script in order to generate chart like this:\n
    .. image:: ./images/BarnieVT.png
    In order to process the files with NplusT VTAnalysis.py 3 linked TLC pages must be selected.\n

    The recipe parameters (from the segment input object) are:\n
    - CH (recipe param): the channel to measure.
    - Ce (recipe param): the chip to measure.
    - Lun (recipe param): the lun to measure.
    - Block (recipe param): the block to measure.
    - Pages (recipe param): the list of 3 TLC pages to measure.
    - DacStart (recipe param): this is the dac Start value to scan read offset.
    - DacEnd (recipe param): this is the dac End value to scan read offset.
    - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.
    - ExpectedFile (recipe param): if not empty, this is the name of the file to create with expected content

     """
    ch = int(segment.variables("CH").value())
    ce = int(segment.variables("Ce").value())
    lun = int(segment.variables("Lun").value())
    block = int(segment.variables("Block").value())
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = segment.variables("DacStep").value()
    vt_file_name = segment.variables("VTFile").value()
    file_name = segment.variables("ExpectedFile").value()

    page_list = parse_page_list(pages)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))

    if file_name:
        do_dump_expected(file_name, [ch], [ce], [lun], [block], page_list)

    # now measure the VT
    do_read_offset_vth_dump(vt_file_name, ch, ce, lun, block, page_list, start_dac, end_dac, step_dac)


def read_offset_dump_brick(segment):
    """
        This function is mapped to the `ReadOffset_Dump` recipe brick of the `VT` category.\n
        It is used to save in a file the page content for each Read Levels and Offset.
        A file for each block is generated. The file contains for each page, for each valid level the page content at different dac value.\n

        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to test in parallel.
        - Ces (recipe param): the list of chip enables to test.
        - Luns (recipe param): the list of luns to test.
        - Blocks (recipe param): the list of blocks to test.
        - Pages (recipe param): the list of pages to test.
        - DacStart (recipe param): this is the dac Start value to scan read offset.
        - DacEnd (recipe param): this is the dac End value to scan read offset.
        - DacStep (recipe param): this is the dac Step to scan read offset from DacStart to DacEnd.

         """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    start_dac = int(segment.variables("DacStart").value())
    end_dac = int(segment.variables("DacEnd").value())
    step_dac = int(segment.variables("DacStep").value())

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    if end_dac < start_dac:
        raise Exception("start_dac {0} is larger than end_dac {1}".format(start_dac,end_dac))

    do_read_offset_dump(ch_list, ce_list, lun_list, block_list, page_list, start_dac, end_dac, step_dac)


def bad_block_brick(segment):
    """
    This function is mapped to the `BadBlockTest` recipe brick of the `Special` category.\n
    It is used to verify if a block is marked as Factory bad block. Using device specific code it verifies if a block is marked as bad; if so, the
    bad block mark will be stored inside the NanoCycler database.

    During the test, for each block library logs the info whether the block is good or bad.

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)

    for ce in ce_list:
        hw.select_ce(ce)
        for lun in lun_list:
            for block in block_list:
                device.is_bad_block(ch_list, ce, lun, block)


# def read_dump_brick(segment):
#     """
#     This function is mapped to the `ReadDump` recipe brick of the `Special` category.\n
#     It is used to read and dump to a single file the content of all selected pages.
#     On file for each selected pages is generated.\n
#
#     The recipe parameters (from the segment input object) are:\n
#     - CHs (recipe param): the list of channels to test in parallel.
#     - Ces (recipe param): the list of chip enables to test.
#     - Luns (recipe param): the list of luns to test.
#     - Blocks (recipe param): the list of blocks to test.
#     - Pages (recipe param): the list of pages to test.
#     - File (recipe param): It is used to select the output file name to store the content of the pages.\n
#
#     For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
#     `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`
#
#                  Parameters:
#                          segment: The recipe segment object to access the test parameters
#     """
#     chs = segment.variables("CHs").value()
#     ces = segment.variables("Ces").value()
#     luns = segment.variables("Luns").value()
#     blocks = segment.variables("Blocks").value()
#     pages = segment.variables("Pages").value()
#     file_name = segment.variables("File").value()
#
#     ch_list = parse_ch_list(chs)
#     ce_list = parse_ce_list(ces)
#     lun_list = parse_lun_list(luns)
#     block_list = parse_block_list(blocks)
#     page_list = parse_page_list(pages)
#
#     for ch in ch_list:
#         for ce in ce_list:
#            hw.select_ce(ce)
#             for lun in lun_list:
#                 for block in block_list:
#                     for page in page_list:
#                         fw = nanocycler.binaryfilewriter(tes)
#                         fw.open("CH{0}-CE{1}-L{2}-B{3}-P{4}-{5}".format(ch, ce, lun, block, page, file_name))
#                         device.page_read([ch], ce, lun, block, page, 0, device.PAGE_LENGTH)
#                         buffer = device.get_read_buffer(ch, device.PAGE_LENGTH)
#                         fw.write(buffer, device.PAGE_LENGTH)
#                         fw.close()
#                         fw.cleanup()
#
#     return

def read_dump_brick(segment):
    """
    This function is mapped to the `ReadDump` recipe brick of the `Special` category.\n
    It is used to read and dump to a single file the content of all selected pages.
    On file for each selected pages is generated.\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.
    - File (recipe param): It is used to select the output file name to store the content of the pages.\n

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_block_list` `TestLibrary.parse_page_list`

    """
    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    for ch in ch_list:
        for ce in ce_list:
            hw.select_ce(ce)
            fw = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
            fw.open("CH{0}-CE{1}-{2}".format(ch, ce, file_name))
            for lun in lun_list:
                for block in block_list:
                    for page in page_list:
                        device.page_read([ch], ce, lun, block, page, 0, device.PAGE_LENGTH)
                        buffer = device.get_read_buffer(ch, device.PAGE_LENGTH)
                        # logger.log_buffer("READ_BUFFER", ch, ce, [lun], [block], [page], buffer)    #hang, add for log read buffer
                        fw.write(buffer, device.PAGE_LENGTH)
            fw.close()
            fw.cleanup()

    return


# def expected_dump_brick(segment):
#     """
#        This function is mapped to the `ExpectedDump` recipe brick of the `Special` category.\n
#        It is used to create a file with the expected content of selected pages.
#        The one page for each selected pages is generated. \n
#
#     The recipe parameters (from the segment input object) are:\n
#        - CHs (recipe param): the list of channels to test in parallel.
#        - Ces (recipe param): the list of chip enables to test.
#        - Luns (recipe param): the list of luns to test.
#        - Blocks (recipe param): the list of blocks to test.
#        - Pages (recipe param): the list of pages to test.
#        - File (recipe param): the name of the file to create.
#
#        For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
#        `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`
#
#                     Parameters:
#                             segment: The recipe segment object to access the test parameters
#         """
#
#     chs = segment.variables("CHs").value()
#     ces = segment.variables("Ces").value()
#     luns = segment.variables("Luns").value()
#     blocks = segment.variables("Blocks").value()
#     pages = segment.variables("Pages").value()
#     file_name = segment.variables("File").value()
#
#     ch_list = parse_ch_list(chs)
#     ce_list = parse_ce_list(ces)
#     lun_list = parse_lun_list(luns)
#     block_list = parse_block_list(blocks)
#     page_list = parse_page_list(pages)
#
#     buffer = bytearray(device.PAGE_LENGTH)
#
#     for ch in ch_list:
#         for ce in ce_list:
#             hw.select_ce(ce)
#             for lun in lun_list:
#                 for block in block_list:
#                     for page in page_list:
#                         expected_fw = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
#                         expected_fw.open("CH{0}-CE{1}-L{2}-B{3}-P{4}-{5}".format(ch, ce, lun, block, page, file_name))
#
#                         pattern, seed = device.expected_pattern_fpga_codes(ch, ce, lun, block, page)
#                         if pattern == ePatternType.All1:
#                             for i in range(device.PAGE_LENGTH):
#                                 buffer[i] = 0xFF
#                         elif pattern == ePatternType.All0:
#                             for i in range(device.PAGE_LENGTH):
#                                 buffer[i] = 0x00
#                         elif pattern == ePatternType.Counter:
#                             for i in range(device.PAGE_LENGTH):
#                                 buffer[i] = i & 0xFF
#                         elif pattern == ePatternType.Alt_FF_00:
#                             for i in range(0,device.PAGE_LENGTH,2):
#                                 buffer[i] = 0xFF
#                                 buffer[i+1] = 0x00
#                         elif pattern == ePatternType.Alt_55_AA:
#                             for i in range(0,device.PAGE_LENGTH,2):
#                                 buffer[i] = 0x55
#                                 buffer[i+1] = 0xAA
#                         elif pattern == ePatternType.Random:
#                             res, buffer = hw.random_fill_expected_buffer(seed, 0, device.PAGE_LENGTH)
#                         elif pattern == ePatternType.User:
#                             start_pos = page * device.PAGE_LENGTH
#                             buffer = device.pattern_buffer[start_pos:start_pos + device.PAGE_LENGTH]
#                         expected_fw.write(buffer, device.PAGE_LENGTH)
#                         expected_fw.close()
#                         expected_fw.cleanup()
#
#     return

__pdoc__["do_dump_expected"]=False
def do_dump_expected(file_name, ch_list, ce_list, lun_list, block_list, page_list):

    buffer = bytearray(device.PAGE_LENGTH)

    for ch in ch_list:
        for ce in ce_list:
            hw.select_ce(ce)
            expected_fw = nanocycler.binaryfilewriter(test_library_settings.enable_nfs)
            expected_fw.open("CH{0}-CE{1}-{2}".format(ch, ce, file_name))

            for lun in lun_list:
                for block in block_list:
                    for page in page_list:

                        pattern, seed = device.expected_pattern_fpga_codes(ch, ce, lun, block, page)
                        if pattern == ePatternType.All1:
                            for i in range(device.PAGE_LENGTH):
                                buffer[i] = 0xFF
                        elif pattern == ePatternType.All0:
                            for i in range(device.PAGE_LENGTH):
                                buffer[i] = 0x00
                        elif pattern == ePatternType.Counter:
                            for i in range(device.PAGE_LENGTH):
                                buffer[i] = i & 0xFF
                        elif pattern == ePatternType.Alt_FF_00:
                            for i in range(0,device.PAGE_LENGTH,2):
                                buffer[i] = 0xFF
                                buffer[i+1] = 0x00
                        elif pattern == ePatternType.Alt_55_AA:
                            for i in range(0,device.PAGE_LENGTH,2):
                                buffer[i] = 0x55
                                buffer[i+1] = 0xAA
                        elif pattern == ePatternType.Random:
                            res, buffer = hw.random_fill_expected_buffer(seed, 0, device.PAGE_LENGTH)
                        elif pattern == ePatternType.User:
                            start_pos = page * device.PAGE_LENGTH
                            buffer = device.pattern_buffer[start_pos:start_pos + device.PAGE_LENGTH]
                        expected_fw.write(buffer, device.PAGE_LENGTH)

            expected_fw.close()
            expected_fw.cleanup()


def expected_dump_brick(segment):
    """
       This function is mapped to the `ExpectedDump` recipe brick of the `Special` category.\n
       It is used to create a file with the expected content of selected pages.
       The one page for each selected pages is generated. \n

    The recipe parameters (from the segment input object) are:\n
       - CHs (recipe param): the list of channels to test in parallel.
       - Ces (recipe param): the list of chip enables to test.
       - Luns (recipe param): the list of luns to test.
       - Blocks (recipe param): the list of blocks to test.
       - Pages (recipe param): the list of pages to test.
       - File (recipe param): the name of the file to create.

       For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
       `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

        """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    file_name = segment.variables("File").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    do_dump_expected(file_name, ch_list, ce_list, lun_list, block_list, page_list)

    return




def fast_read_brick(segment):
    """
    This function is mapped to the `FatRead` recipe brick of the `Special` category.\n
    It is used to read and compare the selected pages with an expected pattern using SNAP/FAST read command. The expected pattern is recalled from the NanoCycler database.
    During the test, for each page the library logs the tR (ready busy time after each read page command), the fails and fails for chunk, the IccAvg and IccMax during the entire sequence.\n

    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to test in parallel.
    - Ces (recipe param): the list of chip enables to test.
    - Luns (recipe param): the list of luns to test.
    - Blocks (recipe param): the list of blocks to test.
    - Pages (recipe param): the list of pages to test.

    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    block_list = parse_block_list(blocks)
    page_list = parse_page_list(pages)

    fast_read_pmu_algo = PMU_ALGO.Icc1
    fast_read_log_set = {LOG_SET_ITEM.tRnB4CHUNK, LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.FAILS,
                         LOG_SET_ITEM.FAILS4CHUNK}

    do_fast_read(ch_list, ce_list, lun_list, block_list, page_list, log_set=fast_read_log_set, pmu_algo=fast_read_pmu_algo)


def scan_frequency_brick(segment):
    """
    This function is mapped to the `ScanFrequency` recipe brick of the `Special` category.\n
    It is used to verify the device interface speed. The library will scan the datarate in a range and it tries to perform `TestLibrary.dq_calib_brick`.
    At the end of the test a summary with  passing and failing frequency is generated.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the target chip indexes used for test.
    - FreqMax (recipe param): the max frequency to apply in MHz (max value is 2000 for HS20, 1600MHz for HS16, 800MHz for STD Rev 2, 600 MHz for STD Rev1).
    - FreqMin (recipe param): the min frequency to apply in MHz (min value is 400MHz for HS, 50 MHz for STD Rev 1 and Rev 2).
    - FreqStep (recipe param): the frequency step to apply (min step is 2MHz up to 800MHz and 4MHz up to 1600MHz for HS, 50 MHz for STD Rev 1 and Rev 2).

     """


    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    freq_max = segment.variables("FreqMax").value()
    freq_min = segment.variables("FreqMin").value()
    freq_step = segment.variables("FreqStep").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ch_list(ces)

    aCh = []
    aCe = []
    aFreq = []
    aPass = []

    device.init_test()

    for data_rate_mhz in range(freq_min, freq_max + freq_step // 2, freq_step):

        ws.info("Set Data Rate: {0} MTs".format(data_rate_mhz))

        lun = 0
        device.calib_setup(data_rate_mhz, 256, ce, lun)
        hw.set_datarate(data_rate_mhz)

        for ce in ce_list:
            for ch in ch_list:
                device.select_channel(ch)

                calib_done = False
                max_retry = 3
                for retry in range(max_retry):
                    if retry > 0:
                        ws.warning("CH: {0} - CE: {1} - Frequency: {2} - Calibration Retry".format(ch, ce, data_rate_mhz))

                    calib_done = hw.calibrate(ce, device.PAGE_LENGTH, False)
                    if calib_done:
                        break

                logger.log_calib_param(ch, ce, data_rate_mhz)

                aCh.append(ch)
                aCe.append(ce)
                aFreq.append(data_rate_mhz)
                aPass.append(calib_done)
                if not calib_done:
                    ws.error("CH: {0} - CE: {1} - Frequency: {2} - Calibration Fails".format(ch, ce, data_rate_mhz))

    summary_fw = nanocycler.filewriter()
    summary_fw.open("FrequencyTest.csv")

    sline = "-------------------------------------------------------------------"
    ws.info(sline)
    summary_fw.write_line(sline)
    sline = "-------------------------- S U M M A R Y --------------------------"
    ws.info(sline)
    summary_fw.write_line(sline)
    sline = "-------------------------------------------------------------------"
    ws.info(sline)
    summary_fw.write_line(sline)
    for i in range(0, len(aPass)):
        if aPass[i]:
            sline = "Channel: {0} - CE: {1} - Freq: {2} MHz >> Pass".format(aCh[i], aCe[i], aFreq[i])
            ws.info(sline)
            summary_fw.write_line(sline)
        else:
            sline = "Channel: {0} - CE: {1} - Freq: {2} MHz >> Fail".format(aCh[i], aCe[i], aFreq[i])
            ws.error(sline)
            summary_fw.write_line(sline)

    summary_fw.close()
    summary_fw.cleanup()
    return


# ################################################
#  Power Profile
# ################################################

def ICC1_read_brick(segment):
    """
    This function is mapped to the `ICC1-Read` recipe brick of the `Power Profile` category.\n
    It is used to measure ICC1, ICCQ1 or IPP1 current during a read test. According to the ONFI specifications, the current is measured during
    read command and the busy (tR) time. It does not include data transfer.\n
    A waveform is measured, logged and plotted for each page read.\n
    If you decide to measure two channels in parallel, the current is shared.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
    \n
    The current are sampled and the samples (the measures) are stored in a memory (RAM) inside the tester unit.
    The RAM can store up to 2048 samples so, according the expected max operation time, the sampling rate will be adjusted.
    The max sampling rate is 50MHz (20 nsec) so the sampling period will be a multiple of that. \n
    For example: \n
    - if max erase time is 10 msec, each current sample in the RAM buffer is the average in a period of 4.882 usec.
    - if max erase time is 20 msec, each current sample in the RAM buffer is the average in a period of 9.764 usec.
    - if max program time is 1 msec, each current sample in the buffer should be the average in a period of 0.488 usec, but for accuracy reason, the pmu.py library file limits the min average time to 1 usec.
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    i_str = segment.variables("I channel").value()
    o_str = segment.variables("SP/MP").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}1 {1}".format(i_str, o_str)
    datalog.set_label(label)

    base_name = "Read ({0})".format(label)
    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), base_name), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ1
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp1
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc1

    read_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                page_list=page_list,
                                log_set=read_log_set, current_plot=current_plot, pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                    log_set=read_log_set, current_plot=current_plot, pmu_algo=pmu_algo)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")


def ICC2_program_brick(segment):
    """
    This function is mapped to the `ICC2-Program` recipe brick of the `Power Profile` category.\n
    It is used to measure ICC2, ICCQ2 or IPP2 current during a program test. According to the ONFI specifications, the current is measured during
    command, data transfer and the busy (tPROG) time.\n
    A waveform is measured, logged and plotted for each programmed page.\n
    If you decide to measure two channels in parallel, the current is shared.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    i_str = segment.variables("I channel").value()
    o_str = segment.variables("SP/MP").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}2 {1}".format(i_str, o_str)
    datalog.set_label(label)

    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), "Program ({0})".format(label)), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ2
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp2
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc2

    prog_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                               page_list=page_list,
                               log_set=prog_log_set, pattern_algo=test_library_settings.pattern_algo, current_plot=current_plot,
                               pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                   log_set=prog_log_set, pattern_algo=test_library_settings.pattern_algo, current_plot=current_plot,
                   pmu_algo=pmu_algo)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")


def ICC3_erase_brick(segment):
    """
    This function is mapped to the `ICC3-Erase` recipe brick of the `Power Profile` category.\n
    It is used to measure ICC3, ICCQ3 or IPP3 current during a block erase test. According to the ONFI specifications, the current is measured during
    command and the busy (tERS) time.\n
    A waveform is measured, logged and plotted for each erased block.\n
    If you decide to measure two channels in parallel, the current is shared.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    i_str = segment.variables("I channel").value()
    o_str = segment.variables("SP/MP").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}3 {1}".format(i_str, o_str)
    datalog.set_label(label)

    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), "Erase ({0})".format(label)), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ3
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp3
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc3

    erase_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                             log_set=erase_log_set, current_plot=current_plot, pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_erase(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list,
                 log_set=erase_log_set, current_plot=current_plot, pmu_algo=pmu_algo)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")


def ICC4_read_brick(segment):
    """
    This function is mapped to the `ICC4-Read` recipe brick of the `Power Profile` category.\n
    It is used to measure ICC4, ICCQ4 or IPP4 current during a read test. According to the ONFI specifications, the current is measured only during data transfer.\n
    A waveform is measured, logged and plotted for each page read.\n
    If you decide to measure two channels in parallel, the current is shared.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    i_str = segment.variables("I channel").value()
    o_str = segment.variables("SP/MP").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}4r {1}".format(i_str, o_str)
    datalog.set_label(label)

    base_name = "Read ({0})".format(label)
    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), base_name), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ4r
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp4r
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc4r

    read_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                                page_list=page_list,
                                log_set=read_log_set, current_plot=current_plot, pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_read_out(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                    log_set=read_log_set, current_plot=current_plot, pmu_algo=pmu_algo)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")


def ICC4_program_brick(segment):
    """
    This function is mapped to the `ICC4-Program` recipe brick of the `Power Profile` category.\n
    It is used to measure ICC4, ICCQ4 or IPP4 current during a program test. According to the ONFI specifications, the current is measured during
    data transfer only.\n
    A waveform is measured, logged and plotted for each programmed page.\n
    If you decide to measure two channels in parallel, the current is shared.\n
    The recipe parameters (from the segment input object) are:\n
    - CHs (recipe param): the list of channels to measure in parallel.
    - Ces (recipe param): the list of chip enables to measure.
    - Luns (recipe param): the list of luns to measure.
    - Blocks (recipe param): the list of blocks to measure.
    - Pages (recipe param): the list of pages to measure.
    - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
    - SP/MP (recipe param): It selects between single plane (SP) or multi plane (MP) operation.
    - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
    \n
    For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
    `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

     """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    luns = segment.variables("Luns").value()
    blocks = segment.variables("Blocks").value()
    pages = segment.variables("Pages").value()
    i_str = segment.variables("I channel").value()
    o_str = segment.variables("SP/MP").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)
    lun_list = parse_lun_list(luns)
    page_list = parse_page_list(pages)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}4w {1}".format(i_str, o_str)
    datalog.set_label(label)

    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), "Program ({0})".format(label)), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ4w
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp4w
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc4w

    prog_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    if o_str == "MP":
        list_block_list = parse_multi_block_list(blocks)
        do_multi_plane_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, list_block_list=list_block_list,
                               page_list=page_list,
                               log_set=prog_log_set, pattern_algo=test_library_settings.pattern_algo, current_plot=current_plot,
                               pmu_algo=pmu_algo)
    else:
        block_list = parse_block_list(blocks)
        do_program(ch_list=ch_list, ce_list=ce_list, lun_list=lun_list, block_list=block_list, page_list=page_list,
                   log_set=prog_log_set, pattern_algo=test_library_settings.pattern_algo, current_plot=current_plot,
                   pmu_algo=pmu_algo)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")


def ICC5_idle_brick(segment):
    """
        This function is mapped to the `ICC5-idle` recipe brick of the `Power Profile` category.\n
        It is used to measure ICC5, ICCQ5 current during the idle state, according to the ONFI specifications.\n
        A waveform is measured for the idle time, longer is the period more accurate is the measurement.\n
        If you decide to measure two channels or two CE in parallel, the current is shared.\n
        The recipe parameters (from the segment input object) are:\n
        - CHs (recipe param): the list of channels to measure in parallel.
        - Ces (recipe param): the list of chip enables to measure.
        - IdleTime (recipe param): the idle time to measure in milli seconds, longer it is more accurate is the measurement.
        - I channel (recipe param): It selects between ICC, ICCQ or IPP measurement.
        - VPP (recipe param): If IPP is selected, VPP is turned on at this value (allowed values are between 10V and 14V).
        \n
        For part selection syntax see:  `TestLibrary.parse_ch_list` `TestLibrary.parse_ce_list` `TestLibrary.parse_lun_list`
        `TestLibrary.parse_block_list` `TestLibrary.parse_multi_block_list` `TestLibrary.parse_page_list`

         """

    chs = segment.variables("CHs").value()
    ces = segment.variables("Ces").value()
    idle_time = segment.variables("IdleTime").value()
    i_str = segment.variables("I channel").value()
    vpp = segment.variables("VPP").value()

    ch_list = parse_ch_list(chs)
    ce_list = parse_ce_list(ces)

    if not hw.is_nanocycler_hs():
        ws.error("Test not available in standard version")
        return

    label = "{0}5".format(i_str)
    datalog.set_label(label)

    current_plot = nanocycler.plot()
    current_plot.open(make_unique_plot_name(segment.name(), "Idle ({0})".format(label)), "usec", "mA", "")

    if i_str == "ICCQ":
        pmu_algo = PMU_ALGO.IccQ5
    elif i_str == "IPP":
        pmu_algo = PMU_ALGO.Ipp5
        device.enable_vpp(True, vpp)
    else:
        pmu_algo = PMU_ALGO.Icc5

    idle_log_set = {LOG_SET_ITEM.PmuAvg, LOG_SET_ITEM.PmuMax, LOG_SET_ITEM.PmuSamples}

    device.init_test(log_enable=True, log_set=idle_log_set, current_plot=current_plot)

    pmu_setup(pmu_algo, idle_time * 1000)

    device.device_idle(ch_list, ce_list, idle_time)

    if i_str == "IPP":
        device.enable_vpp(False)

    current_plot.close()

    datalog.set_label("")

    return

#################
### MAIN      ###
#################

def initialize_brick(segment):
    """
    This function is mapped to the `Initialize` recipe brick of the `Basic Functions` category.\n
    It is used to select the device to test. According to the user selection, a specific instance of a device class is created.\n
    After the device creation, the identification method is called. Usually this method turns the device on, applies the reset and set feature commands.
    It also reads the unique id in order to synchronize with the local (and central if installed) database.

    The recipe parameters (from the segment input object) are:\n
    - DeviceName (recipe param): this is the device name to test.
    - StopAtFail (recipe param): this is a flag used to stop the test flow in case of status register fail (Status register not equal to 0xE0).
    - FBCLimit (recipe param): this is a threshold used to stop the test flow in case of number of fails exceed this threshold (-1 not used threshold).
    - EnableNfs (recipe param): this is a flag used to switch between standard datalog in SD card or NFS datalog (in a remote NFS shared folder).
    - PatternAlgo (recipe param): this is used to control the pattern algorithm to use in the next program operation. Allowed options are
        - RANDOM: pseudo random pattern pattern from lfsr, with randomic seed based on nano second timer.
        - USER: pattern is read from the input file.
        - ALL0: all bytes will be programmed at 0x00.
        - ALL1: all bytes will be programmed at 0xFF.
        - ALT_AA_55: even byte will be programmed with 0x55, odd byte will be programmed with 0xAA.
        - ALT_FF_00: even byte will be programmed with 0xFF, odd byte will be programmed with 0x00.
        - INCREMENTAL: byte will be programmed with incremental pattern (0x00, 0x01, 0x02, ... 0xFE, 0xFF, 0x00, 0x01, ...).
        - RANDOM_0: pseudo random pattern pattern from lfsr, with seed equal to the page index.
    - DisableSequenceRecording (recipe param): this is used to force sequence recording disabled also if the `TestLibrary.enable_sequence_recording_brick` is invoked.

     """

    ws.info("NplusT NanoCycler Python Test Library Version: {0}".format(version))

    global device
    device_name = segment.variables("DeviceName").value()
    stop_at_fail = segment.variables("StopAtFail").value()
    fbc_limit = segment.variables("FBCLimit").value()
    enable_nfs = segment.variables("EnableNfs").value()
    pattern_algo = segment.variables("PatternAlgo").value()
    disable_sequence_recording = segment.variables("DisableSequenceRecording").value()

    device = None
    if device_name == 'Dummy':
        device = CDummy()
    if device_name == 'TLCDevice':
        device = CTLCDevice()
    if device_name == 'QLCDevice':
        device = CQLCDevice()
    if device_name == 'HynixV5':
        device = CHynixV5()
    if device_name == 'HynixV6':
        device = CHynixV6()
    if device_name == 'HynixV7':
        device = CHynixV7()
    if device_name == 'KIOXIABiCS5':
        device = CKIOXIABiCS5()
    if device_name == 'KIOXIABiCS5QLC':
        device = CKIOXIABiCS5QLC()
    if device_name == 'MicronB27A':
        device = CMicronB27A()
    if device_name == 'MicronB27B':
        device = CMicronB27B()
    if device_name == 'MicronB37R':
        device = CMicronB37R()
    if device_name == 'MicronB47R':
        device = CMicronB47R()
    if device_name == 'SandiskBiCS4':
        device = CSandiskBiCS4()
    if device_name == 'SandiskBiCS5':
        device = CSandiskBiCS5()
    if device_name == 'SandiskBiCS6_512Gb':
        device = CSandiskBiCS6_512Gb()
    if device_name == 'SandiskBiCS6_1Tb':
        device = CSandiskBiCS6_1Tb()
    if device_name == 'SandiskBiCS8':
        device = CSandiskBiCS8()
    if device_name == 'HynixV5':
        device = CHynixV5()
    if device_name == 'ToshibaBiCS3':
        device = CToshibaBiCS3()
    if device_name == 'ToshibaBiCS4':
        device = CToshibaBiCS4()
    if device_name == 'ToshibaVHT':
        device = CToshibaVHT()
    if device_name == 'YMTCX19050':
        device = CYMTCX19050()
    if device_name == 'YMTCX26070':
        device = CYMTCX26070()
    if device_name == 'YMTCX29060':
        device = CYMTCX29060()
    if device_name == 'YMTCX39070':
        device = CYMTCX39070()
    if device_name == 'YMTCX36070':
        device = CYMTCX36070()
    if device_name == 'YMTCX49060':
        device = CYMTCX49060()
    if device_name == 'OnfiDevice':
        device = COnfiDevice()

    if device is None:
        err = "Device '{0}' is not supported by this library".format(device_name)
        raise Exception(err)  # exception

    # init datalog
    " and the datalog file creation."
    res, time = hw.get_time()
    global file_name
    file_name = test_library_settings.recipe_name
    if (res):
        file_name += "-" + time
    file_name = file_name.replace(".","-")
    ws.info("Open datalog {0}".format(file_name))
    datalog.open(file_name, True if enable_nfs == 1 else False)

    datalog.set_counter(0)
    datalog.set_condition("")
    datalog.set_function("")
    datalog.set_temperature(25)
    datalog.set_ce(0)
    datalog.set_address(0, 0, 0)
    datalog.set_wordline(0)
    datalog.set_level("")
    datalog.set_segment(0, segment.name())
    datalog.set_label("DEVICEINFO")
    datalog.add_data("MANUFACTURER", device.DEVICE_MANUFACTURER)
    datalog.add_data("DEVICE_NAME", device.DEVICE_NAME)
    datalog.add_data("LEVELS", str(device.LEVEL_NUMBER))
    datalog.add_data("WL_NUMBER", str(device.WL_NUMBER))
    datalog.add_data("PAGE_NUMBER", str(device.PAGE_NUMBER))
    datalog.add_data("PAGE_LENGTH", str(device.PAGE_LENGTH))
    datalog.add_data("CHUNK_NUMBER", str(device.CHUNK_NUMBER))
    datalog.add_data("LUN_START_BIT_ADDRESS", str(device.LUN_START_BIT_ADDRESS))
    datalog.add_data("BLOCK_START_BIT_ADDRESS", str(device.BLOCK_START_BIT_ADDRESS))
    datalog.add_data("VALID_LUN_MASK", str(device.VALID_LUN_MASK))
    datalog.add_data("VALID_PAGE_MASK", str(device.VALID_PAGE_MASK))
    datalog.add_data("VALID_BLOCK_MASK", str(device.VALID_BLOCK_MASK))
    datalog.set_vcc(device.VCC)
    datalog.set_vccq(device.VCCQ)
    datalog.set_label("")

    ws.info("Device {0} initialized".format(device.DEVICE_NAME))

    # ####################################
    # configure Test library settings

    # enable nfs
    test_library_settings.enable_nfs = enable_nfs
    # store pattern algorithm
    test_library_settings.set_pattern_algo(pattern_algo)

    # disable sequence recording
    test_library_settings.disable_sequence_recording = disable_sequence_recording

    # init logger
    logger.set_chunks_number(device.CHUNK_NUMBER)
    logger.set_stop_at_fail(stop_at_fail)
    logger.set_fbc_limit(fbc_limit)


def device_config_brick(segment):
    """
    This function is mapped to the `DeviceConfig` recipe brick of the `Basic Functions` category.\n
    It is used to configure some device specific parameters. \n
    It must be called immediately after initialize_brick, but it can also be invoked during the test to change some device parameters. \n
    This method turns on the device and, by the device.identification method, it usually applies the reset and set feature commands.
    It also reads the unique id in order to synchronize with the local (and central if installed) database.

    The recipe parameters (from the segment input object) are:\n
    - EnableSlcMode (recipe param): this is a flag used to enable SLC operation.
    - EnableAiprMode (recipe param): this is a flag used to enable "Plane Interleave Read Async" operation sometimes also called VML Read.
    - ReadLatencyCycles (recipe param): this is used to control the read latency cycles (RE pulses), sometimes also called read warm-up (allowed values are 0,1,2,4).
    - WriteLatencyCycles (recipe param): this is used to control the write latency cycles (DQS pulses), sometimes also called write warm-up (allowed values are 0,1,2,4).

    The SLC mode and aIPR mode flags are stored in COnfiDevice base class and can be managed inside the device specific class. As reference code,
    the flags are set during initialize and are used in all next tests. Of course, it is possible to add this parameter to a test brick
    in order to locally control the flags.

     """

    enable_slc_mode = segment.variables("EnableSlcMode").value()
    enable_aipr_mode = segment.variables("EnableAiprMode").value()
    read_latency_cycles = segment.variables("ReadLatencyCycles").value()
    write_latency_cycles = segment.variables("WriteLatencyCycles").value()
    odt = segment.variables("ODT").value()
    driver_strength = segment.variables("DriverStrength").value()

    # configure device parameters:
    device.odt = odt
    device.driver_strength = driver_strength
    device.read_latency_cycles = read_latency_cycles
    device.write_latency_cycles = write_latency_cycles
    # standard or slc mode
    device.slc_mode = enable_slc_mode
    # standard or aipr mode
    device.aipr_mode = enable_aipr_mode


    device.init_test()

    # turn on with default values
    device.turn_on(device.VCC, device.VCCQ)

    # identify device
    device.identification()


def datalog_brick(segment):
    """
    This function is mapped to the `Initialize` recipe brick of the `Basic Functions` category.\n
    It is used to select the device to test. According to the user selection, a specific instance of a device class is created.\n
    After the device creation, the identification method is called. Usually this method turns the device on, applies the reset and set feature commands.
    It also reads the unique id in order to synchronize with the local (and central if installed) database.

    The recipe parameters (from the segment input object) are:\n
    - DeviceName (recipe param): this is the device name to test.
    - StopAtFail (recipe param): this is a flag used to stop the test flow in case of status register fail (Status register not equal to 0xE0).
    - FBCLimit (recipe param): this is a threshold used to stop the test flow in case of number of fails exceed this threshold (-1 not used threshold).
    - EnableNfs (recipe param): this is a flag used to switch between standard datalog in SD card or NFS datalog (in a remote NFS shared folder).
    - PatternAlgo (recipe param): this is used to control the pattern algorithm to use in the next program operation. Allowed options are
        - RANDOM: pseudo random pattern pattern from lfsr, with randomic seed based on nano second timer.
        - USER: pattern is read from the input file.
        - ALL0: all bytes will be programmed at 0x00.
        - ALL1: all bytes will be programmed at 0xFF.
        - ALT_AA_55: even byte will be programmed with 0x55, odd byte will be programmed with 0xAA.
        - ALT_FF_00: even byte will be programmed with 0xFF, odd byte will be programmed with 0x00.
        - INCREMENTAL: byte will be programmed with incremental pattern (0x00, 0x01, 0x02, ... 0xFE, 0xFF, 0x00, 0x01, ...).
        - RANDOM_0: pseudo random pattern pattern from lfsr, with seed equal to the page index.
    - DisableSequenceRecording (recipe param): this is used to force sequence recording disabled also if the `TestLibrary.enable_sequence_recording_brick` is invoked.

     """

    datalog.close()

    global device
    device_name = segment.variables("DeviceName").value()
    stop_at_fail = segment.variables("StopAtFail").value()
    fbc_limit = segment.variables("FBCLimit").value()
    enable_nfs = segment.variables("EnableNfs").value()
    pattern_algo = segment.variables("PatternAlgo").value()
    disable_sequence_recording = segment.variables("DisableSequenceRecording").value()

    device = None
    if device_name == 'Dummy':
        device = CDummy()
    if device_name == 'TLCDevice':
        device = CTLCDevice()
    if device_name == 'QLCDevice':
        device = CQLCDevice()
    if device_name == 'HynixV5':
        device = CHynixV5()
    if device_name == 'HynixV6':
        device = CHynixV6()
    if device_name == 'HynixV7':
        device = CHynixV7()
    if device_name == 'KIOXIABiCS5':
        device = CKIOXIABiCS5()
    if device_name == 'KIOXIABiCS5QLC':
        device = CKIOXIABiCS5QLC()
    if device_name == 'MicronB27A':
        device = CMicronB27A()
    if device_name == 'MicronB27B':
        device = CMicronB27B()
    if device_name == 'MicronB37R':
        device = CMicronB37R()
    if device_name == 'MicronB47R':
        device = CMicronB47R()
    if device_name == 'SandiskBiCS4':
        device = CSandiskBiCS4()
    if device_name == 'SandiskBiCS5':
        device = CSandiskBiCS5()
    if device_name == 'SandiskBiCS6_512Gb':
        device = CSandiskBiCS6_512Gb()
    if device_name == 'SandiskBiCS8':
        device = CSandiskBiCS8()
    if device_name == 'HynixV5':
        device = CHynixV5()
    if device_name == 'ToshibaBiCS3':
        device = CToshibaBiCS3()
    if device_name == 'ToshibaBiCS4':
        device = CToshibaBiCS4()
    if device_name == 'ToshibaVHT':
        device = CToshibaVHT()
    if device_name == 'YMTCX19050':
        device = CYMTCX19050()
    if device_name == 'YMTCX26070':
        device = CYMTCX26070()
    if device_name == 'YMTCX29060':
        device = CYMTCX29060()
    if device_name == 'YMTCX39070':
        device = CYMTCX39070()
    if device_name == 'YMTCX36070':
        device = CYMTCX36070()
    if device_name == 'YMTCX49060':
        device = CYMTCX49060()
    if device_name == 'OnfiDevice':
        device = COnfiDevice()

    if device is None:
        err = "Device '{0}' is not supported by this library".format(device_name)
        raise Exception(err)  # exception

    # init datalog
    " and the datalog file creation."
    res, time = hw.get_time()
    global file_name
    # file_name = test_library_settings.recipe_name
    file_name = str(segment.name())
    if (res):
        # file_name += "-" + time + "-" + str(segment.name())
        file_name += "-" + time
    file_name = file_name.replace(".","-")
    ws.info("Open datalog {0}".format(file_name))
    datalog.open(file_name, True if enable_nfs == 1 else False)

    datalog.set_counter(0)
    datalog.set_condition("")
    datalog.set_function("")
    datalog.set_temperature(25)
    datalog.set_ce(0)
    datalog.set_address(0, 0, 0)
    datalog.set_wordline(0)
    datalog.set_level("")
    datalog.set_segment(0, segment.name())
    datalog.set_label("DEVICEINFO")
    datalog.add_data("MANUFACTURER", device.DEVICE_MANUFACTURER)
    datalog.add_data("DEVICE_NAME", device.DEVICE_NAME)
    datalog.add_data("LEVELS", str(device.LEVEL_NUMBER))
    datalog.add_data("WL_NUMBER", str(device.WL_NUMBER))
    datalog.add_data("PAGE_NUMBER", str(device.PAGE_NUMBER))
    datalog.add_data("PAGE_LENGTH", str(device.PAGE_LENGTH))
    datalog.add_data("CHUNK_NUMBER", str(device.CHUNK_NUMBER))
    datalog.add_data("LUN_START_BIT_ADDRESS", str(device.LUN_START_BIT_ADDRESS))
    datalog.add_data("BLOCK_START_BIT_ADDRESS", str(device.BLOCK_START_BIT_ADDRESS))
    datalog.add_data("VALID_LUN_MASK", str(device.VALID_LUN_MASK))
    datalog.add_data("VALID_PAGE_MASK", str(device.VALID_PAGE_MASK))
    datalog.add_data("VALID_BLOCK_MASK", str(device.VALID_BLOCK_MASK))
    datalog.set_vcc(device.VCC)
    datalog.set_vccq(device.VCCQ)
    datalog.set_label("")

    ws.info("Device {0} initialized".format(device.DEVICE_NAME))

    # ####################################
    # configure Test library settings

    # enable nfs
    test_library_settings.enable_nfs = enable_nfs
    # store pattern algorithm
    test_library_settings.set_pattern_algo(pattern_algo)

    # disable sequence recording
    test_library_settings.disable_sequence_recording = disable_sequence_recording

    # init logger
    logger.set_chunks_number(device.CHUNK_NUMBER)
    logger.set_stop_at_fail(stop_at_fail)
    logger.set_fbc_limit(fbc_limit)


def test_library_config_brick(segment):
    """
    This function is mapped to the `TestLibraryConfig` recipe brick of the `Basic Functions` category.\n
    It is used to change some test library parameter after the initialization.

    See also:  `TestLibrary.initialize_brick`

    The recipe parameters (from the segment input object) are:\n
    - StopAtFail (recipe param): this is a flag used to stop the test flow in case of status register fail (Status register not equal to 0xE0).
    - FBCLimit (recipe param): this is a threshold used to stop the test flow in case of number of fails exceed this threshold (-1 not used threshold).
    - PatternAlgo (recipe param): this is used to control the pattern algorithm to use in the next program operation. Allowed options are
        - RANDOM: pseudo random pattern pattern from lfsr, with randomic seed based on nano second timer.
        - USER: pattern is read from the input file.
        - ALL0: all bytes will be programmed at 0x00.
        - ALL1: all bytes will be programmed at 0xFF.
        - ALT_AA_55: even byte will be programmed with 0x55, odd byte will be programmed with 0xAA.
        - ALT_FF_00: even byte will be programmed with 0xFF, odd byte will be programmed with 0x00.
        - INCREMENTAL: byte will be programmed with incremental pattern (0x00, 0x01, 0x02, ... 0xFE, 0xFF, 0x00, 0x01, ...).
        - RANDOM_0: pseudo random pattern pattern from lfsr, with seed equal to the page index.

     """

    stop_at_fail = segment.variables("StopAtFail").value()
    fbc_limit = segment.variables("FBCLimit").value()
    pattern_algo = segment.variables("PatternAlgo").value()

    # store pattern algorithm
    test_library_settings.set_pattern_algo(pattern_algo)

    # config logger
    logger.set_stop_at_fail(stop_at_fail)
    logger.set_fbc_limit(fbc_limit)


def select_good_block_list_brick(segment):
    """
    This function is mapped to the `SelectGoodBlockList` recipe brick of the `Block Selection` category.\n
    It is used to select the a set of good blocks (not marked as bad in the NanoCycler database).
    In order to support parallel operations (multi channel, multi chip, multi lun and multi plane operation) the block will be selected only if it is good for each CH, CE, and LUN.
    The library contains 2 lists, one for single plane and one for multi plane operations. An appropriate list must be created according to the following operations.
    In case a list is generated, the next operation will use this list instead of test Block variable.

    The recipe parameters (from the segment input object) are:\n
    - SP/MP (recipe param): select the list to generate (SP = Single Plane or MP = Multi Plane).
    - FirstBlock (recipe param): start index to begin good block search.
    - BlockNumber (recipe param): number of good block to select.

     """

    o_str = segment.variables("SP/MP").value()
    first_block = segment.variables("FirstBlock").value()
    block_number = segment.variables("BlockNumber").value()

    if o_str == "MP":
        test_library_settings.internal_list_block_list.clear()

        block_count = 0
        for block in range(first_block, device.BLOCK_NUMBER, device.PLANE_NUMBER):

            block_list = []

            for b in range(block, block + device.PLANE_NUMBER):
                is_bad = False
                for ch in range(device.CHANNEL_NUM):
                    for ce in range(device.DEVICE_CE_NUMBER):
                        for lun in range(device.LUN_NUMBER):
                            is_bad |= data_mgr.is_bad_block(ch, ce, lun, b)

                if not is_bad and block_count < block_number:
                    block_list.append(b)
                    block_count += 1

            if len(block_list) > 0:
                test_library_settings.internal_list_block_list.append(block_list)

            if block_count >= block_number:
                break

        ws.info("select_good_block_list_brick (MP): {0}".format(test_library_settings.internal_list_block_list))
    else:
        test_library_settings.internal_block_list.clear()

        for block in range(first_block, device.BLOCK_NUMBER):
            is_bad = False
            for ch in range(device.CHANNEL_NUM):
                for ce in range(device.DEVICE_CE_NUMBER):
                    for lun in range(device.LUN_NUMBER):
                        is_bad |= data_mgr.is_bad_block(ch, ce, lun, block)
            # if block is good for each channel, ce and lun add to the list
            if not is_bad:
                test_library_settings.internal_block_list.append(block)

            if len(test_library_settings.internal_block_list) >= block_number:
                break

        ws.info("select_good_block_list_brick (SP): {0}".format(test_library_settings.internal_block_list))


def select_virgin_block_list_brick(segment):
    """
    This function is mapped to the `SelectVirginBlockList` recipe brick of the `Block Selection` category.\n
    It is used to select a set of virgin blocks (not tested in the NanoCycler database; a block is not tested if it has no pattern seed assigned,
    which means no erase and no program operation has been performed on it).
    In order to support parallel operations (multi channel, multi chip, multi lun and multi plane operation) the block will be selected only if it is good for each CH, CE, and LUN.
    The library contains 2 lists, one for single plane and one for multi plane operations. An appropriate list must be created according to the following operations.
    In case a list is generated, next operation will use this list instead of test Block variable.

    The recipe parameters (from the segment input object) are:\n
    - SP/MP (recipe param): select the list to generate (SP = Single Plane or MP = Multi Plane).
    - FirstBlock (recipe param): start index to begin good block search
    - BlockNumber (recipe param): number of good block to select

     """

    o_str = segment.variables("SP/MP").value()
    first_block = segment.variables("FirstBlock").value()
    block_number = segment.variables("BlockNumber").value()

    if o_str == "MP":
        test_library_settings.internal_list_block_list.clear()

        block_count = 0
        for block in range(first_block, device.BLOCK_NUMBER, device.PLANE_NUMBER):

            block_list = []

            for b in range(block, block + device.PLANE_NUMBER):
                has_been_tested = False
                for ch in range(device.CHANNEL_NUM):
                    for ce in range(device.DEVICE_CE_NUMBER):
                        for lun in range(device.LUN_NUMBER):
                            has_been_tested |= (data_mgr.get_seed(ch, ce, lun, block) != PATTERN_SEED.VIRGIN)

                if not has_been_tested and block_count < block_number:
                    block_list.append(b)
                    block_count += 1

            if len(block_list) > 0:
                test_library_settings.internal_list_block_list.append(block_list)

            if block_count >= block_number:
                break

        ws.info("select_virgin_block_list_brick (MP): {0}".format(test_library_settings.internal_list_block_list))

    else:
        test_library_settings.internal_block_list.clear()
        for block in range(first_block, device.BLOCK_NUMBER):
            has_been_tested = False
            for ch in range(device.CHANNEL_NUM):
                for ce in range(device.DEVICE_CE_NUMBER):
                    for lun in range(device.LUN_NUMBER):
                        has_been_tested |= (data_mgr.get_seed(ch, ce, lun, block) != PATTERN_SEED.VIRGIN)
            # add only if not tested
            if not has_been_tested:
                test_library_settings.internal_block_list.append(block)
            if len(test_library_settings.internal_block_list) >= block_number:
                break

        ws.info("select_virgin_block_list_brick (MP): {0}".format(test_library_settings.internal_block_list))


def clear_block_list_brick(segment):
    """
    This function is mapped to the `ClearBlockList` recipe brick of the `Block Selection` category.\n
    It is used to reset previously generated lists by SelectGoodBlockList or by SelectVirginBlockList.
    A parameter is used to select the list to clear (SP, MP, or ALL).

    The recipe parameters (from the segment input object) are:\n
    - SP/MP (recipe param): select the list to clear.

    See also:  `TestLibrary.select_good_block_list_brick` `TestLibrary.select_virgin_block_list_brick`

     """

    o_str = segment.variables("SP/MP").value()

    if o_str == "MP":
        test_library_settings.internal_list_block_list.clear()
    elif o_str == "SP":
        test_library_settings.internal_block_list.clear()
    else:
        test_library_settings.internal_list_block_list.clear()
        test_library_settings.internal_block_list.clear()


##########################################################################
### CUSTOM TESTS
##########################################################################

def custom_1_brick(segment):
    """
    This function is mapped to the `Custom1` recipe brick of the `Special` category.\n
    This a general purpose brick, the engineer can customize this method writing custom_test_1 method in the device class.\n
     """
    device.custom_test_1()




def custom_2_brick(segment):
    """
    This function is mapped to the `Custom2` recipe brick of the `Special` category.\n
    This a general purpose brick, the engineer can customize this method writing custom_test_2 method in the device class.\n
     """
    device.custom_test_2()


def fpga_setup_brick(segment):
    """
    This function is mapped to the `FpgaSetup` recipe brick of the `HW` category.\n
    This function is used to program some FPGA parameters as read and write latency cycles or the number of chunck. \n
    Asfter turn on the latencies are zero, during the device initialization, they are programmed according the device configuration.\n
    If the recipe changes (by `set_feature_brick`) the latency values, this brick must be called to keep the settings aligned.

    The recipe parameters (from the segment input object) are:\n
    - ReadLatencyCycles: number of read latency cycles
    - WriteLatencyCycles: number of write latency cycles
    - Chunks: number of chunks in a page (default value depends on the device)

     """

    fpga_rl = segment.variables("ReadLatencyCycles").value()
    fpag_wl = segment.variables("WriteLatencyCycles").value()
    chunk_number = segment.variables("Chunks").value()

    device.fpga_setup(fpga_rl, fpag_wl, chunk_number)

##########################################################################
### TEST COLLECTIONS
##########################################################################

tests = {
    'Init': init_library,  # MANDATORY builtin function
    'Terminate': terminate_library,  # MANDATORY builtin function

    # HW
    'FpgaSetup': fpga_setup_brick,

    # Basic Function
    'Initialize': initialize_brick,
    'DeviceConfig': device_config_brick,
    'DataLog': datalog_brick,
    'TestLibraryConfig': test_library_config_brick,
    'SelectGoodBlockList': select_good_block_list_brick,
    'SelectVirginBlockList': select_virgin_block_list_brick,
    'ClearBlockList': clear_block_list_brick,
    'DQCalib': dq_calib_brick,
    'TurnOn': turn_on_brick,
    'TurnOff': turn_off_brick,
    'DeviceIdentify': device_identify_brick,
    'SetPower': set_power_brick,
    'Reset': reset_brick,
    'ZQCalibLong': zq_calib_long_brick,
    'ZQCalibShort': zq_calib_short_brick,
    'SetFeature': set_feature_brick,
    'SetFeatureByLun': set_feature_by_lun_brick,
    'SetFeatureAsync': set_feature_async_brick,
    'SetFeatureByLunAsync': set_feature_by_lun_async_brick,
    'GetFeature': get_feature_brick,
    'GetFeatureByLun': get_feature_by_lun_brick,
    'ReadOut': read_out_brick,
    'OpenBlk_ReadOut': open_block_read_out_brick, # Eric: not used frequently
    'StdrReadOut': stdr_read_out_brick, # Eric
    'MultiReadOut': multi_read_out_brick, # Eric, multi-time reads, not used frequently
    'Program': program_brick,
    'Erase': erase_brick,

    # Composite Functions
    'Cycling': cycling_brick,
    'ReadDisturb': read_disturb_brick,
    'MultiPlaneCycling': multi_plane_cycling_brick,
    'MultiPlaneFastCycling': multi_plane_fast_cycling_brick, # Eric
    'MultiPlaneDelayCycling': multi_plane_delay_cycling_brick, # Eric
    'MultiPlaneReadDisturb': multi_plane_read_disturb_brick, # Eric
    'MultiPlaneProgram': multi_plane_program_brick,
    'TempProfiler': temp_profiler_brick,
    'MultiPlaneEP_Hang': multi_plane_EP_hang_brick,

    # VT
    'ReadRetry': read_retry_brick,
    'OpenBlk_ReadRetry': open_block_read_retry_brick, # Eric
    'ReadOffset_BER': read_offset_ber_brick,
    'ReadOffsetInCommand_BER': read_offset_in_command_ber_brick,
    'ReadOffset_BEST': read_offset_best_brick,
    'ReadOffset_BEST_2': read_offset_best_2_brick,
    'ReadOffset_Fitting_BEST': read_offset_fitting_best_brick,
    'ReadOffset_Vth': read_offset_vth_brick,
    'ReadOffset_Vth_Dump': read_offset_vth_dump_brick,
    'Vth_Read': vth_read_brick, # collect Vt

    # Power Profile
    'ICC1-Read': ICC1_read_brick,
    'ICC2-Program': ICC2_program_brick,
    'ICC3-Erase': ICC3_erase_brick,
    'ICC4-Read': ICC4_read_brick,
    'ICC4-Program': ICC4_program_brick,
    'ICC5-Idle': ICC5_idle_brick,

    # Temperature
    'TemperatureOn': temperature_on_brick,
    'TemperatureOff': temperature_off_brick,
    'DataRetention': data_retention_brick,

    # Special Function
    'BadBlockTest': bad_block_brick,
    'GoodBlockCycling': good_block_cycling_brick,
    'MultiPlaneGoodBlockCycling': good_block_multi_plane_cycling_brick,
    'ReadDump': read_dump_brick,
    'ExpectedDump': expected_dump_brick,
    'ReadOffsetDump': read_offset_dump_brick,
    'ScanFrequency': scan_frequency_brick,
    'EraseSuspend': erase_suspend_brick,
    'ProgramSuspend': program_suspend_brick,
    'FastRead': fast_read_brick,
    'Delay': delay_brick,
    'DelayWithRead': delay_with_read_brick,
    'PageBufferTest': page_buffer_test_brick,

    # SequenceViewer
    'EnableSequenceRecording': enable_sequence_recording_brick,
    'DisableSequenceRecording': disable_sequence_recording_brick,

    # Custom(s)
    'Custom1': custom_1_brick,
    'Custom2': custom_2_brick
}

start_time = 0

__pdoc__["on_after_test_run_callback"]=False
def on_after_test_run_callback(segment):
    global start_time
    elapsed_time_msec = (hw.get_nsec_time() - start_time) / 1E9
    ws.info("Segment: {0} - Elapsed Time {1:.3f} sec".format(segment.name(), elapsed_time_msec))
    data_mgr.store_seeds()
    data_mgr.store_bad_blocks()
    return


__pdoc__["on_before_test_run_callback"]=False
def on_before_test_run_callback(segment):
    datalog.set_counter(0)
    datalog.set_segment(0, segment.name())
    ws.info("Running Segment: {0} ..... ".format(segment.name()))
    global start_time
    start_time = hw.get_nsec_time()
    return


""" Run test """
ws.run(tests, on_before_test_run_callback, on_after_test_run_callback)

# sys.exit()

