## @package nanocycler.ws
# <summary>	Contains the Test Program base functionalities.</summary>
#
# @ingroup wsApiGroup	

from .libmanager import *
from .utility import *

try:
    import uctypes

    # ! @cond Doxygen_Suppress
    STATE = {
        "segment": 0 | uctypes.INT32,
        "segmentName": (4 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "description": (68 | uctypes.ARRAY, 512 | uctypes.UINT8),
        "errorMessage": (580 | uctypes.ARRAY, 512 | uctypes.UINT8),
        "progressMessage": (1092 | uctypes.ARRAY, 512 | uctypes.UINT8),
        "temperature": (1608 | uctypes.FLOAT64),
        "progress": (1616 | uctypes.INT32),
        "stop": (1620 | uctypes.UINT8)
    }

    VARIABLE = {
        "name": (0 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "type": (64 | uctypes.ARRAY, 512 | uctypes.UINT8),
        "value": (576 | uctypes.PTR, uctypes.UINT8)
    }

    FUNCTION = {
        "name": (0 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "category": (64 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "variableCount": (128 | uctypes.INT32),
        "variables": (136 | uctypes.PTR, uctypes.UINT8)
    }

    SEGMENT = {
        "name": (0 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "function": (64 | uctypes.ARRAY, 64 | uctypes.UINT8),
        "variableCount": (128 | uctypes.INT32),
        "variables": (136 | uctypes.PTR, uctypes.UINT8)
        }

    MANIFEST = {
        "testProgram": (0 | uctypes.ARRAY, 512 | uctypes.UINT8),
        "variableCount": (512 | uctypes.INT32),
        "variables": (520 | uctypes.PTR, uctypes.UINT8),

        "functionCount": (528 | uctypes.INT32),
        "flows": (536 | uctypes.PTR, uctypes.UINT8),

        "segmentCount": (544 | uctypes.INT32),
        "segments": (552 | uctypes.PTR, uctypes.UINT8)
    }


    class wsclass:
        def __init__(self):
            # libc = ffi.open("libc.so.6")
            # self.clock = libc.func("L", "clock", "")

            self._lib_handle = CLibManagerSingleton.load_libwslibpy()
            self.progress = CLibManagerSingleton.load_func(self._lib_handle, "v", "progress", "si")
            self.setalarm = CLibManagerSingleton.load_func(self._lib_handle, "v", "setAlarm", "s")
            self.info = CLibManagerSingleton.load_func(self._lib_handle, "v", "traceInfo", "s")
            self.warning = CLibManagerSingleton.load_func(self._lib_handle, "v", "traceWarning", "s")
            self.error = CLibManagerSingleton.load_func(self._lib_handle, "v", "traceError", "s")
            self.getstate = CLibManagerSingleton.load_func(self._lib_handle, "p", "getState", "")
			
            # Declare variables
            self.recipe = None
            self.flows = None

        def progress(self, text, value):
            self.progress(text, value)
			
        def set_alarm(self, text):
            self.setalarm(text)

        def info(self, text):
            self.info(text)

        def warning(self, text):
            self.warning(text)

        def error(self, text):
            self.error(text)

        def get_state(self):
            addr = self.getstate()
            state = uctypes.struct(addr, STATE, uctypes.NATIVE)
            return state

        def is_stop_request(self):
            state = self.get_state()
            return state.stop

        # def clock(self):
        #     return int(self.clock())

        def run(self, flows, on_before_test_run_callback=None, on_after_test_run_callback=None):
            self.flows = flows
            self.recipe = recipe()

            self.recipe.dump()

            flows = self.flows
            if flows is None:
                self.error("There are no flows registered.")
                return None

            init_fn = flows["Init"]
            if init_fn is not None:
                init_fn(self.recipe.recipe_name())

            try:
                for segment in self.recipe.segments():
                    flow = segment.flow()
                    if flow in flows:
                        self.info("{0} starts ...".format(flow))

                        segment.dump()

                        if on_before_test_run_callback is not None:
                            on_before_test_run_callback(segment)

                        flows[flow](segment)

                        if on_after_test_run_callback is not None:
                            on_after_test_run_callback(segment)

                        if self.is_stop_request():
                            self.info("Flow stopped by user request")
                            break

                    else:
                        self.error("Flow {0} not registered.".format(flow))
                        break

            finally:
                terminate_fn = flows["Terminate"]
                if terminate_fn is not None:
                    terminate_fn()

                CLibManagerSingleton.unload_all()

except ImportError:

    class wsclass:
        def __init__(self):
            return

        def progress(self, text, value):
            return

        def set_alarm(self, text):
            return

        def info(self, text):
            return

        def warning(self, text):
            return

        def error(self, text):
            return

        def get_state(self):
            return 0

        def is_stop_request(self):
            return False

        def clock(self):
            return 0

        def run(self, flows, on_before_test_run_callback=None, on_after_test_run_callback=None):
            return





wsinstance = wsclass()


# ! @endcond

## <summary>	The progress info sent to the client.</summary>
#
# <param name="text">	progress text.</param>
# <param name="value">	progress value [0-100].</param>
def progress(text, value):
    wsinstance.progress(text, value)

## <summary>	The alarm info sent to the client.</summary>
#
# <param name="text">	alarm text.</param>
def set_alarm(text):
    wsinstance.set_alarm(text)
			
## <summary>	The trace info sent to the client.</summary>
#
# <param name="text">	trace info text.</param>
def info(text):
    wsinstance.info(text)


## <summary>	The trace warning sent to the client.</summary>
#
# <param name="text">	trace warning text.</param>
def warning(text):
    wsinstance.warning(text)


## <summary>	The trace error sent to the client.</summary>
#
# <param name="text">	trace error text.</param>
def error(text):
    wsinstance.error(text)


## <summary>	Return the state structure.</summary>
#
def get_state():
    return wsinstance.get_state()


## <summary>	Return true if the user request a stop of test program.</summary>
#
def is_stop_request():
    return wsinstance.is_stop_request()


# # ! @cond Doxygen_Suppress
# def clock():
#     return wsinstance.clock()


# ! @endcond

## <summary>	Register the flows defined to the test program
#				and execute the test program starting to parse the recipe manifest.</summary>
#
# @snippet std_examples.py Ws Run
def run(flows, on_before_test_run_callback=None, on_after_test_run_callback=None):
    wsinstance.run(flows, on_before_test_run_callback, on_after_test_run_callback)


## <summary>	Class variable of a recipe.</summary>
#
#  @ingroup wsApiGroup
class variable:
    # ! @cond Doxygen_Suppress
    def __init__(self, variable, addr):
        self.variable = variable
        self.addr = addr

    # ! @endcond

    ## <summary>	Return variable name.</summary>
    #
    def name(self):
        return cstr(self.variable.name)

    ## <summary>	Return variable type.</summary>
    #
    def type(self):
        return cstr(self.variable.type)

    ## <summary>	Return variable value.</summary>
    #
    def value(self):
        baseaddr = self.addr + 576
        valuestring = get_string_at(baseaddr)
        valuetype = cstr(self.variable.type)

        if valuetype == "System.Int32" or valuetype == "uint" or valuetype == "int":
            return int(valuestring)
        elif valuetype == "System.Double" or valuetype == "double":
            return float(valuestring)
        elif valuetype == "System.Boolean":
            return str2bool(valuestring)

        return valuestring


## <summary>	Class segment of a recipe.</summary>
#
#  @ingroup wsApiGroup
class segment:
    # ! @cond Doxygen_Suppress
    def __init__(self, segment, addr):
        self.segment = segment
        self.addr = addr
        self._variables = None

    # ! @endcond

    ## <summary>	Return segment name.</summary>
    #
    def name(self):
        return cstr(self.segment.name)

    ## <summary>	Return segment function.</summary>
    #
    def flow(self):
        return cstr(self.segment.function)

    ## <summary>	Return segment variables.</summary>
    #
    def variables(self, name=None):
        if self._variables is None:
            self._variables = []
            baseaddr = get_address_at(self.addr + 136)
            size_variable = uctypes.sizeof(VARIABLE)

            for i in range(0, self.segment.variableCount):
                addr = baseaddr + (i * size_variable)
                vi = uctypes.struct(addr, VARIABLE, uctypes.NATIVE)
                self._variables.append(variable(vi, addr))

        if name is not None:
            for var in self._variables:
                if var.name() == name:
                    return var

        return self._variables

    ## <summary>	Logs the segments structure.</summary>
    #
    def dump(self):
        sg_variables = self.variables()
        for sva in sg_variables:
            wsinstance.info(sva.name() + " : " + str(sva.value()))


# ! @cond Doxygen_Suppress
class flow:
    def __init__(self, flow, addr):
        self.flow = flow
        self.addr = addr
        self._variables = None

    ## <summary>	Return flow name.</summary>
    #
    def name(self):
        return cstr(self.flow.name)

    ## <summary>	Return flow category.</summary>
    #
    def category(self):
        return cstr(self.flow.category)

    ## <summary>	Return flow variables.</summary>
    #
    def variables(self):
        if self._variables is None:
            self._variables = []
            baseaddr = get_address_at(self.addr + 136)
            size_variable = uctypes.sizeof(VARIABLE)

            for i in range(0, self.flow.variableCount):
                addr = baseaddr + (i * size_variable)
                vi = uctypes.struct(addr, VARIABLE, uctypes.NATIVE)
                self._variables.append(variable(vi, addr))

        return self._variables


class recipe:
    def __init__(self):
        # Declare flows
        self._lib_handle = CLibManagerSingleton.load_libwslibpy()
        self.getmanifest = CLibManagerSingleton.load_func(self._lib_handle, "p", "getManifest", "")
        addr = self.getmanifest()
        self.manifest = uctypes.struct(addr, MANIFEST, uctypes.NATIVE)
        self.addr = addr
        self._variables = None
        self._flows = None
        self._segments = None

    def test_program(self):
        str = cstr(self.manifest.testProgram)
        pos = str.find("*")
        if pos == -1:
            return str
        return str[0:pos]

    def recipe_name(self):
        str = cstr(self.manifest.testProgram)
        pos = str.find("*")
        if pos == -1:
            return "Recipe Name not supported in this WS version"
        return str[pos + 1:]

    def variables(self):
        if self._variables is None:
            self._variables = []
            baseaddr = get_address_at(self.addr + 520)
            size_variable = uctypes.sizeof(VARIABLE)

            if self.manifest is not None:
                for i in range(0, self.manifest.variableCount):
                    addr = baseaddr + (i * size_variable)
                    vi = uctypes.struct(addr, VARIABLE, uctypes.NATIVE)
                    vi_obj = variable(vi, addr)
                    self._variables.append(vi_obj)

        return self._variables

    def flows(self):
        if self._flows is None:
            self._flows = []
            baseaddr = get_address_at(self.addr + 536)
            size_function = uctypes.sizeof(FUNCTION)

            if self.manifest is not None:
                for i in range(0, self.manifest.functionCount):
                    addr = baseaddr + (i * size_function)
                    fi = uctypes.struct(addr, FUNCTION, uctypes.NATIVE)
                    self._flows.append(flow(fi, addr))

        return self._flows

    def segments(self):
        if self._segments is None:
            self._segments = []
            baseaddr = get_address_at(self.addr + 552)
            size_segment = uctypes.sizeof(SEGMENT)

            if self.manifest is not None:
                for i in range(0, self.manifest.segmentCount):
                    addr = baseaddr + (i * size_segment)
                    si = uctypes.struct(addr, SEGMENT, uctypes.NATIVE)
                    self._segments.append(segment(si, addr))

        return self._segments

    def dump(self):
        indent = "   "
        print("Recipe: " + self.recipe_name())
        recipe = self
        variables = recipe.variables()
        for gva in variables:
            print(indent + gva.name() + " = " + str(gva.value()))

        print("Flows:")
        flows = recipe.flows()
        for fn in flows:
            print(indent + "Name: " + fn.name())
            print(indent + "Category: " + fn.category())

            print(indent + "Variables:")
            fn_variables = fn.variables()
            for fva in fn_variables:
                print(indent + indent + fva.name() + " = " + str(fva.value()))

        print("Segments:")
        segments = recipe.segments()
        for sg in segments:
            print(indent + "Name: " + sg.name())
            print(indent + "Flow: " + sg.flow())

            print(indent + "Variables:")
            sg_variables = sg.variables()
            for sva in sg_variables:
                print(indent + indent + sva.name() + " = " + str(sva.value()))
# ! @endcond