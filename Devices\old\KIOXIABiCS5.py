from Devices.OnfiDevice import COnfiDevice as COnfiDevice

from Devices.Toshiba import <PERSON><PERSON><PERSON> as CToshiba

import nanocycler

#################
### ALIASES   ###
#################

hw = nanocycler.hardware
datalog = nanocycler.datalog
utility = nanocycler.utility
ws = nanocycler.ws
pmu = nanocycler.pmu


###########################################################################
### Reference Datasheet: TH58LKTxY25BAxx_132BGA_D_20210218_0.2(TLC-2PL).pdf
###########################################################################

class CKIOXIABiCS5(CToshiba):
    def __init__(self):
        CToshiba.__init__(self)
        self.DEVICE_MANUFACTURER = "KIOXIA"
        self.DEVICE_NAME = "BiCS5"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2 # # Eric: per CH; TH58LKT0Y25BA4C, only 1 ce
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 2  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.LUN_NUMBER = 2    # Eric: per CH; TH58LKT0Y25BA4C, only 1 lun
        self.BLOCK_NUMBER = 3324
        self.WL_NUMBER = 448
        self.PAGE_NUMBER = (self.LEVEL_NUMBER * self.WL_NUMBER)  # 1344 per block

        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 9
        self.VALID_LUN_MASK = 0x01 # 2,097,152
        self.VALID_WL_MASK = 0x1FF
        self.VALID_BLOCK_MASK = 0xFFF
        
        self.VCC = 3.3
        self.VCCQ = 1.2


    # Figure 4‐2. SDR to DDR, DDR to SDR Mode Transition Diagram
    def die_configure(self, ch_list: [], ce_list: [], odt, driver_strength):

        COnfiDevice.device_reset(self, ch_list, ce_list)

        # Driver strength 10h
        # 00h~01h Reserved
        # 02h Ron = Driver Multiplier : Underdrive
        # 03h Reserved
        # 04h Ron = 35 Driver Multiplier : 1 (default)
        # 05h Reserved
        # 06h Ron = Driver Multiplier : Overdrive 1
        # 07h~FFh Reserved
        p1 = driver_strength
        p2 = 0
        p3 = 0
        p4 = 0
        lAddress = 0x10
        COnfiDevice.set_feature(self, ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # DDR interface 02h
        # bit 7-4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        bODT = odt
        bRE_C = 1
        bDQS_c = 1
        bVREF = 1
        p1 = (bODT << 4) | (bRE_C << 2) | (bDQS_c << 1) | (bVREF << 0)
        lAddress = 0x02
        COnfiDevice.set_feature(self, ch_list, ce_list, lAddress, p1, p2, p3, p4)




