# import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
# from nanocycler import hardware as hw
# from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
# from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
# from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
# from lib.ResultLogger import the_result_logger as logger
# from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

############################################################
### Dummy Device class
############################################################

class CNotValidDevice(COnfiDevice):
    def __init__(self):
        COnfiDevice.__init__(self)
        return

    def identification(self):
        ws.error("Device not available, the generic OnfiDevice will be used!")
        COnfiDevice.identification(self)
        return True

