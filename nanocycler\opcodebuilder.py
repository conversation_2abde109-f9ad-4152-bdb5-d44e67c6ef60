## @package nanocycler.opcodebuilder
# <summary>	Contains the operation code builder to compose all back-door command to be executed by Fpga </summary>
#
# @ingroup onfiApiGroupPyLanguage

from .libmanager import *


## <summary>	BackDoors to access the feature implemented in FPGA.
# 					It's an elementary step to create the sequence </summary>
#
# @ingroup wsApiGroup
class enumNandBackdoor:
    ## <summary>	representing the 'No Operation' command.
    # 					Non change in the signals. Just wait for n-nanoSec</summary>
    BD_NOP = 0

    ## <summary>	keeping signals state for a spcified nanosec value.</summary>
    # \image    html BD_DELAY.jpg
    BD_DELAY = 1

    ## <summary>	Command Latch Enable sequence. CLE(High), nWE(Low) than nWE(High), CLE(Low) </summary>
    # \image    html BD_CLE.jpg
    BD_CLE = 2

    ## <summary>	Address Latch Enable sequence. ALE(High), nWE(Low) than nWE(High), ALE(Low) </summary>
    # \image    html BD_ALE.jpg
    BD_ALE = 3

    ## <summary>	Sequence waiting for "Ready Not Busy" goes Low than High. It records the number of clock cycles between RnB low and high.</summary>
    # \image    html BD_R_NB.jpg
    BD_R_NB = 4

    ## <summary> Repeats n-times the single data rate data-in sequence
    # 					It sets databus at value coming from data generator.
    # 					For each value, a nWE pulse is generated. </summary>
    # \image    html BD_SDR_DATA_IN.jpg
    BD_SDR_DATA_IN = 5

    ## <summary> Repeats n-times the single data rate data-out sequence
    # 					For each repeatition, a RE/nRE pulse is generated.
    # 					The databus is acquired using DQS/nDQS of device </summary>
    # \image    html BD_SDR_DATA_OUT.jpg
    BD_SDR_DATA_OUT = 6

    ## <summary> Repeats n-times the double data rate data-in sequence
    # 					It sets databus at value coming from data generator.
    # 					For each value, a WE pulse is generated. </summary>
    # \image    html BD_DDR_DATA_IN.jpg
    BD_DDR_DATA_IN = 7

    ## <summary> Repeats n-times the double data rate data-out sequence
    # 					For each repeatition, a single RE/nRE edge is generated.
    # 					The databus is acquired using DQS/nDQS of device</summary>
    # \image    html BD_DDR_DATA_OUT.jpg
    BD_DDR_DATA_OUT = 8

    ## <summary> Applies a double data rate data-in sequence just one time
    # 					It sets databus by the OPCODE data and a single DQS/nDQS pulse is generated
    # 					Tipically used by Set Feature command only.
    # 					</summary>
    # <see cref=""> OPCODE </see>
    # \image    html BD_SINGLE_DATA_IN.jpg
    BD_SINGLE_DDR_DATA_IN = 9

    ## <summary>	When 1, DQS signal is driven by NanoCycler (used for data-in sequence),
    # 					when 0(default) the device can drives (mandatory for data-out sequence).
    # 					</summary>
    # \image    html BD_DQS_DRIVE.jpg
    BD_DQS_DRIVE = 10

    ## <summary>	Sets DQS_T low or high (DQS_C is automatically applied as complemented)</summary>
    # \image    html BD_DQS_T_STATE.jpg
    BD_DQS_T_STATE = 11

    ## <summary>	Set RE_T low or high (RE_C is automatically applied as complemented) </summary>
    # \image    html BD_RE_T_STATE.jpg
    BD_RE_T_STATE = 12

    ## <summary>	Drives ( High or Low ) the CE signal sequence.</summary>
    # \image    html BD_CE.jpg
    BD_CE = 13

    ## <summary>	Drives ( High or Low ) the WP signal sequence.</summary>
    # \image    html BD_WP.jpg
    BD_WP = 14

    ## <summary>	Start (1) or Stop (0) DQ(/IO) driving. As default DQ is not driven.</summary>
    # \image    html BD_DQ_DRIVE.jpg
    BD_DQ_DRIVE = 15

    ## <summary>	Resets data serializer.</summary>
    BD_RESET_DATA_GEN = 16

    ## <summary>	Enable fail counter.</summary>
    BD_COMPARE_ENABLE = 17

    ## <summary>	Drives CLE signal.</summary>
    BD_CLE_STATE = 18

    ## <summary>	Drives ALE signal.</summary>
    BD_ALE_STATE = 19

    ## <summary>	Data plus WR pulse.</summary>
    BD_DATA_WR = 20

    ## <summary>	Apply PMU Start Trigger.</summary>
    BD_PMU_START_TRIGGER = 21

    ## <summary>	Apply PMU Stop Trigger.</summary>
    BD_PMU_STOP_TRIGGER = 22

    ## <summary>	CLE with auto start of ready busy measurement .</summary>
    BD_CLE_RB = 23

    ## <summary>	Run a Sequence Table. The table must me previously compiled and loaded.</summary>
    BD_RUN_TABLE = 24

    ## <summary>   Resets the Input data serializer. MUST be called before BD_SDR_DATA_IN or BD_DDR_DATA_IN.</summary>
    BD_INIT_DATA_OUT = 25

    ## <summary>	Address Latch Enable sequence. ALE(High), nWE(Low) than nWE(High), ALE(Low), data is taken ram data, index specified by op_data </summary>
    BD_ALE_REG = 26

    ## <summary>	Command Latch Enable sequence. CLE(High), nWE(Low) than nWE(High), CLE(Low), data is taken from ram data, index specified by op_data </summary>
    BD_CLE_REG = 27

    ## <summary>	Apply LL part (bit 15...0) of the seed used by pattern generator </summary>
    BD_SEED_LL = 28

    ## <summary>	Apply LL part (bit 31...16) of the seed used by pattern generator </summary>
    BD_SEED_LH = 29

    ## <summary>	Apply LL part (bit 47...32) of the seed used by pattern generator </summary>
    BD_SEED_HL = 30

    ## <summary>	Apply LL part (bit 63...48) of the seed used by pattern generator </summary>
    BD_SEED_HH = 31

    ## <summary> Applies a single data rate data-in sequence just one time
    # 					It sets databus by the OPCODE data and a single WR pulse is generated
    # 					Tipically used by Set Feature command only.
    # 					</summary>
    # <see cref=""> OPCODE </see>
    # \image    html BD_SINGLE_DATA_IN.jpg
    BD_SINGLE_SDR_DATA_IN = 32

    ## <summary>	Stop command of Sequence.</summary>
    BD_STOP = 63

# ! @cond Doxygen_Suppress
class enumNandAddressByte:
    COL_ADDR_BYTE_0 = 0
    COL_ADDR_BYTE_1 = 1
    COL_ADDR_BYTE_2 = 2
    COL_ADDR_BYTE_3 = 3

    ROW_ADDR_BYTE_0 = 4
    ROW_ADDR_BYTE_1 = 5
    ROW_ADDR_BYTE_2 = 6
    ROW_ADDR_BYTE_3 = 7
# ! @endcond


_lib_handle = CLibManagerSingleton.load_libwslibpy()

_opbuilderapicreate = CLibManagerSingleton.load_func(_lib_handle, "p", "OpBuilderApi_Create", "L")
_opbuilderapidelete = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_Delete", "p")
_opbuilderapisetcelow = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetCeLow", "p")
_opbuilderapisetcehigh = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetCeHigh", "p")
_opbuilderapisetwplow = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetWpLow", "p")
_opbuilderapisetwphigh = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetWpHigh", "p")
_opbuilderapiadd = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_Add", "piL")
_opbuilderapiclear = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_Clear", "p")
_opbuilderapisetcolumnaddress = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetColumnAddress", "pL")
_opbuilderapisetrowaddress = CLibManagerSingleton.load_func(_lib_handle, "v", "OpBuilderApi_SetRowAddress", "pL")


## <summary>	The operation code builder to compose all back-door command to be executed by Fpga.</summary>
#
# \image html OpCodeBuilderExample.jpg
#
#  @ingroup wsApiGroup
# @snippet std_examples.py OpcodeBuilder Example
class opcodebuilder:
    # ! @cond Doxygen_Suppress
    def __init__(self, lCeIndex: int):
        self.opcodebptr = _opbuilderapicreate(lCeIndex)

    # ! @endcond

    ## <summary>	Cleanup function to unload the library from memory.</summary>
    def cleanup(self):
        if self.opcodebptr is not None:
            _opbuilderapidelete(self.opcodebptr)

    # self.npy.close()

    ## <summary>	Get pointer of opcode builder object.</summary>
    #
    # <returns>	return pointer of opcode builder object for execution.</returns>
    def get_ptr(self):
        return self.opcodebptr

    ## <summary>	Adds op-code to set Chip Enable signal low.</summary>
    def set_ce_low(self):
        _opbuilderapisetcelow(self.opcodebptr)

    ## <summary>	Adds op-code to set Chip Enable signal high.</summary>
    def set_ce_high(self):
        _opbuilderapisetcehigh(self.opcodebptr)

    ## <summary>	Adds op-code to set Write Protect low.</summary>
    def set_wp_low(self):
        _opbuilderapisetwplow(self.opcodebptr)

    ## <summary>	Adds op-code to set Write Protect high.</summary>
    def set_wp_high(self):
        _opbuilderapisetwphigh(self.opcodebptr)

    ## <summary>	Adds the op-code by NandBackdoor and value.</summary>
    #
    # <param name="eNandBackdoor">	The Nand Backdoor.</param>
    # <param name="data">	The data.</param>
    def add(self, eNandBackdoor: enumNandBackdoor, data):
        _opbuilderapiadd(self.opcodebptr, eNandBackdoor, int(data))

    ## <summary>	Clears the builder.</summary>
    def clear(self):
        _opbuilderapiclear(self.opcodebptr)

    ## <summary>	Adds op-code(s) to set column address.</summary>
    #
    # <param name="lColAddress">	The column address.</param>
    def set_column_address(self, lColAddress: int):
        _opbuilderapisetcolumnaddress(self.opcodebptr, lColAddress)

    ## <summary>	Adds op-code(s) to set row address.</summary>
    #
    # <param name="lRowAddress">	The row address.</param>
    def set_row_address(self, lRowAddress: int):
        _opbuilderapisetrowaddress(self.opcodebptr, lRowAddress)
