import nanocycler

# from nanocycler import NanoTimer as time
# from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

# from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM

from Devices.YMTC import CYMTC as CYMTC


###########################################################################
### Reference Datasheet:
###########################################################################

class YMTC_X26070_CMD:
    COARSE_PROGRAMMING_DCh = 0xDC


class CYMTCX26070(CYMTC):
    def __init__(self):
        CYMTC.__init__(self)
        self.DEVICE_MANUFACTURER = "YMTC"
        self.DEVICE_NAME = "X26070"

        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18432
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = (self.PAGE_LENGTH // self.CHUNK_NUMBER)

        self.PLANE_NUMBER = 6  # 6 plane inside the lun
        self.STRING_NUMBER = self.PLANE_NUMBER
        self.LEVEL_NUMBER = 4  # QLC 4 levels
        self.WL_NUMBER = 768
        self.LUN_NUMBER = 8
        self.BLOCK_NUMBER = 3942 # 657 * self.PLANE_NUMBER # it is just a number it depends on device size
        self.PAGE_NUMBER = 3048  # per block

        self.LUN_START_BIT_ADDRESS = 25
        self.BLOCK_START_BIT_ADDRESS = 12
        self.VALID_LUN_MASK = 0x07
        self.VALID_BLOCK_MASK = 0x1FFF
        self.VALID_PAGE_MASK = 0xFFF

        self.VT_LEVEL = 15 # QLC device

        self.MAX_ERASE_TIME = 20000  # 20 msec
        self.MAX_PROG_TIME = 10000  # 10 msec
        self.MAX_READ_TIME = 400  # 200 usec

        self.VCC = 2.5
        self.VCCQ = 1.2


    def die_configure(self, ch_list: [], ce_list: [], odt, driver_strength):

        is_sdr = self.VCCQ > 1.5

        if is_sdr:
            p1 = (odt << 4) | (0x01 << 2) | (0x01 << 1) | (0x01 << 0)
            p2 = 0
            p3 = 0
            p4 = 0
            self.set_feature_async(ch_list, ce_list, 0x02, p1, p2, p3, p4)

            p1 = driver_strength
            self.set_feature_async(ch_list, ce_list, 0x10, p1, p2, p3, p4)

            p1 = 0x20  # Set NV-DDR NOTE: It must be the last step when all feature had been set, Due to set feature need Async.
            self.set_feature_async(ch_list, ce_list, 0x01, p1, p2, p3, p4)
        else:
            p1 = (odt << 4) | (0x01 << 2) | (0x01 << 1) | (0x01 << 0)
            p2 = 0
            p3 = 0
            p4 = 0
            self.set_feature(ch_list, ce_list, 0x02, p1, p2, p3, p4)

            p1 = driver_strength
            self.set_feature(ch_list, ce_list, 0x10, p1, p2, p3, p4)

            p1 = 0x00
            self.set_feature(ch_list, ce_list, 0x01, p1, p2, p3, p4)


    CALIB_LEN = 4096

    def calib_setup(self, data_rate_mhz, calib_length, ce = 0, lun = 0):

        column_address = 0
        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(ce)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 400)  # tADL
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_length)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.page_buffer_write_builder(opcbuilder)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tCS)
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tRR_PB)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DELAY, self.tPRE)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_length)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.page_buffer_read_builder(opcbuilder)

        opcbuilder.cleanup()

        return True


    # ###########################################################
    # Utilities

    def page_info(self, page):

        if page < 18:
            levels = 3 # TLC
            idx = page - 0
            offset = 0
        elif page < 1506:
            levels = 4 # QLC
            idx = page - 18
            offset = 1
        elif page < 1542:
            levels = 3 # TLC
            idx = page - 1506
            offset = 63
        elif page < 3030:
            levels = 4 # QLC
            idx = page - 1542
            offset = 65
        else:
            levels = 3 # TLC
            idx = page - 3030
            offset = 127

        return  idx // (levels * self.STRING_NUMBER) + offset, idx % levels, levels


    def wl_info(self, wl):
        if wl < 1:
            levels = 3  # TLC
            first_page = 0
        elif wl < 63:
            levels = 4  # QLC
            first_page = 18 + (wl - 1) * levels * self.STRING_NUMBER
        elif wl < 65:
            levels = 3  # TLC
            first_page = 1506 + (wl - 63) * levels * self.STRING_NUMBER
        elif wl < 127:
            levels = 4  # QLC
            first_page = 1542 + (wl - 65) * levels * self.STRING_NUMBER
        else:
            levels = 3  # TLC
            first_page = 3030 + (wl - 127) * levels * self.STRING_NUMBER

        return first_page, levels


    def pages_to_wl_range(self, page_list):
        wl_min = 128
        wl_max = 0
        for page in page_list:
            wl, weight, levels = self.page_info(page)
            if wl < wl_min:
                wl_min = wl
            if wl > wl_max:
                wl_max = wl

        return wl_min, wl_max



    # ###########################################################
    # QLC  Program Sequence

    def qlc_program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column, is_coarse = False):

        wl, weight, levels  = self.page_info(page)

        # ws.info("Coarse Program Page: {0} - WL: {1} - Weight: {2} - Levels: {3}".format(page, wl, weight,  levels))

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            bRes, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if is_coarse:
                opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_X26070_CMD.COARSE_PROGRAMMING_DCh)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            # be carefull in case of multi block multi lun only the last will be acquired
            if bI4:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            if weight == levels - 1:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if weight == levels - 1:
            logger.log_program("coarse" if is_coarse else "fine", ch_list, ce_list, lun_list, [block], page, en_i=bI2 or bI4)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def program_block(self, ch_list: [], ce_list: [], lun_list: [], block, page_list : []):
        bOk = True

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)

        last_program_pages = []

        for wl in range(wl_min, wl_max+1):

            first_page, levels = self.wl_info(wl)

            # pages from program coarse
            current_program_pages = range(first_page, first_page + levels * self.STRING_NUMBER)

            if levels == 4:
                # coarse program on current
                for page in current_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, True)

                # fine program on last
                for page in last_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                last_program_pages = current_program_pages
            else:
                # fine program on last
                for page in last_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                # fine program on current
                for page in current_program_pages:
                    self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

                last_program_pages = []

        # fine program on last
        for page in last_program_pages:
            self.qlc_program_page(ch_list, ce_list, lun_list, block, page, 0, False)

        return bOk


    # ###########################################################
    # QLC  Multiplane Program Sequence

    def qlc_multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column, is_coarse = False):

        wl, weight, levels = self.page_info(page)

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4 = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        if is_coarse:
            opcbuilder.add(eNandBackdoor.BD_CLE, YMTC_X26070_CMD.COARSE_PROGRAMMING_DCh)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)

                # pattern is the same between ce and ch use the first
                # CBaseDevice.recall_pattern(self, ce_list[0], lun, block, page)
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2)  # 2 to use register seed

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (column >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                # be carefull in case of multi block multi lun only the last will be acquired
                if bI4:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if weight == levels - 1:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    else:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        if weight == levels - 1:
            logger.log_program("coarse" if is_coarse else "fine", ch_list, ce_list, lun_list, block_list, page)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    def multi_plane_program_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page_list: []):
        bOk = True

        # calc wl range
        wl_min, wl_max = self.pages_to_wl_range(page_list)

        last_program_pages = []

        for wl in range(wl_min, wl_max+1):

            first_page, levels = self.wl_info(wl)

            # pages from program coarse
            current_program_pages = range(first_page, first_page + levels * self.STRING_NUMBER)

            if levels == 4:
                # coarse program on current
                for page in current_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, True)

                # fine program on last
                for page in last_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)

                last_program_pages = current_program_pages
            else:
                # fine program on last
                for page in last_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)

                # fine program on current
                for page in current_program_pages:
                    self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)

                last_program_pages = []

        # fine program on last
        for page in last_program_pages:
            self.qlc_multi_plane_program_page(ch_list, ce_list, lun_list, block_list, page, 0, False)

        return bOk


    # #################################################
    # READ OFFSET

    def offset_to_voltage(self, offset):
        offset_voltage = -1280 + offset * 10.0

        return offset_voltage


    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):

        if level > 14:
            return False, 0.0

        offset_code = self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        # QLC
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA0, offset_code, 0x00, 0x00, 0x00)
        if level == 7:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, offset_code, 0x00, 0x00)
        if level == 13:
            self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, offset_code, 0x00)

        if level == 2:
            self.set_feature(ch_list, [ce], 0xA1, offset_code, 0x00, 0x00, 0x00)
        if level == 6:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, offset_code, 0x00, 0x00)
        if level == 8:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, offset_code, 0x00)
        if level == 12:
            self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, offset_code)

        if level == 4:
            self.set_feature(ch_list, [ce], 0xA2, offset_code, 0x00, 0x00, 0x00)
        if level == 9:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, offset_code, 0x00, 0x00)
        if level == 11:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, offset_code, 0x00)
        if level == 14:
            self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, offset_code)

        if level == 0:
            self.set_feature(ch_list, [ce], 0xA3, offset_code, 0x00, 0x00, 0x00)
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, offset_code, 0x00, 0x00)
        if level == 5:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, offset_code, 0x00)
        if level == 10:
            self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, offset_code)

        return True, offset_voltage


    def reset_read_offset_code(self, ch_list: [], ce, lun, level):

        # QLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)

        return True


    # #################################################
    # READ RETRY

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:

            # ws.info(">>> Option {0}/{1}".format(retry_option, len(self.option_buffer)))

            if retry_option >= len(self.option_buffer):
                return False, ""

            # csv file should contains lines of 15 parameters
            if len(self.option_buffer[retry_option]) < 15:
                return False, ""

            # QLC
            self.set_feature(ch_list, [ce], 0xA0, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][7], 16), int(self.option_buffer[retry_option][13], 16), 0x00)
            self.set_feature(ch_list, [ce], 0xA1, int(self.option_buffer[retry_option][2], 16), int(self.option_buffer[retry_option][6], 16), int(self.option_buffer[retry_option][8], 16), int(self.option_buffer[retry_option][12], 16))
            self.set_feature(ch_list, [ce], 0xA2, int(self.option_buffer[retry_option][4], 16), int(self.option_buffer[retry_option][9], 16), int(self.option_buffer[retry_option][11], 16), int(self.option_buffer[retry_option][14], 16))
            self.set_feature(ch_list, [ce], 0xA3, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][3], 16), int(self.option_buffer[retry_option][5], 16), int(self.option_buffer[retry_option][10], 16))
            # add here other column if necessary

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""


    def reset_read_retry_option(self, ch_list: [], ce, lun):
        # QLC
        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0xA3, 0x00, 0x00, 0x00, 0x00)
        # add here other column if necessary
        return True
