## @package nanocycler.ncdb
# <summary>	Contains the ncdb interface that implements part of the Open Nand Flash Interfaceas
# 					documented by the standard specification - Revision 4.0
# 					It supports 2 channels only</summary>
#
# @ingroup ncdbApiGroupPyLanguage

from .libmanager import *
from .utility import *

_lib_handle = CLibManagerSingleton.load_libncdblib()

_insertorupdatedevice = CLibManagerSingleton.load_func(_lib_handle, "L", "InsertOrUpdateDevice", "ppppp")

_badblockcount = CLibManagerSingleton.load_func(_lib_handle, "L", "BadBlockCount", "p")
_badblockremoveall = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockRemoveAll", "p")
_badblockload = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockLoad", "ppppL")
_badblockstore = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockStore", "pLLL")
_badblockbatchinit = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockBatchInit", "p")
_badblockbatchstore = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockBatchStore", "LLL")
_badblockbatchcommit = CLibManagerSingleton.load_func(_lib_handle, "b", "BadBlockBatchCommit", "")

_patternseedcount = CLibManagerSingleton.load_func(_lib_handle, "L", "PatternSeedCount", "p")
_patternseedremoveall = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedRemoveAll", "p")
_patternseedload = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedLoad", "ppppL")
_patternseedstore = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedStore", "pLLL")
_patternseedbatchinit = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedBatchInit", "p")
_patternseedbatchstore = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedBatchStore", "LLL")
_patternseedbatchcommit = CLibManagerSingleton.load_func(_lib_handle, "b", "PatternSeedBatchCommit", "")

_loadfromcentral = CLibManagerSingleton.load_func(_lib_handle, "b", "LoadFromCentral", "pL")
_storetocentral = CLibManagerSingleton.load_func(_lib_handle, "b", "StoreToCentral", "pL")



# ! @cond Doxygen_Suppress

def bad_block_load(uid: str):
    count = _badblockcount(uid)
    bRes = True
    res_dict = {}
    if count > 0:
        aLun = bytearray(4 * count)
        addrLunBuffer = address_of(aLun)
        aBlock = bytearray(4 * count)
        addrBlockBuffer = address_of(aBlock)
        aCategory = bytearray(4 * count)
        addrCategoryBuffer = address_of(aCategory)
        bRes = _badblockload(uid, addrLunBuffer, addrBlockBuffer, addrCategoryBuffer, count)
        for i in range(0, count):
            base_index = i * 4
            lun = get_int_from_bytes(aLun[base_index: base_index + 4])
            block = get_int_from_bytes(aBlock[base_index: base_index + 4])
            category = get_int_from_bytes(aCategory[base_index: base_index + 4])
            if lun not in res_dict:
                res_dict[lun] = []
            if block not in res_dict[lun]:
                res_dict[lun].append((block, category))
    return bool(bRes), res_dict


def bad_block_store(uid: str, lun: int, block: int, category: int):
    bRes = _badblockstore(uid, lun, block, category)
    return bool(bRes)


def bad_block_batch_init(uid: str):
    bRes = _badblockbatchinit(uid)
    return bool(bRes)

def bad_block_batch_store(lun: int, block: int, seed: int):
    bRes = _badblockbatchstore(lun, block, seed)
    return bool(bRes)

def bad_block_batch_commit():
    bRes = _badblockbatchcommit()
    return bool(bRes)

def bad_block_remove_all(uid: str):
    bRes = _badblockremoveall(uid)
    return bool(bRes)


def pattern_seed_load(uid: str):
    count = _patternseedcount(uid)
    bRes = True
    res_dict = {}

    if count > 0:
        aLunBuffer = bytearray(4 * count)
        addrLunBuffer = address_of(aLunBuffer)
        aBlockBuffer = bytearray(4 * count)
        addrBlockBuffer = address_of(aBlockBuffer)
        aSeedBuffer = bytearray(4 * count)
        addrSeedBuffer = address_of(aSeedBuffer)
        bRes = _patternseedload(uid, addrLunBuffer, addrBlockBuffer, addrSeedBuffer, count)
        for i in range(0, count):
            base_index = i * 4
            lun = get_int_from_bytes(aLunBuffer[base_index: base_index + 4])
            block = get_int_from_bytes(aBlockBuffer[base_index: base_index + 4])
            seed = get_signed_int_from_bytes(aSeedBuffer[base_index: base_index + 4])
            if lun not in res_dict:
                res_dict[lun] = {}
            if block not in res_dict[lun]:
                res_dict[lun].update({block: seed})
    return bool(bRes), res_dict


def pattern_seed_store(uid: str, lun: int, block: int, seed: int):
    bRes = _patternseedstore(uid, lun, block, seed)
    return bool(bRes)

def pattern_seed_batch_init(uid: str):
    bRes = _patternseedbatchinit(uid)
    return bool(bRes)

def pattern_seed_batch_store(lun: int, block: int, seed: int):
    bRes = _patternseedbatchstore(lun, block, seed)
    return bool(bRes)

def pattern_seed_batch_commit():
    bRes = _patternseedbatchcommit()
    return bool(bRes)

def pattern_seed_remove_all(uid: str):
    bRes = _patternseedremoveall(uid)
    return bool(bRes)


def insert_or_update_device(uid: str, dev_name: str, dev_id: str, dev_model: str, manufacturer: str):
    bRes = _insertorupdatedevice(uid, dev_name, dev_id, dev_model, manufacturer)
    return bool(bRes)


def load_from_central(uid: str, timeout: int = 5):
        bRes = _loadfromcentral(uid, timeout)
        return bool(bRes)


def store_to_central(uid: str, timeout: int = 5):
    bRes = _storetocentral(uid, timeout)
    return bool(bRes)

