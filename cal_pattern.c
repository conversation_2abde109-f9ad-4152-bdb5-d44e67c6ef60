// cal_pattern.c - Pattern calculation library for bit operations
#include "cal_pattern.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Bit count lookup table
static const uint8_t BIT_COUNT_TABLE[256] = {
    0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4,
    1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
    1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
    2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
    1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
    2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
    2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
    3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
    1, 2, 2, 3, 2, 3, 3, 4, 2, 3, 3, 4, 3, 4, 4, 5,
    2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
    3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
    2, 3, 3, 4, 3, 4, 4, 5, 3, 4, 4, 5, 4, 5, 5, 6,
    3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
    3, 4, 4, 5, 4, 5, 5, 6, 4, 5, 5, 6, 5, 6, 6, 7,
    4, 5, 5, 6, 5, 6, 6, 7, 5, 6, 6, 7, 6, 7, 7, 8
};

// Calculate page FBC with XOR operation
void xor_cal_page_fbc_pattern_and_fbc(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    uint8_t *fbc_pattern,
    size_t len,
    size_t *page_fbc_16KB,
    size_t *page_fbc_4KB_list
) {
    size_t chunk_size = len / 4;
    *page_fbc_16KB = 0;
    for (size_t i = 0; i < 4; i++){
        page_fbc_4KB_list[i] = 0;
    } 

    for (size_t chunk_index = 0; chunk_index < 4; chunk_index++) {
        for (size_t i = chunk_index * chunk_size; i < (chunk_index + 1) * chunk_size; i++) {
            fbc_pattern[i] = pattern1[i] ^ pattern2[i];
            *page_fbc_16KB += BIT_COUNT_TABLE[fbc_pattern[i]];
            page_fbc_4KB_list[chunk_index] += BIT_COUNT_TABLE[fbc_pattern[i]];
        }
    }
}

// 统计 buf 中所有 bit 为 1 的数量
int64_t get_bit_count(const uint8_t *buf, size_t len) {
    if (!buf || len == 0) {
    return 0;
    }
    int64_t count = 0;
    for (size_t i = 0; i < len; i++) {
        count += BIT_COUNT_TABLE[buf[i]];
    }
    return count;
}

// 支持最多三个 pattern 及其操作（"" 或 "~"），做 AND 运算，结果写入 result
void and_cal_state_cell_pattern(
    const uint8_t *pattern1, const char *operation1,
    const uint8_t *pattern2, const char *operation2,
    const uint8_t *pattern3, const char *operation3,
    uint8_t *result, size_t len) {
    if (!result || len == 0) return;
    bool invert1 = (pattern1 && operation1 && operation1[0] == '~' && operation1[1] == '\0');
    bool invert2 = (pattern2 && operation2 && operation2[0] == '~' && operation2[1] == '\0');
    bool invert3 = (pattern3 && operation3 && operation3[0] == '~' && operation3[1] == '\0');
    for (size_t i = 0; i < len; i++) {
        uint8_t v = 0xFF;
        if (pattern1) {
            uint8_t t = pattern1[i];
            if (invert1) t = ~t;
            v &= t;
        }
        if (pattern2) {
            uint8_t t = pattern2[i];
            if (invert2) t = ~t;
            v &= t;
        }
        if (pattern3) {
            uint8_t t = pattern3[i];
            if (invert3) t = ~t;
            v &= t;
        }
        result[i] = v;
    }
}

// 两个 pattern 做 AND 运算，结果写入 result
void and_cal_state_fbc_pattern(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    uint8_t *result,
    size_t len) {
    if (!pattern1 || !pattern2 || !result || len == 0) return;
    for (size_t i = 0; i < len; i++) {
        result[i] = pattern1[i] & pattern2[i];
    }
}

// 两个 pattern 做 AND 运算，统计所有 1 的数量
int64_t and_cal_state_fbc(
    const uint8_t *pattern1,
    const uint8_t *pattern2,
    size_t len) {
    if (!pattern1 || !pattern2 || len == 0) return 0;
    int64_t count = 0;
    for (size_t i = 0; i < len; i++) {
        count += BIT_COUNT_TABLE[pattern1[i] & pattern2[i]];
    }
    return count;
}