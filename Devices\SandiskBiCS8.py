import nanocycler

from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.Sandisk import CSandisk as CSandisk
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM


###########################################################################
### Reference Datasheet:
###########################################################################

class CSandiskBiCS8(CSandisk):
    def __init__(self):
        COnfiDevice.__init__(self)
        self.DEVICE_MANUFACTURER = "Sandisk"
        self.DEVICE_NAME = "BiCS8"

        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 6

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = self.PAGE_LENGTH // self.CHUNK_NUMBER

        self.PLANE_NUMBER = 4  # 2 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.WL_NUMBER = 218 # hre WL number equals layer number
        self.LUN_NUMBER = 8
        self.BLOCK_NUMBER = 2748
        self.PAGE_NUMBER = 3270  # per block

        self.LUN_START_BIT_ADDRESS = 23
        self.BLOCK_START_BIT_ADDRESS = 11
        self.VALID_LUN_MASK = 0x07
        self.VALID_WL_MASK = 0xFF # here not include string, just 162 WL(layer)
        self.VALID_BLOCK_MASK = 0xFFF
        self.ROW_BYTE_ADDRESS = 4

        self.aLevels = ["0", "1", "2"]
        self.VT_SCAN = [0, 2, 4, 6]
        self.VT_LEVEL_VALID = [[0,4],[1,3,5],[2,6]] #hang, add for read_offset_best

    def get_valid_level_for_page(self, page):   #hang, add for read_offset_best
        return self.VT_LEVEL_VALID[page%3]

    def read_unique_id(self, ch_list, ce, lun = 0):

        ce_mask = self.index_list_to_mask([ce])

        wls = [11, 35]

        for ch in ch_list:
            self.select_channel(ch)

            ch_mask = self.index_list_to_mask([ch])

            uid_valid = False

            for wl in wls:
                if uid_valid:
                    break

                for s in range(4):
                    if uid_valid:
                        break

                    xx = s * 2
                    yy = wl << 3
                    # lun_a = lun * 0x20
                    if lun == 0:
                        cyc_addr_5th = 0x00
                        cyc_addr_6th = 0x00
                    elif lun == 1:
                        cyc_addr_5th = 0x80
                        cyc_addr_6th = 0x00
                    elif lun == 2:
                        cyc_addr_5th = 0x00
                        cyc_addr_6th = 0x01
                    elif lun == 3:
                        cyc_addr_5th = 0x80
                        cyc_addr_6th = 0x01

                    opcbuilder = nanocycler.opcodebuilder(0)
                    opcbuilder.set_ce_low()
                    opcbuilder.set_wp_high()
                    opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
                    opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0x5A)
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0xB5)
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0x00)
                    opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
                    opcbuilder.add(eNandBackdoor.BD_ALE, xx)
                    opcbuilder.add(eNandBackdoor.BD_ALE, yy)
                    opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
                    # opcbuilder.add(eNandBackdoor.BD_ALE, lun_a)
                    opcbuilder.add(eNandBackdoor.BD_ALE, cyc_addr_5th)
                    opcbuilder.add(eNandBackdoor.BD_ALE, cyc_addr_6th)
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0x30)

                    opcbuilder.add(eNandBackdoor.BD_CLE, 0x05)
                    opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
                    opcbuilder.add(eNandBackdoor.BD_ALE, xx)
                    opcbuilder.add(eNandBackdoor.BD_ALE, yy)
                    opcbuilder.add(eNandBackdoor.BD_ALE, 0x00)
                    # opcbuilder.add(eNandBackdoor.BD_ALE, lun_a)
                    opcbuilder.add(eNandBackdoor.BD_ALE, cyc_addr_5th)
                    opcbuilder.add(eNandBackdoor.BD_ALE, cyc_addr_6th)
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0xE0)

                    opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                    opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
                    opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
                    opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
                    # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 16) # SDR to have lower speed (valid only for HS because data out is performed with custom_sequence_ddr_get_data_byte)
                    opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 32)
                    opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
                    opcbuilder.add(eNandBackdoor.BD_CLE, 0xFF)
                    opcbuilder.set_ce_high()
                    opcbuilder.set_wp_low()
                    hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                    opcbuilder.cleanup()

                    res, buffer = hw.custom_sequence_get_out_buffer(32)

                    uid_valid = True

                    even = []
                    odd = []
                    for i in range(16):
                        even.append(buffer[2 * i])
                        odd.append(buffer[2 * i + 1])
                        check = buffer[2 * i] | buffer[2 * i + 1]
                        if check != 0xFF:
                            uid_valid = False

                    if uid_valid:
                        uid = self.format_array(even)
                        logger.log_device_unique_id([ch], [ce], lun, "UID", uid)
                        self.load_info(ch, ce, lun, uid, self.DEVICE_NAME, self.device_id, self.manufacturer_code, self.model)
                    else:
                        ws.error("Ch: {0} - Ce: {1} - Lun: {2} - WL: {3} - Set: {4} is not valid - {5} / {6}".format(ch, ce, lun, wl, s, even, odd))

    
    def build_row_address(self, lun, block, word_line):
        string = word_line % 5
        wl = word_line // 5
        # wl = page // self.LEVEL_NUMBER

        lRowAddress = (((lun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | (
                (block & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (wl & self.VALID_WL_MASK) << 3 | (string % 5))
        return True, lRowAddress
    
    # Figure 40: Multi-Plane TLC Read Example, Primary
    def multi_plane_single_page_read_disturb(self, ch_list: [], ce, lun, list_block_list: [[]], page, lColumn, page_length, cycles, loop):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = lColumn

        opcbuilder = nanocycler.opcodebuilder(0)
        for block_list in list_block_list:

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()

            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # bRes, lRowAddress = COnfiDevice.build_row_address(self, lun, block, word_line)
                bRes, lRowAddress = self.build_row_address(lun, block, word_line)

                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.set_row_address(lRowAddress)
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()

        startT = hw.get_nsec_time()
        for cycle in range(0, cycles):
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            if (cycle+1) == cycles:
                endT = hw.get_nsec_time()
                elapsed = (endT - startT) / 1000000000
                ws.info("Done: MP-Single Page RD with {0} count - on Block {1} - Page {2} - Elapsed Time {3:.3f} sec".format(cycles*(loop+1), block_list, page, elapsed))
                startT = hw.get_nsec_time()

        opcbuilder.cleanup()

        
    # Figure 40: Multi-Plane TLC Read Example, Primary
    def multi_plane_block_read_disturb(self, ch_list: [], ce, lun, block_list: [], page, lColumn, page_length):

        ch_mask = COnfiDevice.index_list_to_mask(self, ch_list)
        ce_mask = COnfiDevice.index_list_to_mask(self, [ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = lColumn

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        opcbuilder.clear()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            # bRes, lRowAddress = COnfiDevice.build_row_address(self, lun, block, word_line)
            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(lRowAddress)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)

        opcbuilder.cleanup()

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:

            # ws.info(">>> Option {0}/{1}".format(retry_option, len(self.option_buffer)))

            if retry_option >= len(self.option_buffer):
                return False, ""

            # csv file, each line should contain:
            if len(self.option_buffer[retry_option]) >= 7:
                
                # 7 parameters for TLC
                # A/C/E/G
                self.set_feature(ch_list, [ce], 0x12, int(self.option_buffer[retry_option][0], 16), int(self.option_buffer[retry_option][2], 16),
                                        int(self.option_buffer[retry_option][4], 16), int(self.option_buffer[retry_option][6], 16))
                
                # B/D/F
                self.set_feature(ch_list, [ce], 0x13, int(self.option_buffer[retry_option][1], 16), int(self.option_buffer[retry_option][3], 16),
                                        int(self.option_buffer[retry_option][5], 16), 0x00)
            elif len(self.option_buffer[retry_option]) >= 8:
                # 1 parameter for SLC
                self.set_feature(ch_list, [ce], 0x14, int(self.option_buffer[retry_option][7], 16), 0x00, 0x00, 0x00)
            else:
                return  False, ""

            return True, "{0}".format(hex(retry_option))
        else:
            # default code not implemented
            return False, ""

    def set_read_offset_code_multi_level(self, ch, ce, lun, page, offset4level):    #hang, add for read_offset_fitting_best
        for i in range(len(offset4level)):
            offset4level[i] = (offset4level[i] + 0x100) & 0xFF

        if len(offset4level) >= 7: #only for TLC
            # A/C/E/G
            self.set_feature([ch], [ce], 0x12, offset4level[0], offset4level[2], offset4level[4], offset4level[6])
            # B/D/F
            self.set_feature([ch], [ce], 0x13, offset4level[1], offset4level[3], offset4level[5], 0x00)
        else:
            return False
        
        return True

    def reset_read_retry_option(self, ch_list: [], ce, lun):
        # TLC
        self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, 0x00, 0x00)
        # SLC
        self.set_feature(ch_list, [ce], 0x14, 0x00, 0x00, 0x00, 0x00)
        # add here other column if necessary
        return True

    def set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 6:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0x12, shift_code, 0x00, 0x00, 0x00) # page LSB
        if level == 1:
            self.set_feature(ch_list, [ce], 0x13, shift_code, 0x00, 0x00, 0x00) # page CSB
        if level == 2:
            self.set_feature(ch_list, [ce], 0x12, 0x00, shift_code, 0x00, 0x00) # page MSB
        if level == 3:
            self.set_feature(ch_list, [ce], 0x13, 0x00, shift_code, 0x00, 0x00) # page CSB
        if level == 4:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, shift_code, 0x00) # page LSB
        if level == 5:
            self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, shift_code, 0x00) # page CSB
        if level == 6:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, shift_code) # page MSB

        # SLC
        self.set_feature(ch_list, [ce], 0x14, shift_code, 0x00, 0x00, 0x00)

        return True, shift_voltage

    def read_retry_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.vt_page_compare(ch_list, ce, lun, block, page, column, page_length)

    def vt_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        self.recall_pattern(ch_list, ce, lun, block, page)

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        column_address = column
        bRes, lRowAddress = self.build_row_address(lun, block, word_line)

        opcbuilder = nanocycler.opcodebuilder(ce)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5D)
        opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_read("READ", ch_list, ce, lun, [block], page)