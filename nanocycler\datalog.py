## @package nanocycler.datalog
# <summary>	The datalog api allows to create a csv file with the following fields:
#           - Id - line identification
#           - SegmentIndex - index of running segment
#           - SegmentName - name of running segment
#           - Label - test identifier ( e.g CALIB, CYCLING...)
#           - Condition - conditions of the test ( e.g Vcc=3.3, VccQ=1.8...)
#           - Function - function called inside the test ( e.g ERS, PRG, READOUT...)
#           - Counter - Counter
#           - Temperature - the current temperature of socket
#           - Vcc - the current Vcc applied on the tester unit
#           - VccQ - the current VccQ applied on the tester unit
#           - Vpp - the current Vpp applied on the tester unit
#           - DataRate - the current DataRate(MT/s) applied on the tester unit
#           - Channel - the selected internal chip under test
#           - Ce - the selected Chip Enable Index
#           - Lun - the selected Lun index
#           - Block - the selected Block Index
#           - Page - the selected Page index
#           - WordLine - the selected Wordline index
#           - level - the selected level of page
#           - Tag - general tag for custom usage ( e.g CHUNK_0... )
#           - Measurement - measurement of measure as string ( e.g nSecREAD, nSecERS, FAILCOUNT...)
#           - Value - value of the measure
# </summary>
# @ingroup wsApiGroupPyLanguage

from .libmanager import *
from .utility import *

_lib_handle = CLibManagerSingleton.load_libwslibpy()

_open = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_Open", "sB")
_close = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_Close", "")
_flush = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_Flush", "")
_isopen = CLibManagerSingleton.load_func(_lib_handle, "i", "DataloggerApi_IsOpen", "")

_setsegment = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetSegment", "Ls")
_setlabel = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetLabel", "s")
_setcondition = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetCondition", "s")
_setfunction = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetFunction", "s")
_setcounter = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetCounter", "L")

_settemperature = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetTemperature", "f")
_setvcc = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetVcc", "f")
_setvccq = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetVccQ", "f")
_setvpp = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetVpp", "f")
_setdatarate = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetDataRate", "L")

_setchannel = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetChannel", "L")
_setce = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetCe", "L")
_setlun = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetLun", "L")
_setblock = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetBlock", "L")
_setpage = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetPage", "L")
_setcevector = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetCeVector", "pL")
_setlunvector = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetLunVector", "pL")
_setblockvector = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetBlockVector", "pL")
_setpagevector = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetPageVector", "pL")
_setaddress = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetAddress", "LLL")
_setwordline = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetWordline", "L")
_setlevel = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_SetLevel", "s")

_adddata = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddData", "ss")

_adddatavector = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddDataVector", "spLL")
_adddatamatrix = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddDataMatrix", "spLLL")

_adddatatag = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddDataTag", "sss")

_adddatavectortag = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddDataVectorTag", "sspLL")
_adddatamatrixtag = CLibManagerSingleton.load_func(_lib_handle, "v", "DataloggerApi_AddDataMatrixTag", "sspLLL")



## <summary>	Opens the datalog file ( file without extension)
# 					A CSV file is generated and copy to Result folder at the end of test.</summary>
#
# <param name="fileNameWithoutExt">	file name without extension.</param>
# <param name="nfs_enable">	true to enable data trasnsfer by NFS.</param>
# @snippet std_examples.py Datalogger Example
def open(fileNameWithoutExt: str, nfs_enable: bool = False):
    _open(fileNameWithoutExt, 1 if nfs_enable else 0)

## <summary>	Closes the Datalog.</summary>
# @snippet std_examples.py Datalogger Example
def close():
    _close()


## <summary>	Flushes the Datalog to disk.</summary>
# @snippet std_examples.py Datalogger Example
def flush():
    _flush()


## <summary>	Queries if this datalog is open.</summary>
#
# <returns>	true if open, false if not.</returns>
# @snippet std_examples.py Datalogger Example
def is_open():
    return bool(_isopen())


## <summary>	Sets the segment information.</summary>
#
# <param name="index">	Zero-based index of the segment.</param>
# <param name="name">	Name of the segment.</param>
# @snippet std_examples.py Datalogger Example
def set_segment(index: int, name: str):
    _setsegment(index, name)


## <summary>	Sets the test label.</summary>
#
# <param name="label">	The text of the test label.</param>
# @snippet std_examples.py Datalogger Example
def set_label(label: str):
    _setlabel(label)


## <summary>	Sets the test condition.</summary>
#
# <param name="condition">	The text of the test condition.</param>
# @snippet std_examples.py Datalogger Example
def set_condition(condition: str):
    _setcondition(condition)


## <summary>	Sets the function name inside a test.</summary>
#
# <param name="function">	The text of the function name.</param>
# @snippet std_examples.py Datalogger Example
def set_function(function: str):
    _setfunction(function)


## <summary>	Sets the counter ( e.g cycling usage ).</summary>
#
# <param name="counter">	Zero-based index of the counter.</param>
# @snippet std_examples.py Datalogger Example
def set_counter(counter: int):
    _setcounter(counter)


## <summary>	Sets the socket temperature.</summary>
#
# <param name="temperature">	The temperature to set.</param>
# @snippet std_examples.py Datalogger Example
def set_temperature(temperature: float):
    _settemperature(temperature)


## <summary>	Sets the Vcc value applied on the tester.</summary>
#
# <param name="vcc">	The Vcc voltage.</param>
# @snippet std_examples.py Datalogger Example
def set_vcc(vcc: float):
    _setvcc(vcc)


## <summary>	Sets the VccQ value applied on the tester.</summary>
#
# <param name="vccq">	The VccQ voltage.</param>
# @snippet std_examples.py Datalogger Example
def set_vccq(vccq: float):
    _setvccq(vccq)


## <summary>	Sets the Vpp value applied on the tester.</summary>
#
# <param name="vpp">	The Vpp voltage.</param>
# @snippet std_examples.py Datalogger Example
def set_vpp(vpp: float):
    _setvpp(vpp)


## <summary>	Sets data rate (MT/s).</summary>
#
# <param name="datarateMTs">	The data rate (MT/s).</param>
# @snippet std_examples.py Datalogger Example
def set_datarate(datarateMTs: int):
    _setdatarate(datarateMTs)


## <summary>	Sets the Tester Unit channel.</summary>
#
# <param name="channel">	Zero-based index of the channel.</param>
# @snippet std_examples.py Datalogger Example
def set_channel(channel: int):
    _setchannel(channel)

## <summary>	Sets the die ce.</summary>
#
# <param name="ce">	Zero-based index of the ce.</param>
# @snippet std_examples.py Datalogger Example
def set_ce(ce: int):
    _setce(ce)

## <summary>	Sets the lun.</summary>
#
# <param name="lun">	Zero-based index of the lun.</param>
# @snippet std_examples.py Datalogger Example
def set_lun(lun: int):
    _setlun(lun)

## <summary>	Sets the block.</summary>
#
# <param name="block">	Zero-based index of the ce.</param>
# @snippet std_examples.py Datalogger Example
def set_block(block: int):
    _setblock(block)

## <summary>	Sets the page.</summary>
#
# <param name="page">	Zero-based index of the page.</param>
# @snippet std_examples.py Datalogger Example
def set_page(page: int):
    _setpage(page)



## <summary>	Sets the list of the ce indexes.</summary>
#
# <param name="array_index">	array with Zero-based index of the ce.</param>
# <param name="itemcount">	the array length.</param>
# @snippet std_examples.py Datalogger Example
def set_ce_vector(array_index: [], itemcount: int):
    if itemcount == 1:
        _setce(array_index[0])
    else:
        baValues = ai2b(array_index)
        _setcevector(baValues[0], itemcount)


## <summary>	Sets the list of the lun indexes.</summary>
#
# <param name="array_index">	array with Zero-based index of the lun.</param>
# <param name="itemcount">	the array length.</param>
# @snippet std_examples.py Datalogger Example
def set_lun_vector(array_index: [], itemcount: int):
    if itemcount == 1:
        _setlun(array_index[0])
    else:
        baValues = ai2b(array_index)
        _setlunvector(baValues[0], itemcount)

## <summary>	Sets the list of the block indexes.</summary>
#
# <param name="array_index">	array with Zero-based index of the block.</param>
# <param name="itemcount">	the array length.</param>
# @snippet std_examples.py Datalogger Example
def set_block_vector(array_index: [], itemcount: int):
    if itemcount == 1:
        _setblock(array_index[0])
    else:
        baValues = ai2b(array_index)
        _setblockvector(baValues[0], itemcount)

## <summary>	Sets the list of the page indexes.</summary>
#
# <param name="array_index">	array with Zero-based index of the page.</param>
# <param name="itemcount">	the array length.</param>
# @snippet std_examples.py Datalogger Example
def set_page_vector(array_index: [], itemcount: int):
    if itemcount == 1:
        _setpage(array_index[0])
    else:
        baValues = ai2b(array_index)
        _setpagevector(baValues[0], itemcount)

## <summary>	Sets the address.</summary>
#
# <param name="lun">	The lun.</param>
# <param name="block">	The block.</param>
# <param name="page">	The page.</param>
# @snippet std_examples.py Datalogger Example
def set_address(lun: int, block: int, page: int):
    _setaddress(lun, block, page)


## <summary>	Sets the wordline in the page - if supported.</summary>
#
# <param name="wordline">	The wordline.</param>
# @snippet std_examples.py Datalogger Example
def set_wordline(wordline: int):
    _setwordline(wordline)

## <summary>	Sets the wordline in the page - if supported.</summary>
#
# <param name="level">	The wordline index.</param>
# @snippet std_examples.py Datalogger Example
def set_level(level: str):
        _setlevel(level)

## <summary>	Adds data as Measurement and value.</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="data">	The text value.</param>
# @snippet std_examples.py Datalogger Example
def add_data(measurement: str, data: str):
        _adddata(measurement, data)

## <summary>	Adds a measurement as a vector of double. Items separated by '#'</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="float_array">	The vector of double values.</param>
# <param name="itemcount">	The vector size.</param>
# <param name="decimals">	The number of decimal digits.</param>
# @snippet std_examples.py Datalogger Example
def add_data_vector(measurement: str, float_array, itemcount: int, decimals: int):
    bafValues = af2b(float_array)
    _adddatavector(measurement, bafValues[0], itemcount, decimals)

## <summary>	Adds a measurement as a matrix of double. Column items separated by '#' and rows separated by '|'.</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="float_array">	The array of double values.</param>
# <param name="rowcount">	The number matrix's row.</param>
# <param name="columncount">	The number matrix's column.</param>
# <param name="decimals">	The number of decimal digits.</param>
# @snippet std_examples.py Datalogger Example
def add_data_matrix(measurement: str, float_array, rowcount: int, columncount: int, decimals: int):
    # convert matrix to array
    farray = []
    for r in range(0, rowcount):
        for c in range(0, columncount):
            farray.append(float(float_array[r][c]))

    bafValues = af2b(farray)
    _adddatamatrix(measurement, bafValues[0], rowcount, columncount, decimals)

## <summary>	Adds data as Measurement, Tag and value. Tag is reset at the end.</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="tag">	The Tag of the measurement.</param>
# <param name="data">	The value as text.</param>
# @snippet std_examples.py Datalogger Example
def add_data_tag(measurement: str, tag: str, data):
    _adddatatag(measurement, tag, data)


## <summary>	Adds a measurement as a vector of double, included Tag. Items separated by '#'</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="tag">			The Tag of the measurement.</param>
# <param name="float_array">	The vector of double values.</param>
# <param name="itemcount">	The vector size.</param>
# <param name="decimals">	The number of decimal digits.</param>
# @snippet std_examples.py Datalogger Example
def add_data_vector_tag(measurement: str, tag: str, float_array, itemcount: int, decimals: int):
    bafValues = af2b(float_array)
    _adddatavectortag(measurement, tag, bafValues[0], itemcount, decimals)


## <summary>	Adds a measurement as a matrix of double, included Tag. Column items separated by '#' and rows separated by '|'.</summary>
#
# <param name="measurement">	Measurement.</param>
# <param name="tag">	The Tag of the measurement.</param>
# <param name="float_array">	The array of double values.</param>
# <param name="rowcount">	The number matrix's row.</param>
# <param name="columncount">	The number matrix's column.</param>
# <param name="decimals">	The number of decimal digits.</param>
# @snippet std_examples.py Datalogger Example
def add_data_matrix_tag(measurement: str, tag: str, float_array, rowcount: int, columncount: int, decimals: int):
    # convert matrix to array
    farray = []
    for r in range(rowcount):
        for c in range(columncount):
            farray.append(float(float_array[r][c]))
    bafValues = af2b(farray)
    _adddatamatrixtag(measurement, tag, bafValues[0], rowcount, columncount, decimals)

