import nanocycler

# from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM
from lib.DataMgr import the_data_mgr as data_mgr



################################
### MICRON SPECIFIC COMMANDS ###
################################

class MICRON_CMD:
    READ_CMD_20h = 0X20

    BLOCK_ERASE_SUSPEND_CMD_61h = 0x61
    BLOCK_ERASE_RESUME_CMD_D2h = 0xD2

    PROGRAM_SUSPEND_CMD_84h = 0x84
    PROGRAM_RESUME_CMD_13h = 0x13


############################################################
### the Device generic class
############################################################

class CMicron(COnfiDevice):
    """This is the base class for a Micron Nand device.
     It contains some default implementation of specific Micron algorithm. Overriding a method it is possible to
     customize the implementation of a function for a specific device.
      """
    def __init__(self):
        COnfiDevice.__init__(self)
        self.CHANNEL_NUM = nanocycler.CHANNEL_NUM
        self.DEVICE_MANUFACTURER = "Micron"
        self.DEVICE_NAME = "xxx"
        self.DEVICE_CE_NUMBER = 1
        self.DEVICE_ID_LEN = 8
        self.PAGE_LENGTH = 18592
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = self.PAGE_LENGTH // self.CHUNK_NUMBER
        self.PLANE_NUMBER = 4  # 4 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.PAGE_NUMBER = 5184  # per block
        self.WL_NUMBER = self.PAGE_NUMBER // self.LEVEL_NUMBER
        self.LUN_NUMBER = 4
        self.BLOCK_NUMBER = 1024
        self.LUN_START_BIT_ADDRESS = 23
        self.VALID_LUN_MASK = 0x03
        self.BLOCK_START_BIT_ADDRESS = 13
        self.VALID_BLOCK_MASK = 0x3FF
        self.VALID_PAGE_MASK = 0x1FFF
        self.RR_OPTIONS = 15

        self.MAX_ERASE_TIME = 15000 # 15 msec
        self.MAX_PROG_TIME = 10000 # 10 msec
        self.MAX_READ_TIME = 1000 # 1 msec

        self.toshiba_like = False
        self.channel = 0


    def build_row_address(self, lun, block, page):
        row_address = (((lun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | (
                (block & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (page & self.VALID_PAGE_MASK))
        return True, row_address



    def read_unique_id(self, ch_list, ce, lun = 0):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        res, row_address = self.build_row_address(lun, 0, 0)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xED)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        # opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 256) # SDR to have lower speed (valid only for HS because data out is performed with custom_sequence_ddr_get_data_byte)
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, 512)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            res, buffer = hw.custom_sequence_get_out_buffer(512)

            for s in range(16):

                res = True

                for i in range(16):
                    check = buffer[s * 32 + i] | buffer[s * 32 + 16 + i]
                    if check != 0xFF:
                        res = False
                        break

                if res:
                    uid = self.format_array(buffer[s * 32: s * 32 + 16])
                    logger.log_device_unique_id([ch], [ce], lun, "UID", uid)
                    self.load_info(ch, ce, lun, uid, self.DEVICE_NAME, self.device_id,
                                          self.manufacturer_code,
                                          self.model)
                    break
                else:
                    ws.error("Ch: {0} - Ce: {1} - Lun: {2} - Set {3} is not valid - {4} / {5}".format(ch, ce, lun, s,
                                                                                           self.format_array(buffer[s * 32: s * 32 + 16]),
                                                                                           self.format_array(buffer[s * 32 + 16: s * 32 + 32])))

    def get_status_enhanced_78h(self, ch_list, ce, lun, block):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        res, row_address = self.build_row_address(lun, block, 0)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_STATUS_ENHANCED_CMD_78h)
        self.micron_set_row_address(opcbuilder,row_address)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_status("SRE78h", ch_list, ce, lun, block)

    def identification(self):

        # clear uid list if already present, but before update central db
        for uid in self.uid_list:
            data_mgr.store_to_central(uid)
        self.uid_list.clear()

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        lun_list = range(0, self.LUN_NUMBER)

        # some initial setting for channels
        self.fpga_setup(0, 0, self.CHUNK_NUMBER)

        for ch in ch_list:
            self.die_configure([ch], ce_list, self.odt, self.driver_strength)

        self.fpga_setup(self.read_latency_cycles, self.write_latency_cycles, self.CHUNK_NUMBER)

        for ch in ch_list:
            for ce in ce_list:
                hw.select_ce(ce)
                # Read Device Id
                self.device_id_read([ch], ce, 0, self.DEVICE_ID_LEN)
                # Read page parameters
                self.page_parameter_read([ch], ce, 0)
                for lun in range(self.LUN_NUMBER):
                    # Read Unique id
                    self.read_unique_id([ch], ce, lun)

        # before exit perform perform ZQ Calib Long
        for lun in lun_list:
            self.zq_calib_long(ch_list, ce_list, lun)


    # def calib_setup(self, data_rate_mhz):
    #     calib_page_length = self.PAGE_LENGTH

    #     opcbuilder = nanocycler.opcodebuilder(0)

    #     opcbuilder.clear()
    #     opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
    #     opcbuilder.set_ce_low()
    #     opcbuilder.set_wp_high()
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
    #     opcbuilder.add(eNandBackdoor.BD_CLE, 0x80)
    #     opcbuilder.set_column_address(0)
    #     self.micron_set_row_address(opcbuilder, 0)
    #     opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
    #     opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
    #     opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
    #     opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, calib_page_length)
    #     opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
    #     opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #     opcbuilder.set_ce_high()
    #     opcbuilder.set_wp_low()
    #     hw.page_buffer_write_builder(opcbuilder)

    #     opcbuilder.clear()
    #     opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
    #     opcbuilder.set_ce_low()
    #     opcbuilder.set_wp_high()
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
    #     opcbuilder.add(eNandBackdoor.BD_CLE, 0x05)
    #     opcbuilder.set_column_address(0)
    #     # self.micron_set_row_address(opcbuilder, 0)
    #     opcbuilder.add(eNandBackdoor.BD_CLE, 0xE0)
    #     opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
    #     opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
    #     opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
    #     opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, calib_page_length)
    #     opcbuilder.set_ce_high()
    #     opcbuilder.set_wp_low()
    #     opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
    #     hw.page_buffer_read_builder(opcbuilder)

    #     opcbuilder.cleanup()

    #     return True

    # NC DEBUG START
    def page_buffer_write(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        for ch in ch_list:
            hw.select_channel(ch)
            hw.set_pattern(nanocycler.enumPatternType.Random, 0, 0) # random pattaern

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x80)
        opcbuilder.set_column_address(0)
        self.micron_set_row_address(opcbuilder, 0)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x99)
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PBW", ch_list, ce_list, lun_list, [block], page)


    def page_buffer_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x05)
        opcbuilder.set_column_address(0)
        # self.micron_set_row_address(opcbuilder, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xE0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.PAGE_LENGTH)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_read("PBR", ch_list, ce, lun, [block], page)

    # NC DEBUG END

    def micron_set_row_address(self, opcbuilder, row_address):
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 0) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 8) & 0xFF)
        opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 16) & 0xFF)
        # opcbuilder.add(eNandBackdoor.BD_ALE, (row_address >> 24) & 0xFF)


    def micron_set_row_address_reg(self, opcbuilder):
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 4)
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 5)
        opcbuilder.add(eNandBackdoor.BD_ALE_REG, 6)
        # opcbuilder.add(eNandBackdoor.BD_ALE_REG, 7)


    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            self.micron_set_row_address(opcbuilder, row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block], en_i=bI3)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            self.micron_set_row_address(opcbuilder, row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        #delay than suspend all luns
        opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

        for lun in lun_list:
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_SUSPEND_CMD_61h)
            self.micron_set_row_address(opcbuilder, row_address)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS-SPD", ch_list, ce_list, lun_list, [block], rbTimeName="tESPD")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)



    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list : []):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) #Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                self.micron_set_row_address(opcbuilder, row_address)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    def multi_plane_erase_block_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0) # Eric: 20231111

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                self.micron_set_row_address(opcbuilder, row_address)

            # in case of multi-lun (interleaving operation), we wait only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        #delay than suspend all luns
        opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_SUSPEND_CMD_61h)
                self.micron_set_row_address(opcbuilder, row_address)

        # get suspend time
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS-SPD", ch_list, ce_list, lun_list, block_list, rbTimeName="tESPD")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block, suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_RESUME_CMD_D2h)

        if not suspend_cmd_delay == -1:
            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                    res, row_address = self.build_row_address(lun, block, 0)

                    opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_SUSPEND_CMD_61h)
                    self.micron_set_row_address(opcbuilder, row_address)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS-RSM", ch_list, ce_list, lun_list, [block], rbTimeName="tBERS" if suspend_cmd_delay == -1 else "tESPD")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_RESUME_CMD_D2h)

        if not suspend_cmd_delay == -1:
            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                for block_idx in range(len(block_list)):
                    block = block_list[block_idx]

                    res, row_address = self.build_row_address(lun, block, 0)

                    opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.BLOCK_ERASE_SUSPEND_CMD_61h)
                    self.micron_set_row_address(opcbuilder, row_address)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS-RSM", ch_list, ce_list, lun_list, block_list, rbTimeName="tBERS" if suspend_cmd_delay == -1 else "tESPD")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


    # READ PAGE (00h-30h) Operation
    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        self.micron_set_row_address(opcbuilder, row_address)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()


    # READ PAGE (00h-30h) Operation
    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        column_address = column
        res, row_address = self.build_row_address(lun, block, page)

        self.recall_pattern(ch_list, ce, lun, block, page)
        # seed_high, seed_low = BaseDevice.recall_pattern(self, 0, lun, block, page)

        self.set_ram_data_2(ch_list, column_address, row_address)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # in order to improve the performance, the sequence is created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            if bI1:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DELAY, 100)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_DELAY, self.tPRE)
            if bI4r:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 4 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 4 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)


    def multi_plane_read_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):
        return self.multi_plane_retry_page_compare(ch_list, ce, lun, block_list, page, column, page_length)
    
    def multi_plane_retry_page_compare(self, ch_list: [], ce, lun, block_list, page, column, page_length):
        # READ PAGE (00h-30h) Operation
     
        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        # in order to improve the performance, the sequence is created  only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce

        # if self.opcbuilder_cache is None:
        self.opcbuilder_cache = nanocycler.opcodebuilder(ce)
        self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        self.opcbuilder_cache.set_ce_low()
        self.opcbuilder_cache.set_wp_high()
        self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1)  # tDQSRH
        
        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)
            ram_dword0 = (column & 0xFFFF ) | (self.rr_option << 16)
            ram_dword1 = row_address
            self.set_ram_data_2(ch_list, ram_dword0, ram_dword1)

            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0)  # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1)  # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 2)  # rr_option is stored as byte21 of the ram
            if block_idx < (len(block_list) - 1):
                self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)

        self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0)  # tDQSRH

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)
        # self.opcbuilder_cache.clear()

        logger.log_read("READ", ch_list, ce, lun, block_list, page, en_fails=False, en_rnb=True, en_i=False)
        self.opcbuilder_cache.set_wp_low()
        self.opcbuilder_cache.set_ce_high()

        for block_idx in range(len(block_list)):
            
            block = block_list[block_idx]
            self.recall_pattern(ch_list, ce, lun, block, page)
            res, row_address = self.build_row_address(lun, block, page)
            ram_dword0 = (column & 0xFFFF ) | (self.rr_option << 16)
            ram_dword1 = row_address
            self.set_ram_data_2(ch_list, ram_dword0, ram_dword1)

            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            # self.opcbuilder_cache.set_column_address(column)
            # self.micron_set_row_address(self.opcbuilder_cache, row_address)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0)  # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1)  # column address byte 1, is stored as byte 1 of the ram
            self.micron_set_row_address_reg(self.opcbuilder_cache)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 2)
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()
            hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)
            # self.opcbuilder_cache.clear()
            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False, en_i= False)
            # self.opcbuilder_cache.cleanup()


    # READ PAGE MULTI-PLANE (00h-32h) Operation
    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column_address, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            res, row_address = self.build_row_address(lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i= bI1)


        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_06h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder)    # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False, en_i= bI4r)

        opcbuilder.cleanup()


    # READ PAGE (00h-20h) Operation
    def page_fast_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        self.recall_pattern(ch_list, ce, lun, block, page)

        # reset data generator only one time
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        res, row_address = self.build_row_address(lun, block, page)
        self.set_ram_data_2(ch_list, 0, row_address)

        if bI1 or bI4r:
            pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 1 start

        for chunk in range(0, self.CHUNK_NUMBER):
            column_address = column + chunk * self.CHUNK_LENGTH

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address_reg(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.READ_CMD_20h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, self.CHUNK_LENGTH)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)

        if bI1 or bI4r:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop

        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_read("FAST-READ", ch_list, ce, lun, [block], page, en_i= bI4r or bI4r, rbTimeName="tRSNAP")



    # PROGRAM PAGE (80h-10h) Operation - Two Pass
    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            # pattern is the same between ce, use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            res, row_address = self.build_row_address(lun, block, page)
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            self.micron_set_row_address(opcbuilder,row_address)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            # be carefull in case of multiple lun only last lun should be acquired
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        if bI2 or bI4w:
            pmu.PMU_START_TRIGGER(opcbuilder)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)

                # pattern is the same between ce  and channel, use the first
                # CBaseDevice.recall_pattern(self, ch_list, ce_list[0], lun, block, page)
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2) # 2 to use register seed

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                self.micron_set_row_address(opcbuilder, row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble

                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                # be carefull in case of multi block multi lun only the last will be acquired
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI2 or bI4w:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("MP-PRG", ch_list, ce_list, lun_list, block_list, page, en_i=bI2 or bI4w)


        # log SR only if log is enabled, page is upper page and we want to log SR
        if page <= 1047:
            if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and (((page-4)%3) == 2):
                for ce in ce_list:
                    for lun in lun_list:
                        for block in block_list:
                            self.get_status_enhanced_78h(ch_list, ce, lun, block)
        elif page >= 1064:
            if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and (((page+1)%3) == 2):
                    for ce in ce_list:
                        for lun in lun_list:
                            for block in block_list:
                                self.get_status_enhanced_78h(ch_list, ce, lun, block)


    # #################################################
    # PROGRAM SUSPEND

    def program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay):

        page_suspended = True # in micron all page can be suspended

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            res, row_address = self.build_row_address(lun, block, page)
            column_address = 0

            # pattern is the same between ce use the first
            self.recall_pattern(ch_list, ce_list[0], lun, block, page)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            time_measure = "tPSPD"
            # delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)
            opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_SUSPEND_CMD_84h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_program("PRG-SPD", ch_list, ce_list, [lun], [block], page, rbTimeName = time_measure)

        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)

        return page_suspended


    def program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block, page, suspend_cmd_delay = -1):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        column_address = 0

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            # resume is for lun, use block 0 to calc the address
            res, row_address = self.build_row_address(lun, block, 0)

            opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_RESUME_CMD_13h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)

        if not suspend_cmd_delay == -1:

            # delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:
                # suspend is for lun use block 0 to calc the address
                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_SUSPEND_CMD_84h)
                opcbuilder.set_column_address(column_address)
                self.micron_set_row_address(opcbuilder, row_address)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG-RSM", ch_list, ce_list, lun_list, [block], page, rbTimeName = "tPROG" if suspend_cmd_delay == -1 else "tPSPD")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_program_page_suspend(self, ch_list: [], ce_list: [], lun_list: [], block_list : [], page, suspend_cmd_delay):
        page_suspended = True # in micron all page can be suspended

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                res, row_address = self.build_row_address(lun, block, page)
                column_address = 0

                # pattern is the same between ce use the first
                self.recall_pattern(ch_list, ce_list[0], lun, block, page)

                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column_address)
                self.micron_set_row_address(opcbuilder, row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # tcals
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpsth
                if block_idx < (len(block_list) - 1):
                    time_measure = "tBSY"
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    time_measure = "tPSPD"
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    # delay than suspend all luns
                    opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)
                    opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_SUSPEND_CMD_84h)
                    opcbuilder.set_column_address(column_address)
                    self.micron_set_row_address(opcbuilder, row_address)
                opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()

                logger.log_program("MP-PRG-SPD", ch_list, ce_list, [lun], [block], page, rbTimeName=time_measure)

        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)


        return page_suspended



    def multi_plane_program_page_resume(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, suspend_cmd_delay=-1):

        column_address  = 0

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)
        
        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            # resume is for lun, use block 0 to calc the address
            res, row_address = self.build_row_address(lun, 0, 0)

            opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_RESUME_CMD_13h)
            opcbuilder.set_column_address(column_address)
            self.micron_set_row_address(opcbuilder, row_address)

        if not suspend_cmd_delay == -1:

            #delay than suspend all luns
            opcbuilder.add(eNandBackdoor.BD_DELAY, suspend_cmd_delay)

            for lun in lun_list:

                # suspend is for lun use block 0 to calc the address
                res, row_address = self.build_row_address(lun, 0, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, MICRON_CMD.PROGRAM_SUSPEND_CMD_84h)
                opcbuilder.set_column_address(column_address)
                self.micron_set_row_address(opcbuilder, row_address)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("MP-PRG-RSM", ch_list, ce_list, lun_list, block_list, page,
                           rbTimeName="tPROG" if suspend_cmd_delay == -1 else "tPGMSL")

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)




    # #################################################
    # READ RETRY

    def set_read_retry_option(self, ch_list: [], ce, lun, retry_option):
        if self.option_buffer:
            if retry_option >= len(self.option_buffer) == 0:
                return False, ""
            option = int(self.option_buffer[retry_option][0])
            # csv file contains one line for each RR options
            self.set_feature_by_lun(ch_list, [ce], lun, 0x89, option, 0, 0, 0)
            return True, "{0}".format(hex(option))
        else:
            if retry_option >= self.RR_OPTIONS:
                return False, ""
            self.set_feature_by_lun(ch_list, [ce], lun, 0x89, retry_option, 0, 0, 0)
            return True, "{0}".format(hex(retry_option))


    def reset_read_retry_option(self, ch_list: [], ce, lun):
        self.set_feature_by_lun(ch_list, [ce], lun, 0x89, 0, 0, 0, 0)
        return True



    # Table 42: Feature Address E7h: Temperature Sensor Readout
    def read_internal_temperature(self, ch_list: [], ce):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xEE)
        opcbuilder.add(eNandBackdoor.BD_ALE, 0xE7)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            temp = hw.custom_sequence_sdr_get_data_byte(0) - 37
            logger.log_die_temperature("READ_INT_TEMP", [ch], [ce], "{:.1f}".format(temp))
            self.temperature_4_channel[ch] = temp



    # Table 63: Error Management Details
    def is_bad_block(self, ch_list: [], ce, lun, block):

        self.page_read(ch_list, ce, lun, block, 0, 0, self.PAGE_LENGTH)

        for ch in ch_list:
            hw.select_channel(ch)
            buffer = self.get_read_buffer(ch, self.PAGE_LENGTH)
            is_bad = (buffer[16384] == 00)
            logger.log_bad_block(ch, ce, lun, block, is_bad)


    def offset_to_voltage(self, offset):
        offset_voltage = -960 + offset * 7.5

        return offset_voltage


    def set_read_offset_code(self, ch_list: [], ce, lun, level, offset):

        if level > 6:
            return False, 0.0

        offset_code = self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        if level == 0:
            self.set_feature(ch_list, [ce], 0xA5, offset_code, 0x00, 0x00, 0x00)  # TLC extra
            self.set_feature(ch_list, [ce], 0xA0, offset_code, 0x00, 0x00, 0x00)  # MLC upper
            self.set_feature(ch_list, [ce], 0xA4, offset_code, 0x00, 0x00, 0x00)  # SLC
        if level == 1:
            self.set_feature(ch_list, [ce], 0xA6, offset_code, 0x00, 0x00, 0x00)  # TLC upper
            self.set_feature(ch_list, [ce], 0xA1, offset_code, 0x00, 0x00, 0x00)  # MLC lower
        if level == 2:
            self.set_feature(ch_list, [ce], 0xA7, offset_code, 0x00, 0x00, 0x00)  # TLC extra
            self.set_feature(ch_list, [ce], 0xA2, offset_code, 0x00, 0x00, 0x00)  # MLC upper
        if level == 3:
            self.set_feature(ch_list, [ce], 0xA8, offset_code, 0x00, 0x00, 0x00)  # TLC lower
        if level == 4:
            self.set_feature(ch_list, [ce], 0xA9, offset_code, 0x00, 0x00, 0x00)  # TLC extra
        if level == 5:
            self.set_feature(ch_list, [ce], 0xAA, offset_code, 0x00, 0x00, 0x00)  # TLC upper
        if level == 6:
            self.set_feature(ch_list, [ce], 0xAB, offset_code, 0x00, 0x00, 0x00)  # TLC extra

        return True, offset_voltage


    def reset_read_offset_code(self, ch_list: [], ce, lun, level):

        # reset all features
        self.set_feature(ch_list, [ce], 0x89, 0x00, 0x00, 0x00, 0x00)

        self.set_feature(ch_list, [ce], 0xA5, 0x00, 0x00, 0x00, 0x00)  # TLC extra
        self.set_feature(ch_list, [ce], 0xA6, 0x00, 0x00, 0x00, 0x00)  # TLC upper
        self.set_feature(ch_list, [ce], 0xA7, 0x00, 0x00, 0x00, 0x00)  # TLC extra
        self.set_feature(ch_list, [ce], 0xA8, 0x00, 0x00, 0x00, 0x00)  # TLC lower
        self.set_feature(ch_list, [ce], 0xA9, 0x00, 0x00, 0x00, 0x00)  # TLC extra
        self.set_feature(ch_list, [ce], 0xAA, 0x00, 0x00, 0x00, 0x00)  # TLC upper
        self.set_feature(ch_list, [ce], 0xAB, 0x00, 0x00, 0x00, 0x00)  # TLC extra

        self.set_feature(ch_list, [ce], 0xA4, 0x00, 0x00, 0x00, 0x00)  # SLC

        self.set_feature(ch_list, [ce], 0xA0, 0x00, 0x00, 0x00, 0x00)  # MLC upper
        self.set_feature(ch_list, [ce], 0xA1, 0x00, 0x00, 0x00, 0x00)  # MLC lower
        self.set_feature(ch_list, [ce], 0xA2, 0x00, 0x00, 0x00, 0x00)  # MLC upper

