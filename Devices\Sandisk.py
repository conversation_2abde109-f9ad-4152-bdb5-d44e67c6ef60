import nanocycler

from nanocycler import NanoTimer as time
from nanocycler import ws as ws
from nanocycler import hardware as hw
from nanocycler import pmu as pmu
# from nanocycler import datalog as datalog
# from nanocycler import utility as utility
from nanocycler import enumNandBackdoor as eNandBackdoor
# from nanocycler import enumPatternType as ePatternType
# from nanocycler import enumSequenceSignal as eSequenceSignal

from Devices.OnfiDevice import COnfiDevice as COnfiDevice
from Devices.OnfiDevice import ONFI_CMD as ONFI_CMD
# from Devices.OnfiDevice import PMU_ALGO as PMU_ALGO
from lib.ResultLogger import the_result_logger as logger
from lib.ResultLogger import LOG_SET_ITEM as LOG_SET_ITEM
from lib.DataMgr import the_data_mgr as data_mgr


###########################################################################
### Reference Datasheet:
###########################################################################

class CSandisk(COnfiDevice):
    def __init__(self):
        COnfiDevice.__init__(self)
        self.DEVICE_MANUFACTURER = "Sandisk"
        self.DEVICE_NAME = "xxx"

        self.DEVICE_CE_NUMBER = 2
        self.DEVICE_ID_LEN = 4

        self.PAGE_LENGTH = 18336
        self.CHUNK_NUMBER = 4
        self.CHUNK_LENGTH = self.PAGE_LENGTH // self.CHUNK_NUMBER

        self.PLANE_NUMBER = 2  # 2 plane inside the lun
        self.LEVEL_NUMBER = 3  # TLC 3 levels
        self.WL_NUMBER = 384
        self.LUN_NUMBER = 8
        self.BLOCK_NUMBER = 1024 # it is just a number it depends on device size
        self.PAGE_NUMBER = 1152  # per block

        self.LUN_START_BIT_ADDRESS = 21
        self.BLOCK_START_BIT_ADDRESS = 9
        self.VALID_LUN_MASK = 0x07
        self.VALID_WL_MASK = 0x1FF
        self.VALID_BLOCK_MASK = 0x7FF
        self.ROW_BYTE_ADDRESS = 4

        self.aLevels = ["0", "1", "2"]
        self.VT_SCAN = [0, 2, 4, 6]


    def address_info(self, lun, block, page):
        lWL = page // self.LEVEL_NUMBER
        acLevel = self.aLevels[page % self.LEVEL_NUMBER]
        return True, lWL, acLevel

    def build_row_address(self, lun, block, word_line):
        lRowAddress = (((lun & self.VALID_LUN_MASK) << self.LUN_START_BIT_ADDRESS) | (
                (block & self.VALID_BLOCK_MASK) << self.BLOCK_START_BIT_ADDRESS) | (word_line % self.WL_NUMBER))
        return True, lRowAddress
    

    def identification(self):

        # clear uid list if already present, but before update central db
        for uid in self.uid_list:
            data_mgr.store_to_central(uid)
        self.uid_list.clear()

        # wait a while after turn on
        time.msec_sleep(1000)

        # check the voltage
        ws.info("Vcc: {0:.3f} - Vccq: {1:.3f}".format(hw.get_vcc(), hw.get_vccq()))

        board_status = hw.get_board_status()
        if (board_status >> 16) & 0x3 != 0x3:
            ws.error("Ready Busy Problem on Channel 0")
        if (board_status >> 18) & 0x3 != 0x3:
            ws.error("Ready Busy Problem on Channel 1")

        ch_list = range(0, self.CHANNEL_NUM)
        ce_list = range(0, self.DEVICE_CE_NUMBER)
        lun_list = range(0, self.LUN_NUMBER)

        # some initial setting for channels
        self.fpga_setup(0, 0, self.CHUNK_NUMBER)

        # Don't know why this doesn't work
        #self.die_configure(ch_list, ce_list, self.odt, self.driver_strength)

        for ch in ch_list:
            self.die_configure([ch], ce_list, self.odt, self.driver_strength)

        self.fpga_setup(self.read_latency_cycles, self.write_latency_cycles, self.CHUNK_NUMBER)

        for ce in ce_list:
            hw.select_ce(ce)
            # Read Device Id
            self.device_id_read(ch_list, ce, 0, self.DEVICE_ID_LEN)
            # # Read page parameters
            # CBaseDevice.page_parameter_read(self, ch_list, ce, 0)
            # TODO Read Unique id not implemented yet
            self.read_unique_id(ch_list, ce)

        # before exit perform perform ZQ Calib Long
        for lun in lun_list:
            self.zq_calib_long(ch_list, ce_list, lun)



    # Figure 33: SDR Set Features Command Sequence
    def die_configure(self, ch_list: [], ce_list: [], odt = 0, driver_strength = 0):

        self.device_reset(ch_list, ce_list)

        p1 = p2 = p3 = p4 = 0

        # Driver strength 10h
        # 00h~01h Reserved
        # 02h Ron = Driver Multiplier: Underdrive
        # 03h Reserved
        # 04h Ron = 35 Driver Multiplier: 1(default)
        # 05h Reserved
        # 06h Ron = Driver Multiplier: Overdrive 1
        # 07h~FFh Reserved
        p1 = driver_strength
        lAddress = 0x10
        self.set_feature_async(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # DDR interface 02h
        # bit 7 - 4 ODT: 0 disabled, 1->150 ohm, 2->100 ohm, 3->75 ohm, 4->50 ohm
        # bit 2 RE_c
        # bit 1 DQS_c
        # bit 0 VREF
        p_rl = self.latency_cycle_decode(self.read_latency_cycles)
        p_wl = self.latency_cycle_decode(self.write_latency_cycles)
        odt_bit = odt & 0xF
        re_c_bit = 1
        dqs_c_bit = 1
        vref_bit = 1
        p1 = (odt_bit << 4) | (re_c_bit << 2) | (dqs_c_bit << 1) | (vref_bit << 0)
        p2 = (p_wl << 4) | (p_rl << 0)
        lAddress = 0x02
        self.set_feature_async(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        # Vendor - specific setting used to turn Toggle Mode on or off. 80h
        # bit 0: 0 Enable Toggle Mode
        # bit 0: 1 Disable Toggle Mode
        p1 = 0
        lAddress = 0x80
        self.set_feature_async(ch_list, ce_list, lAddress, p1, p2, p3, p4)

        return

    def page_convert(self, level, page):
        if level == 0:                                              # A status
            current_read_page = page                                # read LSB page
        elif level == 1:                                            # B status
            current_read_page = page + 1                            # read CSB page
        elif level == 2:                                            # C status
            current_read_page = page + 2                            # to read MSB page
        elif level == 3:                                            # D status
            current_read_page = page + 1                            # to read CSB page
        elif level == 4:                                            # E status
            current_read_page = page                                # to read LSB page
        elif level == 5:                                            # F status
            current_read_page = page + 1                            # to read CSB page
        elif level == 6:                                            # G status
            current_read_page = page + 2                            # to read MSB page
        return current_read_page

    def program_page(self, ch_list: [], ce_list: [], lun_list: [], block, page, column):

        if self.slc_mode and ((page % self.LEVEL_NUMBER) != 0):
            # in slc test only first page
            return

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI2:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        # pattern is the same between ce, and lun: use the first
        self.recall_pattern(ch_list, ce_list[0], 0, block, page)

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            res, row_address = self.build_row_address(lun, block, wl)

            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            if self.slc_mode:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SLC_CMD_A2h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
            opcbuilder.set_column_address(column)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
            opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
            if bI4w:
                pmu.PMU_START_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
            if bI4w:
                pmu.PMU_STOP_TRIGGER(opcbuilder)
            opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

            if page_cmd < self.LEVEL_NUMBER:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
            opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI2:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        if (page%3) == 2:
            logger.log_program("PRG", ch_list, ce_list, lun_list, [block], page, en_i = bI2 or bI4w)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page%3) == 2):
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)




    # Figure 5‐33. Multi Plane Page Program Operation Sequence (TLC)
    def multi_plane_program_page(self, ch_list: [], ce_list: [], lun_list: [], block_list: [], page, column):

        if self.slc_mode and ((page % self.LEVEL_NUMBER) != 0):
            # in slc test only first page
            return

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
        opcbuilder.add(eNandBackdoor.BD_DELAY, 100)  # twpre

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        bI2 = self.pmu_algo_is_I2()
        bI4w = self.pmu_algo_is_I4w()

        for lun_idx in range(len(lun_list)):
            lun = lun_list[lun_idx]

            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                # record I2 and I4w only for upper page (2) of the last block
                bI2 = self.pmu_algo_is_I2()
                bI4w = self.pmu_algo_is_I4w()
                if (page % 3) != 2 or block_idx != (len(block_list) - 1):
                    bI2 = False
                    bI4w = False

                res, row_address = self.build_row_address(lun, block, wl)

                # pattern is the same between ce use the first
                seed_high, seed_low = self.recall_pattern(ch_list, ce_list[0], lun, block, page)
                # ws.info('prog recall pattern -> block: {0}, page: {1}, seed_high: {2}, seed_low: {3}'.format(block, page, seed_high, seed_low))
                opcbuilder.add(eNandBackdoor.BD_SEED_LL, seed_low[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_LH, (seed_low[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HL, seed_high[0] & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_SEED_HH, (seed_high[0] >> 16) & 0xFFFF)
                opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 2)  # 2 to use register seed

                if self.slc_mode:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SLC_CMD_A2h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
                opcbuilder.set_column_address(column)
                opcbuilder.set_row_address(row_address)
                # self.apply_col_address(opcbuilder, column)
                # self.apply_row_address(opcbuilder, row_address)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # tcals
                # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 0)  # preamble
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 20)  # twpre
                if bI4w:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DDR_DATA_IN, self.PAGE_LENGTH)
                if bI4w:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)
                opcbuilder.add(eNandBackdoor.BD_DELAY, 30)  # twpsth

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_11h)
                else:
                    if self.slc_mode:
                        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
                    else:
                        if page_cmd < self.LEVEL_NUMBER:
                            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_1Ah)
                        else:
                            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)

                if bI2:
                    pmu.PMU_START_TRIGGER(opcbuilder)
                # opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
                # opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
                if bI2:
                    pmu.PMU_STOP_TRIGGER(opcbuilder)

            if (page % 3) == 2:
                hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
                opcbuilder.clear()
                logger.log_program("MP-PRG", ch_list, ce_list, [lun], block_list, page, rbTimeName="tPROG",
                                   en_i=bI2 or bI4w)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        opcbuilder.add(eNandBackdoor.BD_DQS_T_STATE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        # log SR only if log is enabled, page is upper page and we want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled() and ((page % 3) == 2):
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)



    # Figure 39: Single-Plane Page-by-Page TLC Read
    def page_read(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = column
        bRes, lRowAddress = self.build_row_address(lun, block, word_line)

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()



    # Figure 39: Single-Plane Page-by-Page TLC Read
    def page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        if self.slc_mode and ((page % self.LEVEL_NUMBER) != 0):
            # in slc test only first page
            return

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        wl = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        column_address = column
        bRes, row_address = self.build_row_address(lun, block, wl)

        self.recall_pattern(ch_list, ce, lun, block, page)

        if self.slc_mode:
            self.set_ram_data_3(ch_list, column_address, row_address, ONFI_CMD.SLC_CMD_A2h)
        else:
            self.set_ram_data_3(ch_list, column_address, row_address, page_cmd)

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        # in order to improve the performance, the sequence is created only one time and addresses and page command are applied as ram_data
        # the sequence will be executed by run_parallel in order to run time change the ce
        if self.opcbuilder_cache is None:
            self.opcbuilder_cache = nanocycler.opcodebuilder(0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            self.opcbuilder_cache.set_ce_low()
            self.opcbuilder_cache.set_wp_high()
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 1)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
            if bI1:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 1 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE_REG, 8) #  page command or slc command is stored as byte 8 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_R_NB, 0)
            if bI1:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 1 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 0) # column address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 1) # column address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 4) # row address byte 0, is stored as byte 0 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 5) # row address byte 1, is stored as byte 1 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_ALE_REG, 6) # row address byte 2, is stored as byte 2 of the ram
            self.opcbuilder_cache.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 0)
            self.opcbuilder_cache.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(self.opcbuilder_cache)  # ICC 4 start
            self.opcbuilder_cache.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(self.opcbuilder_cache)  # ICC 4 stop
            self.opcbuilder_cache.add(eNandBackdoor.BD_RE_T_STATE, 1)
            self.opcbuilder_cache.set_wp_low()
            self.opcbuilder_cache.set_ce_high()

        hw.custom_sequence_run_ext(self.opcbuilder_cache, ch_mask, ce_mask)

        logger.log_read("READ", ch_list, ce, lun, [block], page, en_i=bI1 or bI4r)



    # Figure 40: Multi-Plane TLC Read Example, Primary
    def multi_plane_page_compare(self, ch_list: [], ce, lun, block_list: [], page, lColumn, page_length):

        if self.slc_mode and ((page % self.LEVEL_NUMBER) != 0):
            # in slc test only first page
            return

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = lColumn

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder) # ICC 1 start

        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5D)  #prefix cmd for dynamic read

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            if self.slc_mode:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SLC_CMD_A2h)
                opcbuilder.add(eNandBackdoor.BD_CLE, 0x60)
                opcbuilder.set_row_address(lRowAddress)

                if block_idx == (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                    opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.set_row_address(lRowAddress)

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i = bI1)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            seed_high, seed_low = self.recall_pattern(ch_list, ce, lun, block, page)
            # ws.info('read recall pattern -> block: {0}, page: {1}, seed_high: {2}, seed_low: {3}'.format(block, page, seed_high, seed_low))

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder) # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False, en_i = bI4r)

        opcbuilder.cleanup()

    def multi_plane_page_read_pattern(self, ch_list: [], ce, lun, block_list: [], page, lColumn, page_length):

        if self.slc_mode and ((page % self.LEVEL_NUMBER) != 0):
            # in slc test only first page
            return

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = lColumn

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()

        bI1 = self.pmu_algo_is_I1()
        bI4r = self.pmu_algo_is_I4r()

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        if bI1:
            pmu.PMU_START_TRIGGER(opcbuilder) # ICC 1 start

        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5D)  #prefix cmd for dynamic read

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            if self.slc_mode:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.SLC_CMD_A2h)
                opcbuilder.add(eNandBackdoor.BD_CLE, 0x60)
                opcbuilder.set_row_address(lRowAddress)

                if block_idx == (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                    opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
                opcbuilder.set_column_address(column_address)
                opcbuilder.set_row_address(lRowAddress)

                if block_idx < (len(block_list) - 1):
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
                else:
                    opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
                opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        if bI1:
            pmu.PMU_STOP_TRIGGER(opcbuilder)  # ICC 1 stop
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, rbTimeName = "tR", en_fails = False, en_rnb = True, en_i = bI1)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        read_pattern_dict = {}
        for ch in ch_list:
            read_pattern_dict[ch] = {}

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            # seed_high, seed_low = self.recall_pattern(ch_list, ce, lun, block, page)
            # ws.info('read recall pattern -> block: {0}, page: {1}, seed_high: {2}, seed_low: {3}'.format(block, page, seed_high, seed_low))

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            if bI4r:
                pmu.PMU_START_TRIGGER(opcbuilder)  # ICC 4 start
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            if bI4r:
                pmu.PMU_STOP_TRIGGER(opcbuilder) # ICC 4 stop
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            # hang, comment for preventing get fail data and log
            # logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False, en_i = bI4r)

            # get read pattern for each channel
            for ch in ch_list:
                buffer = self.get_read_buffer(ch, page_length)
                read_pattern_dict[ch][block] = buffer
                
        opcbuilder.cleanup()

        return read_pattern_dict


    # info not available in datasheet
    def read_internal_temperature(self, ch_list: [], ce):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xB9)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x7C)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_SDR_DATA_OUT, 4)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        for ch in ch_list:
            self.select_channel(ch)

            temp = hw.custom_sequence_sdr_get_data_byte(0) - 42
            logger.log_die_temperature("READ_INT_TEMP", [ch], [ce], "{:.1f}".format(temp))
            self.temperature_4_channel[ch] = temp

    # Figure 47: Single-Plane SLC Program without Data Input, to check Bad block (Figure 8: Bad-Block Test Flow)
    def is_bad_block(self, ch_list: [], ce, lun, block):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        bRes, lRowAddress = self.build_row_address(lun, block, 0)
        column_address = 0

        opcbuilder = nanocycler.opcodebuilder(0)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_CLE, 0xA2)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_80h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        # self.apply_col_address(opcbuilder, column_address)
        # self.apply_row_address(opcbuilder, lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_DELAY, 500)  # tADL
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.PROGRAM_CMD_10h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        self.get_status_enhanced_72h(ch_list, ce, lun)
            
        for ch in ch_list:
            self.select_channel(ch)
            sr = hw.custom_sequence_ddr_get_data_byte(0)
            is_bad = (sr & 0x1) != 0
            logger.log_bad_block(ch, ce, lun, block, is_bad)



    def erase_block(self, ch_list: [], ce_list: [], lun_list: [], block: int):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            self.recall_pattern(ch_list, ce_list[0], lun, block, 0) # Eric: 20231111
            res, row_address = self.build_row_address(lun, block, 0)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
            opcbuilder.set_row_address(row_address)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("ERS", ch_list, ce_list, lun_list, [block])

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    self.get_status_enhanced_78h(ch_list, ce, lun, block)


    def multi_plane_erase_block(self, ch_list: [], ce_list: [], lun_list: [], block_list: []):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask(ce_list)

        bI3 = self.pmu_algo_is_I3()

        opcbuilder = nanocycler.opcodebuilder(0)
        if bI3:
            pmu.PMU_START_TRIGGER(opcbuilder)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)

        for lun in lun_list:
            for block_idx in range(len(block_list)):
                block = block_list[block_idx]

                self.recall_pattern(ch_list, ce_list[0], lun, block, 0)
                res, row_address = self.build_row_address(lun, block, 0)

                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_60h)
                # opcbuilder.set_row_address(row_address) #Eric: 20231111
                self.apply_row_address(opcbuilder, row_address)

            # in case of multi-lun (interleaving operation), cmd D0h is applied for each lun
            # but the wait is only on last lun
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.BLOCK_ERASE_CMD_D0h)

        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.set_ce_high()
        opcbuilder.set_wp_low()
        if bI3:
            pmu.PMU_STOP_TRIGGER(opcbuilder)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_erase("MP-ERS", ch_list, ce_list, lun_list, block_list)

        # skip if log id not enabled or we do not want to log SR
        if logger.is_in_log_set(LOG_SET_ITEM.SR) and logger.is_enabled():
            for ce in ce_list:
                for lun in lun_list:
                    for block in block_list:
                        self.get_status_enhanced_78h(ch_list, ce, lun, block)

    def read_retry_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):
        return self.vt_page_compare(ch_list, ce, lun, block, page, column, page_length)

    def vt_page_compare(self, ch_list: [], ce, lun, block, page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        self.recall_pattern(ch_list, ce, lun, block, page)

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1

        column_address = column
        bRes, lRowAddress = self.build_row_address(lun, block, word_line)

        opcbuilder = nanocycler.opcodebuilder(ce)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5D)
        opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
        opcbuilder.add(eNandBackdoor.BD_R_NB, 0)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
        opcbuilder.set_column_address(column_address)
        opcbuilder.set_row_address(lRowAddress)
        opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
        opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
        opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.cleanup()

        logger.log_read("READ", ch_list, ce, lun, [block], page)

    def multi_plane_read_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):
        return self.multi_plane_retry_page_compare(ch_list, ce, lun, block_list, page, column, page_length)

    def multi_plane_retry_page_compare(self, ch_list: [], ce, lun, block_list: [], page, column, page_length):

        ch_mask = self.index_list_to_mask(ch_list)
        ce_mask = self.index_list_to_mask([ce])

        word_line = page // self.LEVEL_NUMBER
        page_cmd = page % self.LEVEL_NUMBER + 1
        column_address = column

        opcbuilder = nanocycler.opcodebuilder(ce)
        opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
        opcbuilder.set_ce_low()
        opcbuilder.set_wp_high()
        
        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1) # tDQSRH

        opcbuilder.add(eNandBackdoor.BD_CLE, 0x5D)
        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            bRes, lRowAddress = self.build_row_address(lun, block, word_line)
            opcbuilder.add(eNandBackdoor.BD_CLE, page_cmd)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_00h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.set_row_address(lRowAddress)
            if block_idx < (len(block_list) - 1):
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_32h)
            else:
                opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_30h)
            opcbuilder.add(eNandBackdoor.BD_R_NB, 0)

        opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
        opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0)
        hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
        opcbuilder.clear()

        logger.log_read("MP-READ", ch_list, ce, lun, block_list, page, en_fails = False)

        opcbuilder.set_wp_low()
        opcbuilder.set_ce_high()

        for block_idx in range(len(block_list)):
            block = block_list[block_idx]

            self.recall_pattern(ch_list, ce, lun, block, page)
            bRes, lRowAddress = self.build_row_address(lun, block, word_line)

            opcbuilder.set_ce_low()
            opcbuilder.set_wp_high()
            opcbuilder.add(eNandBackdoor.BD_RESET_DATA_GEN, 1)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 1) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 1)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_05h)
            opcbuilder.set_column_address(column_address)
            opcbuilder.set_row_address(lRowAddress)
            opcbuilder.add(eNandBackdoor.BD_CLE, ONFI_CMD.READ_CMD_E0h)
            opcbuilder.add(eNandBackdoor.BD_DQ_DRIVE, 0)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 0)
            opcbuilder.add(eNandBackdoor.BD_DQS_DRIVE, 0) # tDQSRH
            opcbuilder.add(eNandBackdoor.BD_DDR_DATA_OUT, page_length)
            opcbuilder.add(eNandBackdoor.BD_RE_T_STATE, 1)
            opcbuilder.set_wp_low()
            opcbuilder.set_ce_high()
            hw.custom_sequence_run_ext(opcbuilder, ch_mask, ce_mask)
            opcbuilder.clear()

            logger.log_read("MP-READ", ch_list, ce, lun, [block], page, en_fails = True, en_rnb = False)

        opcbuilder.cleanup()

    # 0x80<=shift_value<=0xFF(即：Dec=[128, 255], shift voltage=[-1.6V, -0.0125V]); step=12.5mV
    # 0x00<=shift_value<=0x7F(即：Dec=[0, 127], shift voltage=[0V, 1.5875V])
    def vt_init(self, ch_list, ce, lun, level):                     
        start_dac, end_dac, voltage_offset = 0, 256, 0
        if level == 0:                                                # A status -> scan range [0x80, 0x40]
            start_scan = 128                                          # start_point: 0x80: dac=128
            end_scan = int(hex(0x40), 16) + 1                         # end_point: 0x40: dac=64 -> scan range: positive shift to +800mV from 0 of the A status' default level
        
        elif level == 2:                                              # C status -> scan range [0xc5, 0x40]
            start_scan = int(hex(0xc5), 16)                           # start_point: 0xc5(dac=197) -> scan range: negative shift to -737.5mV [(0xff-0xc5)*12.5mV] from 0 of the C status' default level
            end_scan = int(hex(0x40), 16) + 1                         # end_point: 0x40(dac=64)  -> scan range: positive shift to +800mV from 0 of the C status' default level
            voltage_offset = (737.5)+(800)                            # C start voltage point following A scan end point of 800mV (A voltage widow width from 0mV to 0x40), (737.5+800)mV is overlapped point btw A and C
        
        elif level == 4:                                              # E status -> scan range [0xcd, 0x40]
            start_scan = int(hex(0xcd), 16)                           # start_point: 0xcd(dac=205) -> scan range: negative shift to -637.5mV from this status' default 0
            end_scan = int(hex(0x40), 16) + 1                         # end_point: 0x40(dac=64)  -> scan range: positive shift to +800mV from this status' default 0 
            voltage_offset = (637.5)+(737.5+800)+(800)                # E start voltage point following C scan end point: 800mV is A positive end point; (737.5+800)mV is C Vt width
            
        elif level == 6:                                              # G status -> scan range [0xbf, 0xff]
            start_scan = int(hex(0xbf), 16)                           # start_point: 0xbf(dac=191) -> scan range: negative shift to -812.5mV from this status' default 0
            end_scan = 256                                            # end_point
            voltage_offset = (812.5)+(637.5+800)+(737.5+800)+(800)    # G start voltage point following E scan end point: (800)mV is A end point, (737.5+800)mV is C Vt width, (637.5+800)mV is E Vt width
            
        if 128 <= start_scan <= 255: 
            start_dac = start_scan - 128 # 0X80 TO 0XFF
        elif 0 <= start_dac <= 127:
            start_dac = start_scan + 128 # 0X00 TO 0X7F
        
        if 128 <= end_scan <= 255: 
            end_dac = end_scan - 128 # 0X80 TO 0XFF
        elif 0 <= end_scan <= 127:
            end_dac = end_scan + 128 # 0X00 TO 0X7F
        
        return start_dac, end_dac, voltage_offset

    def shift_to_code(self, offset):
        code = offset & 0xFF
        return code
    
    def page_covert(level, page):
        if level == 0: # A status
            current_read_page = page
        elif level == 1: # B status
            current_read_page = page + 1
        elif level == 2: # C status
            current_read_page = page + 2
        elif level == 3: # D status
            current_read_page = page + 1
        elif level == 4: # E status
            current_read_page = page
        elif level == 5: # F status
            current_read_page = page + 1
        elif level == 6: # G status
            current_read_page = page + 2
        return current_read_page
    
    def shift_to_voltage(self, offset):
        offset_voltage = -1600 + offset * 12.5

        return offset_voltage
    
    def set_vt_code(self, ch_list, ce, lun, level, vt_init):

        if level > 6:
            return False, 0.0

        # default implementation offset from -128 to 127
        # in order to have linear scan from min offset to max offset
        if vt_init <= 127: 
            vt = vt_init + 128 # 0X80 TO 0XFF
        else:
            vt = vt_init - 128 # 0X00 TO 0X7F

        shift_code = self.shift_to_code(vt)
        shift_voltage = self.shift_to_voltage(vt_init)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0x12, shift_code, 0x00, 0x00, 0x00) # page LSB
        if level == 1:
            self.set_feature(ch_list, [ce], 0x13, shift_code, 0x00, 0x00, 0x00) # page CSB
        if level == 2:
            self.set_feature(ch_list, [ce], 0x12, 0x00, shift_code, 0x00, 0x00) # page MSB
        if level == 3:
            self.set_feature(ch_list, [ce], 0x13, 0x00, shift_code, 0x00, 0x00) # page CSB
        if level == 4:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, shift_code, 0x00) # page LSB
        if level == 5:
            self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, shift_code, 0x00) # page CSB
        if level == 6:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, shift_code) # page MSB

        # SLC
        self.set_feature(ch_list, [ce], 0x14, shift_code, 0x00, 0x00, 0x00)

        return True, shift_voltage
    
    def reset_vt_code(self, ch_list, ce, lun):
        self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x14, 0x00, 0x00, 0x00, 0x00)
        return True
    
    def offset_to_voltage(self, offset):
        offset_voltage = -1600 + offset * 12.5

        return offset_voltage

    def set_read_offset_code(self, ch_list, ce, lun, level, offset):

        if level > 6:
            return False, 0.0

        offset_code = self.offset_to_code(offset)
        offset_voltage = self.offset_to_voltage(offset)

        # TLC
        if level == 0:
            self.set_feature(ch_list, [ce], 0x12, offset_code, 0x00, 0x00, 0x00) # page 0
        if level == 1:
            self.set_feature(ch_list, [ce], 0x13, offset_code, 0x00, 0x00, 0x00) # page 1
        if level == 2:
            self.set_feature(ch_list, [ce], 0x12, 0x00, offset_code, 0x00, 0x00) # page 2
        if level == 3:
            self.set_feature(ch_list, [ce], 0x13, 0x00, offset_code, 0x00, 0x00) # page 1
        if level == 4:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, offset_code, 0x00) # page 0
        if level == 5:
            self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, offset_code, 0x00) # page 1
        if level == 6:
            self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, offset_code) # page 2

        # SLC
        self.set_feature(ch_list, [ce], 0x14, offset_code, 0x00, 0x00, 0x00)

        return True, offset_voltage

    def reset_read_offset_code(self, ch_list, ce, lun, level):
        self.set_feature(ch_list, [ce], 0x12, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x13, 0x00, 0x00, 0x00, 0x00)
        self.set_feature(ch_list, [ce], 0x14, 0x00, 0x00, 0x00, 0x00)
        return True
