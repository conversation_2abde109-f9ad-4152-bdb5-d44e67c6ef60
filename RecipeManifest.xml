<?xml version="1.0"?>
<Recipe xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="HS_V7.8.1_X36070_Debug_20250328" version="1.0" xmlns="http://n-plus-t.com/nano/2018/recipe">
  <PartNumberCompatibilityList>
    <PartNumbers>
      <PartNumber>5004</PartNumber>
    </PartNumbers>
  </PartNumberCompatibilityList>
  <TestLibrary source="TestLibrary.py">
    <Tests>
      <Test name="Initialize" category="Basic Functions">
        <Variables>
          <Variable name="DeviceName" type="choice" options="TLCDevice,QLCDevice,Dummy,HynixV5,HynixV6,HynixV7,KIOXIABiCS5,KIOXIABiCS5QLC,MicronB27A,MicronB27<PERSON>,MicronB37<PERSON>,MicronB<PERSON><PERSON>,<PERSON>isk<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>iCS5,<PERSON>iskBiCS6_512Gb,SandiskBiCS6_1Tb,SandiskBiCS8,ToshibaBiCS3,ToshibaBiCS4,ToshibaVHT,YMTCX19050,YMTCX29060,YMTCX39070,YMTCX36070,YMTCX49060,YMTCX26070" />
          <Variable name="StopAtFail" type="uint">0</Variable>
          <Variable name="EnableNfs" type="uint">1</Variable>
          <Variable name="FBCLimit" type="int">-1</Variable>
          <Variable name="PatternAlgo" type="choice" options="RANDOM,USER,ALL0,ALL1,ALT_AA_55,ALT_FF_00,INCREMENTAL,RANDOM_0">RANDOM</Variable>
          <Variable name="DisableSequenceRecording" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="DataLog" category="Basic Functions">
        <Variables>
          <Variable name="DeviceName" type="choice" options="TLCDevice,QLCDevice,Dummy,HynixV5,HynixV6,HynixV7,KIOXIABiCS5,KIOXIABiCS5QLC,MicronB27A,MicronB27B,MicronB37R,MicronB47R,SandiskBiCS4,SandiskBiCS5,SandiskBiCS6_512Gb,SandiskBiCS6_1Tb,SandiskBiCS8,ToshibaBiCS3,ToshibaBiCS4,ToshibaVHT,YMTCX19050,YMTCX29060,YMTCX39070,YMTCX36070,YMTCX49060,YMTCX26070" />
          <Variable name="StopAtFail" type="uint">0</Variable>
          <Variable name="EnableNfs" type="uint">1</Variable>
          <Variable name="FBCLimit" type="int">-1</Variable>
          <Variable name="PatternAlgo" type="choice" options="RANDOM,USER,ALL0,ALL1,ALT_AA_55,ALT_FF_00,INCREMENTAL,RANDOM_0">RANDOM</Variable>
          <Variable name="DisableSequenceRecording" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="DeviceConfig" category="Basic Functions">
        <Variables>
          <Variable name="ODT" type="uint">4</Variable>
          <Variable name="DriverStrength" type="uint">4</Variable>
          <Variable name="EnableSlcMode" type="uint">0</Variable>
          <Variable name="EnableAiprMode" type="uint">0</Variable>
          <Variable name="ReadLatencyCycles" type="uint">4</Variable>
          <Variable name="WriteLatencyCycles" type="uint">4</Variable>
        </Variables>
      </Test>
      <Test name="TurnOn_DeviceConfig" category="Basic Functions">
        <Variables>
          <Variable name="ODT" type="uint">4</Variable>
          <Variable name="DriverStrength" type="uint">4</Variable>
          <Variable name="EnableSlcMode" type="uint">0</Variable>
          <Variable name="EnableAiprMode" type="uint">0</Variable>
          <Variable name="ReadLatencyCycles" type="uint">4</Variable>
          <Variable name="WriteLatencyCycles" type="uint">4</Variable>
          <Variable name="VCC" type="double">2.5</Variable>
          <Variable name="VCCQ" type="double">1.2</Variable>
        </Variables>
      </Test>
      <Test name="TestLibraryConfig" category="Basic Functions">
        <Variables>
          <Variable name="StopAtFail" type="uint">0</Variable>
          <Variable name="FBCLimit" type="int">-1</Variable>
          <Variable name="PatternAlgo" type="choice" options="RANDOM,USER,ALL0,ALL1,ALT_AA_55,ALT_FF_00,INCREMENTAL,RANDOM_0">RANDOM</Variable>
        </Variables>
      </Test>
      <Test name="FpgaSetup" category="Hardware">
        <Variables>
          <Variable name="ReadLatencyCycles" type="uint">0</Variable>
          <Variable name="WriteLatencyCycles" type="uint">0</Variable>
          <Variable name="Chunks" type="uint">4</Variable>
        </Variables>
      </Test>
      <Test name="SelectGoodBlockList" category="Block Selection">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="FirstBlock" type="uint">0</Variable>
          <Variable name="BlockNumber" type="uint">10</Variable>
        </Variables>
      </Test>
      <Test name="SelectVirginBlockList" category="Block Selection">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="FirstBlock" type="uint">0</Variable>
          <Variable name="BlockNumber" type="uint">10</Variable>
        </Variables>
      </Test>
      <Test name="ClearBlockList" category="Block Selection">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP,ALL">ALL</Variable>
        </Variables>
      </Test>
      <Test name="EnableSequenceRecording" category="SequenceViewer">
        <Variables>
          <Variable name="Section Name" type="string">Capture</Variable>
        </Variables>
      </Test>
      <Test name="DisableSequenceRecording" category="SequenceViewer">
        <Variables />
      </Test>
      <Test name="DQCalib" category="Basic Functions">
        <Variables>
          <Variable name="DataRateMHz" type="uint">$MTs</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
        </Variables>
      </Test>
      <Test name="TurnOn" category="Basic Functions">
        <Variables>
          <Variable name="VCC" type="double">3.3</Variable>
          <Variable name="VCCQ" type="double">1.2</Variable>
        </Variables>
      </Test>
      <Test name="TurnOff" category="Basic Functions">
        <Variables />
      </Test>
      <Test name="DeviceIdentify" category="Basic Functions">
        <Variables />
      </Test>
      <Test name="SetPower" category="Basic Functions">
        <Variables>
          <Variable name="VCC" type="double">3.3</Variable>
          <Variable name="VCCQ" type="double">1.2</Variable>
          <Variable name="VPP" type="double">12.0</Variable>
        </Variables>
      </Test>
      <Test name="Reset" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="ResetCmd" type="uint">255</Variable>
        </Variables>
      </Test>
      <Test name="ZQCalibLong" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
        </Variables>
      </Test>
      <Test name="ZQCalibShort" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
        </Variables>
      </Test>
      <Test name="SetFeature" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="address" type="uint">0</Variable>
          <Variable name="p1" type="uint">0</Variable>
          <Variable name="p2" type="uint">0</Variable>
          <Variable name="p3" type="uint">0</Variable>
          <Variable name="p4" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="SetFeatureByLun" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="address" type="uint">0</Variable>
          <Variable name="p1" type="uint">0</Variable>
          <Variable name="p2" type="uint">0</Variable>
          <Variable name="p3" type="uint">0</Variable>
          <Variable name="p4" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="SetFeatureAsync" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="address" type="uint">0</Variable>
          <Variable name="p1" type="uint">0</Variable>
          <Variable name="p2" type="uint">0</Variable>
          <Variable name="p3" type="uint">0</Variable>
          <Variable name="p4" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="SetFeatureByLunAsync" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="address" type="uint">0</Variable>
          <Variable name="p1" type="uint">0</Variable>
          <Variable name="p2" type="uint">0</Variable>
          <Variable name="p3" type="uint">0</Variable>
          <Variable name="p4" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="GetFeature" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="address" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="GetFeatureByLun" category="Basic Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="address" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOut" category="Basic Functions">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="File" type="string" />
        </Variables>
      </Test>
      <Test name="OpenBlk_ReadOut" category="Basic Functions">
        <Variables>
          <Variable name="MP" type="string">MP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="File" type="string" />
        </Variables>
      </Test>
      <Test name="Erase" category="Basic Functions">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
        </Variables>
      </Test>
      <Test name="Erase_Hang" category="Basic Functions">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">MP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
        </Variables>
      </Test>
      <Test name="Program" category="Basic Functions">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="File" type="string" />
        </Variables>
      </Test>
      <Test name="MultiPlaneEP_Hang" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">0,1</Variable>
          <Variable name="Ces" type="string">0</Variable>
          <Variable name="Luns" type="string">0</Variable>
          <Variable name="Blocks" type="string">0</Variable>
          <Variable name="Pages" type="string">0</Variable>
          <Variable name="ByWL/Sawtooth_QLCProg" type="string">Sawtooth</Variable>
          <Variable name="Cycles" type="string">1</Variable>
          <Variable name="Commonisor" type="uint">1</Variable>
          <Variable name="NumBlocks" type="uint">1</Variable>
          <Variable name="BlockFreq" type="uint">30</Variable>
          <Variable name="LogModCyc" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="Cycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="Cycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="UseGoodBlockList" type="uint">1</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="GoodBlockCycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="GoodBlockNumber" type="uint">10</Variable>
          <Variable name="FirstGoodBlockIndex" type="uint">0</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="Cycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadDisturb" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="ReadCycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
        </Variables>
      </Test>
      <Test name="MultiPlaneCycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$BlocksMP</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="Cycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="MultiPlaneFastCycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">0,1</Variable>
          <Variable name="Ces" type="string">0</Variable>
          <Variable name="Luns" type="string">0</Variable>
          <Variable name="Blocks" type="string">0</Variable>
          <Variable name="Pages" type="string">0</Variable>
          <Variable name="ByWL/Sawtooth_QLCProg" type="string">Sawtooth</Variable>
          <Variable name="Cycles" type="string">1</Variable>
          <Variable name="Commonisor" type="uint">1</Variable>
          <Variable name="NumBlocks" type="uint">1</Variable>
          <Variable name="BlockFreq" type="uint">30</Variable>
          <Variable name="LogModCyc" type="uint">0</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="MultiPlaneDelayCycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">0,1</Variable>
          <Variable name="Ces" type="string">0</Variable>
          <Variable name="Luns" type="string">0</Variable>
          <Variable name="Blocks" type="string">0</Variable>
          <Variable name="Pages" type="string">0</Variable>
          <Variable name="Cycles" type="string">1</Variable>
          <Variable name="NumBlocks" type="uint">1</Variable>
          <Variable name="NlockFreq" type="uint">30</Variable>
          <Variable name="Endurancetime [sec]" type="uint">0</Variable>
          <Variable name="LogModCyc" type="uint">0</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="MultiPlaneGoodBlockCycling" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="GoodBlockNumber" type="uint">10</Variable>
          <Variable name="FirstGoodBlockIndex" type="uint">0</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="Cycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="MultiPlaneReadDisturb" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$BlocksMP</Variable>
          <Variable name="SP/MP_Retry" type="choice" options="SP,MP">MP</Variable>
          <Variable name="Retry_Blocks" type="string">$BlocksMP</Variable>
          <Variable name="Disturb_Pages" type="string">$Pages</Variable>
          <Variable name="Read_Pages" type="string">ALL</Variable>
          <Variable name="RDCycles" type="uint">1</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="BaseCycle" type="uint">1</Variable>
          <Variable name="RDType" type="choice" options="SPRD,BLKRD">SPRD</Variable>
          <Variable name="FileOptions" type="string" />
          <Variable name="Options" type="string">0-14</Variable>
        </Variables>
      </Test>
      <Test name="TempProfiler" category="Composite Functions">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Temps" type="string">40,80,125</Variable>
          <Variable name="Settle Time [sec]" type="uint">300</Variable>
        </Variables>
      </Test>
      <Test name="ReadRetry" category="VT">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="FileOptions" type="string" />
          <Variable name="Options" type="string">0-14</Variable>
        </Variables>
      </Test>
      <Test name="OpenBlk_ReadRetry" category="VT">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">MP</Variable>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="FileOptions" type="string" />
          <Variable name="Options" type="string">0-14</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_BER" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">8</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="ChartEnable" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffsetInCommand_BER" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">8</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="ChartEnable" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_BEST" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">4</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="LogDac" type="uint">0</Variable>
          <Variable name="LogFails" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_BEST_2" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">4</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="LogDac" type="uint">0</Variable>
          <Variable name="LogFails" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_Fitting_BEST" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="NumBlocks" type="int">$NumBlocks</Variable>
          <Variable name="BlockFreq" type="int">$BlockFreq</Variable>
          <Variable name="MaxPPPerCH" type="int">$MaxPPPerCH</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">4</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="LogDac" type="uint">0</Variable>
          <Variable name="LogFails" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_Vth" category="VT">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">4</Variable>
          <Variable name="ChartEnable" type="uint">1</Variable>
        </Variables>
      </Test>
      <Test name="Vth_Read" category="VT">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">MP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="WLs" type="string">$WLs</Variable>
          <Variable name="DacStart" type="int">0</Variable>
          <Variable name="DacEnd" type="int">256</Variable>
          <Variable name="DacStep" type="int">4</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffset_Vth_Dump" category="VT">
        <Variables>
          <Variable name="CH" type="string">0</Variable>
          <Variable name="Ce" type="string">0</Variable>
          <Variable name="Lun" type="string">0</Variable>
          <Variable name="Block" type="string">0</Variable>
          <Variable name="Pages" type="string">0,1,2</Variable>
          <Variable name="DacStart" type="int">-128</Variable>
          <Variable name="DacEnd" type="int">127</Variable>
          <Variable name="DacStep" type="int">8</Variable>
          <Variable name="ExpectedFile" type="string">Expected.bin</Variable>
          <Variable name="VTFile" type="string">VT.bin</Variable>
        </Variables>
      </Test>
      <Test name="ICC1-Read" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="ICC2-Program" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="ICC3-Erase" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="ICC4-Read" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="ICC4-Program" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="ICC5-Idle" category="Power Profile">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="IdleTime" type="uint">1000</Variable>
          <Variable name="I channel" type="choice" options="ICC,ICCQ,IPP">ICC</Variable>
          <Variable name="VPP" type="double">12</Variable>
        </Variables>
      </Test>
      <Test name="BadBlockTest" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
        </Variables>
      </Test>
      <Test name="ReadDump" category="Special">
        <Variables>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="File" type="string">DataOut.bin</Variable>
        </Variables>
      </Test>
      <Test name="ExpectedDump" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="File" type="string">Expected.bin</Variable>
        </Variables>
      </Test>
      <Test name="ReadOffsetDump" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
          <Variable name="DacStart" type="uint">-128</Variable>
          <Variable name="DacEnd" type="uint">127</Variable>
          <Variable name="VtStep" type="uint">1</Variable>
        </Variables>
      </Test>
      <Test name="FastRead" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">$Pages</Variable>
        </Variables>
      </Test>
      <Test name="EraseSuspend" category="Special">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="SuspendCmdDelay" type="uint">1000</Variable>
          <Variable name="SuspendTimes" type="uint">1</Variable>
          <Variable name="CHsRead" type="string">0,1</Variable>
          <Variable name="CesRead" type="string">0</Variable>
          <Variable name="LunsRead" type="string">0</Variable>
          <Variable name="BlocksRead" type="string">0</Variable>
          <Variable name="PagesRead" type="string">0</Variable>
        </Variables>
      </Test>
      <Test name="ProgramSuspend" category="Special">
        <Variables>
          <Variable name="SP/MP" type="choice" options="SP,MP">SP</Variable>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="SuspendPages" type="string">0</Variable>
          <Variable name="SuspendCmdDelay" type="uint">1000</Variable>
          <Variable name="SuspendTimes" type="uint">1</Variable>
          <Variable name="CHsRead" type="string">0</Variable>
          <Variable name="CesRead" type="string">0</Variable>
          <Variable name="LunsRead" type="string">0</Variable>
          <Variable name="BlocksRead" type="string">0</Variable>
          <Variable name="PagesRead" type="string">0</Variable>
        </Variables>
      </Test>
      <Test name="ScanFrequency" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="FreqMin" type="uint">400</Variable>
          <Variable name="FreqStep" type="uint">100</Variable>
          <Variable name="FreqMax" type="uint">1600</Variable>
        </Variables>
      </Test>
      <Test name="Delay" category="Special">
        <Variables>
          <Variable name="DD" type="uint">0</Variable>
          <Variable name="HH" type="uint">0</Variable>
          <Variable name="MM" type="uint">0</Variable>
          <Variable name="SS" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="DelayWithRead" category="Special">
        <Variables>
          <Variable name="DD" type="uint">0</Variable>
          <Variable name="HH" type="uint">0</Variable>
          <Variable name="MM" type="uint">0</Variable>
          <Variable name="SS" type="uint">0</Variable>
          <Variable name="timeToRead" type="uint">1</Variable>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Luns" type="string">$Luns</Variable>
          <Variable name="Blocks" type="string">$Blocks</Variable>
          <Variable name="Pages" type="string">ALL</Variable>
        </Variables>
      </Test>
      <Test name="PageBufferTest" category="Special">
        <Variables>
          <Variable name="CHs" type="string">$CHs</Variable>
          <Variable name="Ces" type="string">$Ces</Variable>
          <Variable name="Cycles" type="uint">1</Variable>
          <Variable name="ForceReWrite" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="Custom1" category="Special">
        <Variables />
      </Test>
      <Test name="Custom2" category="Special">
        <Variables />
      </Test>
      <Test name="TemperatureOn" category="Temperature">
        <Variables>
          <Variable name="Set Point" type="double">30</Variable>
          <Variable name="Settle Time [sec]" type="uint">0</Variable>
          <Variable name="Plot Refresh Time [sec]" type="uint">5</Variable>
        </Variables>
      </Test>
      <Test name="TemperatureOff" category="Temperature">
        <Variables>
          <Variable name="Settle Time [sec]" type="uint">0</Variable>
        </Variables>
      </Test>
      <Test name="DataRetention" category="Temperature">
        <Variables>
          <Variable name="Temp On Set Point" type="double">50</Variable>
          <Variable name="Temp On Settle Time [sec]" type="uint">0</Variable>
          <Variable name="Temp Off Set Point" type="double">25</Variable>
          <Variable name="Temp Off Settle Time [sec]" type="uint">0</Variable>
          <Variable name="Plot Refresh Time [sec]" type="uint">5</Variable>
        </Variables>
      </Test>
    </Tests>
  </TestLibrary>
  <TestProgram>
    <Description>NAND demo flow for NanoCycler.</Description>
    <GlobalVariables>
      <Variable name="MTs" type="uint">1200</Variable>
      <Variable name="CHs" type="string">0,1</Variable>
      <Variable name="Ces" type="string">0</Variable>
      <Variable name="Luns" type="string">0</Variable>
      <Variable name="Blocks" type="string">0</Variable>
      <Variable name="BlocksMP" type="string">0-19:4</Variable>
      <Variable name="Pages" type="string">ALL</Variable>
    </GlobalVariables>
    <Segments>
      <Segment name="Initialize" test="Initialize">
        <Variables>
          <Variable name="DeviceName" type="choice" options="TLCDevice,QLCDevice,Dummy,HynixV5,HynixV6,HynixV7,KIOXIABiCS5,KIOXIABiCS5QLC,MicronB27A,MicronB27B,MicronB37R,MicronB47R,SandiskBiCS4,SandiskBiCS5,SandiskBiCS6_512Gb,SandiskBiCS6_1Tb,SandiskBiCS8,ToshibaBiCS3,ToshibaBiCS4,ToshibaVHT,YMTCX19050,YMTCX29060,YMTCX39070,YMTCX36070,YMTCX49060,YMTCX26070">YMTCX36070</Variable>
          <Variable name="StopAtFail" type="uint">0</Variable>
          <Variable name="EnableNfs" type="uint">1</Variable>
          <Variable name="FBCLimit" type="int">-1</Variable>
          <Variable name="PatternAlgo" type="choice" options="RANDOM,USER,ALL0,ALL1,ALT_AA_55,ALT_FF_00,INCREMENTAL,RANDOM_0">RANDOM</Variable>
          <Variable name="DisableSequenceRecording" type="uint">0</Variable>
        </Variables>
      </Segment>
      <Segment name="DeviceConfig" test="DeviceConfig">
        <Variables>
          <Variable name="ODT" type="uint">4</Variable>
          <Variable name="DriverStrength" type="uint">2</Variable>
          <Variable name="EnableSlcMode" type="uint">0</Variable>
          <Variable name="EnableAiprMode" type="uint">0</Variable>
          <Variable name="ReadLatencyCycles" type="uint">4</Variable>
          <Variable name="WriteLatencyCycles" type="uint">4</Variable>
        </Variables>
      </Segment>
      <Segment name="DQCalib" test="DQCalib">
        <Variables>
          <Variable name="DataRateMHz" type="uint">1200</Variable>
          <Variable name="CHs" type="string">0,1</Variable>
          <Variable name="Ces" type="string">0,1</Variable>
        </Variables>
      </Segment>
      <Segment name="MultiPlaneFastCycling" test="MultiPlaneFastCycling">
        <Variables>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">0</Variable>
          <Variable name="Luns" type="string">0</Variable>
          <Variable name="Blocks" type="string">0-3</Variable>
          <Variable name="Pages" type="string">ALL</Variable>
          <Variable name="ByWL/Sawtooth_QLCProg" type="string">Sawtooth</Variable>
          <Variable name="Cycles" type="string">1</Variable>
          <Variable name="Commonisor" type="uint">1</Variable>
          <Variable name="NumBlocks" type="uint">1</Variable>
          <Variable name="BlockFreq" type="uint">30</Variable>
          <Variable name="LogModCyc" type="uint">1</Variable>
          <Variable name="EnableReadOut" type="uint">0</Variable>
          <Variable name="DwellTime(s)" type="uint">0</Variable>
          <Variable name="ClosePartialBlock" type="uint">0</Variable>
        </Variables>
      </Segment>
      <Segment name="ReadOffset_Fitting_BEST" test="ReadOffset_Fitting_BEST">
        <Variables>
          <Variable name="CHs" type="string">0</Variable>
          <Variable name="Ces" type="string">0</Variable>
          <Variable name="Luns" type="string">0</Variable>
          <Variable name="Blocks" type="string">[0-3]</Variable>
          <Variable name="NumBlocks" type="int">1</Variable>
          <Variable name="BlockFreq" type="int">1</Variable>
          <Variable name="MaxPPPerCH" type="int">5</Variable>
          <Variable name="Pages" type="string">[0-65]</Variable>
          <Variable name="DacStart" type="int">-4</Variable>
          <Variable name="DacEnd" type="int">4</Variable>
          <Variable name="DacStep" type="int">4</Variable>
          <Variable name="Pattern" type="choice" options="ALL1,ALL0,Expected">Expected</Variable>
          <Variable name="LogDac" type="uint">1</Variable>
          <Variable name="LogFails" type="uint">1</Variable>
        </Variables>
      </Segment>
    </Segments>
  </TestProgram>
</Recipe>