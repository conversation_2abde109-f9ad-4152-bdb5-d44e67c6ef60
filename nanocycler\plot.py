## @package nanocycler.plot
#  <summary> Contains the plot manager. </summary>
#
# @ingroup wsApiGroupPyLanguage

from .libmanager import *

_lib_handle = CLibManagerSingleton.load_libwslibpy()

_create = CLibManagerSingleton.load_func(_lib_handle, "p", "PlotApi_Create", "")
_delete = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_Delete", "p")
_open = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_Open", "pssss")
_openext = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_OpenExt", "pssisbsisbs")
_close = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_Close", "p")
_flush = CLibManagerSingleton.load_func(_lib_handle, "v", "Plot<PERSON><PERSON>_Flush", "p")
_isOpen = CLibManagerSingleton.load_func(_lib_handle, "b", "PlotApi_IsOpen", "p")
_add = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_Add", "pffs")
_addQualitative = CLibManagerSingleton.load_func(_lib_handle, "v", "PlotApi_AddQualitative", "psfs")


##<summary> Plot axis scale type.</summary>
#
# @ingroup wsApiGroup
class enumScaleType:
    ## <summary>	Specifies the Qualitative format - string format.</summary>
    Qualitative = 0
    ## <summary>	Specifies the Numerical format - numerical format.</summary>
    Numerical = 1
    ## <summary>	Specifies the DateTime format - date time format.</summary>
    DateTime = 2
    ## <summary>	Specifies the Auto format.</summary>
    Auto = 3


## <summary> The plot manager.
#						During test execution, the Test Library can generate Charts.
#						The Chart panel in the User Interface can be controlled from the test using the Plot interface.
#						Multiple Trend Chart can be created with different names; each chart can have multiple series.
#						For each chart it is possible to specify the title and axis names.
# 					</summary>
#
# @ingroup wsApiGroup
# @snippet std_examples.py Plot Example
class plot:
    # ! @cond Doxygen_Suppress
    def __init__(self):
        self.plotptr = _create()

    # ! @endcond

    ## <summary>	Cleanup function to unload the library from memory.</summary>
    def cleanup(self):
        if self.plotptr is not None:
            _delete(self.plotptr)

    ## <summary>	Opens the plot object.</summary>
    #
    # <param name="plotName">	The plotname.</param>
    # <param name="x_Header">	The x axis string.</param>
    # <param name="y_Header">	The y axis string.</param>
    # <param name="serie">	 	The serie.</param>
    def open(self, plotName: str, x_Header: str, y_Header: str, serie: str):
        _open(self.plotptr, plotName, x_Header, y_Header, serie)

    ## <summary>	Opens the plot object.</summary>
    #
    # <param name="plotName">	The plot name.</param>
    # <param name="x_header">	The x axis string.</param>
    # <param name="x_scaletype">	Set x axis scale type (0=Qualitative, 1=Numerical, 2=DateTime, 3=Auto).</param>
    # <param name="x_format">	Format string of x axis values. (applied only if x_isqualitative = false)
    # Number Examples
    # -------------------------------------------
    # #.00        => .10, .20, 3.10, 10.2 ....
    # #.#         => .1, .2, 3.1, 10.2 ....
    # #.#E+0.#    => 0E0, 2E-1, 1.0E1 ...
    # #.#e+0.#    => 0e0, 2e-1, 1.0e1 ...
    # n           => 1.02, 2.15, 3.53 ...
    # n0          => 1, 2, 3, 10 ....
    # n1          => 1.0, 2.1, 3.5 ....
    # n2          => 1.02, 2.15, 3.53 ....
    # e           => 1.000000-e001, 2.000000e001 ...
    # e1          => 1.0-e001, 2.0e001 ...
    # e2          => 1.09-e001, 2.05e001 ...
    # f           => 1.02, 2.15, 3.53 ...
    # f0          => 1, 2, 3, 10 ....
    # f1          => 1.0, 2.1, 3.5 ....
    #
    # Percent
    # -----------------
    # 0.00%       => 0.00%, 100.00%
    # 0%          => 0%, 100%
    #
    # DateTime formats not works
    # </param>
    # <param name="x_logaritmic">	Enable x axis logarithmic scale.</param>
    # <param name="y_header">	The y axis string.</param>
    # <param name="y_scaletype">	Set y axis scale type (0=Qualitative, 1=Numerical, 2=DateTime, 3=Auto).</param>
    # <param name="y_format"> Format string of y axis values	.
    # Number Examples
    # -------------------------------------------
    # #.00        => .10, .20, 3.10, 10.2 ....
    # #.#         => .1, .2, 3.1, 10.2 ....
    # #.#E+0.#    => 0E0, 2E-1, 1.0E1 ...
    # #.#e+0.#    => 0e0, 2e-1, 1.0e1 ...
    # n           => 1.02, 2.15, 3.53 ...
    # n0          => 1, 2, 3, 10 ....
    # n1          => 1.0, 2.1, 3.5 ....
    # n2          => 1.02, 2.15, 3.53 ....
    # e           => 1.000000-e001, 2.000000e001 ...
    # e1          => 1.0-e001, 2.0e001 ...
    # e2          => 1.09-e001, 2.05e001 ...
    # f           => 1.02, 2.15, 3.53 ...
    # f0          => 1, 2, 3, 10 ....
    # f1          => 1.0, 2.1, 3.5 ....
    #
    # Percent
    # -----------------
    # 0.00%       => 0.00%, 100.00%
    # 0%          => 0%, 100%
    #
    # DateTime formats not works
    # </param>
    # <param name="y_logaritmic">	Enable y axis logarithmic scale.</param>
    # <param name="serie">	 	The serie.</param>
    def openext(self, plotName: str, x_header: str, x_scaletype: enumScaleType, x_format: str, x_logaritmic: bool, y_header: str, y_scaletype: enumScaleType, y_format: str, y_logaritmic: bool, serie: str):
        _openext(self.plotptr, plotName, x_header, x_scaletype, x_format, int(x_logaritmic), y_header, y_scaletype, y_format, int(y_logaritmic), serie)

    ## <summary>	Close the Plot.</summary>
    def close(self):
        _close(self.plotptr)

    ## <summary>	Query if this plot is open.</summary>
    #
    # <returns>	true if open, false if not.</returns>
    def is_open(self):
        return bool(_isOpen(self.plotptr))

    ## <summary>	Flushes the point to plot.</summary>
    def flush(self):
        _flush(self.plotptr)

    ## <summary>	Adds point to plot.</summary>
    #
    # <param name="x">		The x coordinate.</param>
    # <param name="y">		The y coordinate.</param>
    # <param name="serie">	The serie.</param>
    def add(self, x: float, y: float, serie: str):
        _add(self.plotptr, x, y, serie)

    ## <summary>	Adds qualitative point to plot.</summary>
    #
    # <param name="x">		The x coordinate.</param>
    # <param name="y">		The y coordinate.</param>
    # <param name="serie">	The serie.</param>
    def add_qualitative(self, x: str, y: float, serie: str):
        _addQualitative(self.plotptr, x, y, serie)
