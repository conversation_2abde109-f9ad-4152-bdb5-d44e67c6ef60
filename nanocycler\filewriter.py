## @package nanocycler.filewriter
#  <summary> Contains the writer of a text file. </summary>
#
# @ingroup wsApiGroupPyLanguage

from .libmanager import *


## <summary>    The writer of text file.</summary>
#
# @ingroup wsApiGroup
# @snippet std_examples.py FileWriter Example
class filewriter:
    # !@cond Doxygen_Suppress
    def __init__(self):

        self._lib_handle = CLibManagerSingleton.load_libwslibpy()

        self._filewritercreate = CLibManagerSingleton.load_func(self._lib_handle, "p", "FileWriterApi_Create", "")
        self._filewriterdelete = CLibManagerSingleton.load_func(self._lib_handle, "v", "FileWriterApi_Delete", "p")
        self._filewriteropen = CLibManagerSingleton.load_func(self._lib_handle, "v", "FileWriterApi_Open", "ps")
        self._filewriterclose = CLibManagerSingleton.load_func(self._lib_handle, "v", "FileWriterApi_Close", "p")
        self._filewriterflush = CLibManagerSingleton.load_func(self._lib_handle, "v", "FileWriterApi_Flush", "p")
        self._filewriterisopen = CLibManagerSingleton.load_func(self._lib_handle, "b", "FileWriterApi_IsOpen", "p")
        self._filewriterwriteline = CLibManagerSingleton.load_func(self._lib_handle, "v", "FileWriterApi_WriteLine", "ps")

        self.fwptr = None

    # ! @endcond

    ## <summary>    Only for compatibility.</summary>
    def cleanup(self):
        return

    ## <summary>    Opens the given file name with exetension ( e.g Test.txt).
    #                   The file is created to temporary folder and copied at the end of test to Result folder
    #                   </summary>
    #
    # <param name="fileNameWithExt">    file name with extension.</param>
    def open(self, fileNameWithExt: str):
		# ! @cond Doxygen_Suppress
        self.fwptr = self._filewritercreate()
        if self.fwptr is not None:
            self._filewriteropen(self.fwptr, fileNameWithExt)
		# ! @endcond

    ## <summary>    Closes the file.</summary>
     
    def close(self):
		# ! @cond Doxygen_Suppress
        if self.fwptr is not None:
            self._filewriterclose(self.fwptr)
            self._filewriterdelete(self.fwptr)
            self.fwptr = None
		# ! @endcond

    ## <summary>    Flush the content on disk.</summary>
     
    def flush(self):
        if self.fwptr is not None:
            self._filewriterflush(self.fwptr)

    ## <summary>    Query if this file is open.</summary>
    #
    # <returns> true if open, false if not.</returns>
    def is_open(self):
        if self.fwptr is not None:
            return bool(self._filewriterisopen(self.fwptr))

        return False

    ## <summary>    Writes a line.</summary>
    #
    # <param name="text">   The text line.</param>
    def write_line(self, text: str):
        if self.fwptr is not None:
            self._filewriterwriteline(self.fwptr, text)
